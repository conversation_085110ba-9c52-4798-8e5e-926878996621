import { AxiosInstance } from 'axios';

import {
  CreateDepartmentInfoRequest,
  DepartmentInfoResponse,
  UpdateDepartmentInfoRequest,
} from '@/types/organization';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';

class DepartmentInfoService {
  private static instance: DepartmentInfoService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticUserService}/department-info`;
  }

  public static getInstance(): DepartmentInfoService {
    if (!DepartmentInfoService.instance) {
      DepartmentInfoService.instance = new DepartmentInfoService();
    }
    return DepartmentInfoService.instance;
  }

  async getDepartmentInfo(
    axiosInstance: AxiosInstance,
    agentSuiteKey: string
  ): Promise<DepartmentInfoResponse> {
    const response = await axiosInstance.get(
      `${this.BASE_URL}?agentSuiteKey=${agentSuiteKey}`
    );
    return response.data;
  }

  async createDepartmentInfo(
    axiosInstance: AxiosInstance,
    payload: CreateDepartmentInfoRequest
  ): Promise<DepartmentInfoResponse> {
    const response = await axiosInstance.post(`${this.BASE_URL}`, payload);
    return response.data;
  }

  async updateDepartmentInfo(
    axiosInstance: AxiosInstance,
    payload: UpdateDepartmentInfoRequest
  ): Promise<DepartmentInfoResponse> {
    const response = await axiosInstance.patch(`${this.BASE_URL}`, payload);
    return response.data;
  }

  async getDepartmentInfoByTenant(
    axiosInstance: AxiosInstance,
    tenantId: string,
    agentSuiteKey: string
  ): Promise<DepartmentInfoResponse> {
    const response = await axiosInstance.get(
      `${this.BASE_URL}/by-tenant?tenantId=${tenantId}&agentSuiteKey=${agentSuiteKey}`
    );
    return response.data;
  }
}

export default DepartmentInfoService.getInstance();

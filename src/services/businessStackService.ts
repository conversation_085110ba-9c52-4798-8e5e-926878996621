import { AxiosError } from 'axios';

import { useTenant } from '@/context/TenantContext';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import { integrationService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';

// TypeScript interfaces for Business Stack API responses
interface AppDetails {
  key: string;
  name: string;
  logo: string;
  enabled: boolean;
  authType: 'OAUTH2' | 'BASIC' | string; // Add other possible auth types
  appCategory:
    | 'EMAILING_SYSTEM'
    | 'DOCUMENT_REPO'
    | 'CRM_SYSTEM'
    | 'MESSAGING_SYSTEM'
    | string;
  uiHints: {
    shortDescription: string | null;
    longDescription: string | null;
    connectButtonText: string | null;
  };
  form: {
    submitButtonText: string;
    fields: FormField[];
  } | null;
  preAuth: {
    title: string;
    submitButtonText: string;
    fields: FormField[];
  } | null;
  connectionRequestFields: FormField[];
  isConnected: boolean;
}

interface FormField {
  key: string;
  label: string | null;
  placeholder: string | null;
  description: string | null;
  type: 'TEXT' | 'PASSWORD' | string; // Add other possible field types
  required: boolean;
  minLength: number | null;
  maxLength: number | null;
  options: any[] | null; // Replace 'any' with a more specific type if options have a known structure
  order: number | null;
}

interface AvailableAppsResponse {
  status: boolean;
  message: string;
  data: {
    availableApps: AppDetails[];
    total: number;
    page: number;
    pageSize: number;
  };
}

interface AppCategory {
  categoryName: string;
  categoryAlias: string;
}

interface AppCategoriesResponse {
  status: boolean;
  message: string;
  data: AppCategory[];
}

const useApi = () => {
  const { tenantId, activeAgent } = useTenant();
  const agentKey = activeAgent ?? '';

  return usePivotlPrivateRequest(BASE_URL, tenantId || '', agentKey);
};

// Hook for available apps API
export const useAvailableAppsApi = () => {
  const pivotlRequestRef = useApi();

  const getAvailableApps = async (params?: {
    page?: number;
    size?: number;
    search?: string;
    appCategory?: string;
  }): Promise<AvailableAppsResponse> => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }
      const { data } = await axiosInstance.get(
        `${integrationService}/available-apps`,
        {
          params: {
            page: params?.page || 1,
            pageSize: params?.size || 10,
            search: params?.search || '',
            appCategory: params?.appCategory || '',
          },
        }
      );
      return data || [];
    } catch (error) {
      const agentName =
        pivotlRequestRef.current?.defaults?.headers?.['X-Active-Agent'] ||
        'agent';
      console.error(`${agentName} Available Apps API Error:`, error);

      let errorMessage = `Failed to fetch available apps for ${agentName}. Please try again.`;

      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  };

  return getAvailableApps;
};

// Hook for app categories API
export const useAppCategoriesApi = () => {
  const pivotlRequestRef = useApi();

  const getAppCategories = async (): Promise<AppCategoriesResponse> => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }
      const { data } = await axiosInstance.get(
        `${integrationService}/available-apps/categories`
      );
      return data || { status: false, message: 'No data', data: [] };
    } catch (error) {
      const agentName =
        pivotlRequestRef.current?.defaults?.headers?.['X-Active-Agent'] ||
        'agent';
      console.error(`${agentName} App Categories API Error:`, error);

      let errorMessage = `Failed to fetch app categories for ${agentName}. Please try again.`;

      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  };

  return getAppCategories;
};

// Unified Connection API hooks
export const useUnifiedConnectionApi = () => {
  const pivotlRequestRef = useApi();

  const getConnectionUrl = async (payload: {
    appKey: string;
    params?: Record<string, string>;
  }): Promise<{
    status: boolean;
    message: string;
    data: {
      data: string;
      preAuth?: {
        title: string;
        submitButtonText: string;
        fields: FormField[];
      };
    };
  }> => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }
      const { data } = await axiosInstance.post(
        `${integrationService}/available-apps/connection-url`,
        payload
      );
      return data;
    } catch (error) {
      const agentName =
        pivotlRequestRef.current?.defaults?.headers?.['X-Active-Agent'] ||
        'agent';
      console.error(`${agentName} Get Connection URL API Error:`, error);

      let errorMessage = 'Failed to get connection URL. Please try again.';

      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  };

  const saveConnectionCredentials = async (payload: {
    appKey: string;
    connectionData: Record<string, string>;
  }): Promise<{ status: boolean; message: string; data?: any }> => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }
      const { data } = await axiosInstance.post(
        `${integrationService}/available-apps/save-connection-credentials`,
        payload
      );
      return data;
    } catch (error) {
      const agentName =
        pivotlRequestRef.current?.defaults?.headers?.['X-Active-Agent'] ||
        'agent';
      console.error(
        `${agentName} Save Connection Credentials API Error:`,
        error
      );

      let errorMessage =
        'Failed to save connection credentials. Please try again.';

      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  };

  const disconnectApp = async (payload: {
    appKey: string;
  }): Promise<{ status: boolean; message: string; data?: any }> => {
    try {
      const axiosInstance = pivotlRequestRef.current;
      if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
      }
      const { data } = await axiosInstance.post(
        `${integrationService}/available-apps/disconnect`,
        payload
      );
      return data;
    } catch (error) {
      const agentName =
        pivotlRequestRef.current?.defaults?.headers?.['X-Active-Agent'] ||
        'agent';
      console.error(`${agentName} Disconnect App API Error:`, error);

      let errorMessage = 'Failed to disconnect app. Please try again.';

      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  };

  return {
    getConnectionUrl,
    saveConnectionCredentials,
    disconnectApp,
  };
};

/**
 * Generates a secure session ID using Web Crypto API
 * @returns A 64-character hex string representing a secure session ID
 */
export const generateSecureSessionId = (): string => {
  // Generate 32 random bytes (256 bits) for strong security
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);

  // Convert to hex string
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

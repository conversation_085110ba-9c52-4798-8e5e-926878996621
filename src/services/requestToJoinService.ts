import { axiosInstance } from '@/helpers/axiosConfig';
import { MemberRole } from '@/types/members';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';

// ===== Types =====

export interface CreateJoinRequestPayload {
  agentSuiteKey: string;
  message: string;
  requestedRole: string;
}

export interface RejectJoinRequestPayload {
  rejectionReason: string;
}

export interface UpdateJoinRequestRolePayload {
  requestedRole: MemberRole;
}

export interface JoinRequestQueryParams {
  page: number;
  pageSize: number;
  status?: string;
}

export interface JoinRequest {
  id: string;
  requesterId: string;
  requesterName: string;
  requesterEmail: string;
  agentSuiteKey: string;
  tenantDomain: string;
  message: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  reviewerUserId: string | null;
  reviewerName: string | null;
  reviewedAt: string | null;
  rejectionReason: string | null;
  requestedRole: MemberRole;
}

export interface ApiStandardResponse<T = string> {
  status: boolean;
  message: string;
  data: T;
}

export interface ApiErrorResponse {
  status: boolean;
  message: string;
  data: {
    timestamp: string;
    details: string;
  };
}

export interface JoinRequestsListResponse {
  status: boolean;
  message: string;
  data: {
    requests: JoinRequest[];
    total: number;
    page: number;
    pageSize: number;
  };
}

// ===== Service =====

class RequestToJoinService {
  private static instance: RequestToJoinService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticUserService}/tenants`;
  }

  public static getInstance(): RequestToJoinService {
    if (!RequestToJoinService.instance) {
      RequestToJoinService.instance = new RequestToJoinService();
    }
    return RequestToJoinService.instance;
  }

  /**
   * Create a new join request
   * POST /tenants/suite-join-requests
   */
  async createJoinRequest(
    kcToken: string | undefined,
    payload: CreateJoinRequestPayload
  ): Promise<ApiStandardResponse> {
    const response = await axiosInstance.post(
      `${this.BASE_URL}/suite-join-requests`,
      payload,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  /**
   * Reject a join request
   * POST /tenants/suite-join-requests/{requestId}/reject
   */
  async rejectJoinRequest(
    kcToken: string | undefined,
    requestId: string,
    payload: RejectJoinRequestPayload
  ): Promise<ApiStandardResponse> {
    const response = await axiosInstance.post(
      `${this.BASE_URL}/suite-join-requests/${requestId}/reject`,
      payload,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  /**
   * Approve a join request
   * POST /tenants/suite-join-requests/{requestId}/approve
   */
  async approveJoinRequest(
    kcToken: string | undefined,
    requestId: string
  ): Promise<ApiStandardResponse> {
    const response = await axiosInstance.post(
      `${this.BASE_URL}/suite-join-requests/${requestId}/approve`,
      {},
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  /**
   * Get join requests for a specific suite
   * GET /tenants/suites/{agentSuiteKey}/join-requests
   */
  async getSuiteJoinRequests(
    kcToken: string | undefined,
    agentSuiteKey: string,
    params: JoinRequestQueryParams
  ): Promise<JoinRequestsListResponse> {
    const response = await axiosInstance.get(
      `${this.BASE_URL}/suites/${agentSuiteKey}/join-requests`,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
        params: {
          page: params.page,
          pageSize: params.pageSize,
          status: params.status,
        },
      }
    );
    return response.data;
  }

  /**
   * Get my join requests
   * GET /tenants/my-join-requests
   */
  async getMyJoinRequests(
    kcToken: string | undefined,
    params: JoinRequestQueryParams
  ): Promise<JoinRequestsListResponse> {
    const response = await axiosInstance.get(
      `${this.BASE_URL}/my-join-requests`,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
        params: {
          page: params.page,
          pageSize: params.pageSize,
          status: params.status,
        },
      }
    );
    return response.data;
  }

  /**
   * Update join request role
   * PATCH /tenants/suite-join-requests/{requestId}/role
   */
  async updateJoinRequestRole(
    kcToken: string | undefined,
    requestId: string,
    payload: UpdateJoinRequestRolePayload
  ): Promise<ApiStandardResponse> {
    const response = await axiosInstance.patch(
      `${this.BASE_URL}/suite-join-requests/${requestId}/role`,
      payload,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }

  /**
   * Delete a join request
   * DELETE /tenants/suite-join-requests/{requestId}
   */
  async deleteJoinRequest(
    kcToken: string | undefined,
    requestId: string
  ): Promise<ApiStandardResponse> {
    const response = await axiosInstance.delete(
      `${this.BASE_URL}/suite-join-requests/${requestId}`,
      {
        headers: { Authorization: `Bearer ${kcToken}` },
      }
    );
    return response.data;
  }
}

export default RequestToJoinService.getInstance();

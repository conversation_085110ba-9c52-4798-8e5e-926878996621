import { AxiosInstance } from 'axios';

import {
  CompanyInfoResponse,
  CreateCompanyInfoRequest,
  UpdateCompanyInfoRequest,
} from '@/types/organization';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';

class CompanyInfoService {
  private static instance: CompanyInfoService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticUserService}/company-info`;
  }

  public static getInstance(): CompanyInfoService {
    if (!CompanyInfoService.instance) {
      CompanyInfoService.instance = new CompanyInfoService();
    }
    return CompanyInfoService.instance;
  }

  async getCompanyInfo(
    axiosInstance: AxiosInstance,
    agentSuiteKey: string
  ): Promise<CompanyInfoResponse> {
    const response = await axiosInstance.get(
      `${this.BASE_URL}?agentSuiteKey=${agentSuiteKey}`
    );
    return response.data;
  }

  async createCompanyInfo(
    axiosInstance: AxiosInstance,
    payload: CreateCompanyInfoRequest
  ): Promise<CompanyInfoResponse> {
    const response = await axiosInstance.post(`${this.BASE_URL}`, payload);
    return response.data;
  }

  async updateCompanyInfo(
    axiosInstance: AxiosInstance,
    payload: UpdateCompanyInfoRequest
  ): Promise<CompanyInfoResponse> {
    const response = await axiosInstance.patch(`${this.BASE_URL}`, payload);
    return response.data;
  }

  async getCompanyInfoByTenant(
    axiosInstance: AxiosInstance,
    tenantId: string,
    agentSuiteKey: string
  ): Promise<CompanyInfoResponse> {
    const response = await axiosInstance.get(
      `${this.BASE_URL}/by-tenant?tenantId=${tenantId}&agentSuiteKey=${agentSuiteKey}`
    );
    return response.data;
  }
}

export default CompanyInfoService.getInstance();

import { AxiosInstance } from 'axios';

import { CountriesWithStatesResponse } from '@/types/locations';
import { agenticUserService } from '@/utils/apiServiceControllersRoute';

class LocationsService {
  private static instance: LocationsService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticUserService}/locations`;
  }

  public static getInstance(): LocationsService {
    if (!LocationsService.instance) {
      LocationsService.instance = new LocationsService();
    }
    return LocationsService.instance;
  }

  async getCountriesWithStates(
    axiosInstance: AxiosInstance,
    params?: {
      search?: string;
      page?: number;
      pageSize?: number;
    }
  ): Promise<CountriesWithStatesResponse> {
    const response = await axiosInstance.get<CountriesWithStatesResponse>(
      `${this.BASE_URL}/countries-with-states`,
      {
        params: {
          search: params?.search || '',
          page: params?.page || 0,
          pageSize: params?.pageSize || 20,
        },
      }
    );
    return response.data;
  }
}

export default LocationsService.getInstance();

/**
 * Heartbeat Service
 *
 * This service handles all API calls related to agent heartbeat functionality,
 * including fetching heartbeat status, initializing, and pausing heartbeats.
 */

import { useCallback } from 'react';

import { useTenant } from '@/context/TenantContext';
import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import type {
  HeartbeatActionResponse,
  HeartbeatListResponse,
} from '@/types/heartbeat';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import { BASE_URL } from '@/utils/apiUrls';
import { extractErrorMessage } from '@/utils/errorUtils';

/**
 * Hook for heartbeat API operations
 * Uses the current active tenant for API calls
 */
export const useHeartbeatApi = () => {
  const { activeAgent, tenantId } = useTenant();

  // Create a base axios instance for general operations (no specific agent)
  const baseAxiosInstance = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || '',
    '' // Don't set a default agent for the base instance
  );

  /**
   * Fetch heartbeat status for all agents
   * GET /agentous-agentic-service/ai-agents/heartbeats
   */
  const fetchAllHeartbeats =
    useCallback(async (): Promise<HeartbeatListResponse> => {
      try {
        const response = await baseAxiosInstance.current?.get(
          `${agenticService}/ai-agents/heartbeats`,
          {
            headers: {
              'x-active-agent': activeAgent || '',
            },
          }
        );

        return (
          response?.data || {
            status: false,
            message: 'No data received',
            data: [],
          }
        );
      } catch (error) {
        console.error('Fetch heartbeats API error:', error);
        throw new Error(extractErrorMessage(error));
      }
    }, [baseAxiosInstance]);

  /**
   * Initialize heartbeat for a specific agent
   * POST /agentous-agentic-service/ai-agents/initialize-heartbeat
   *
   * Note: This function sets the x-active-agent header for the target agent
   */
  const initializeHeartbeat = useCallback(
    async (targetAgentKey: string): Promise<HeartbeatActionResponse> => {
      try {
        // Ensure we have a valid target agent key
        if (!targetAgentKey || targetAgentKey.trim() === '') {
          throw new Error('Target agent key is required');
        }

        const response = await baseAxiosInstance.current?.post(
          `${agenticService}/ai-agents/initialize-heartbeat`,
          {},
          {
            headers: {
              'x-active-agent': targetAgentKey,
            },
          }
        );

        return (
          response?.data || {
            status: false,
            message: 'No data received',
            data: null,
          }
        );
      } catch (error) {
        console.error('Initialize heartbeat API error:', error);
        throw new Error(extractErrorMessage(error));
      }
    },
    [baseAxiosInstance]
  );

  /**
   * Pause heartbeat for a specific agent
   * PATCH /agentous-agentic-service/ai-agents/pause-heartbeat
   *
   * Note: This function sets the x-active-agent header for the target agent
   */
  const pauseHeartbeat = useCallback(
    async (targetAgentKey: string): Promise<HeartbeatActionResponse> => {
      try {
        // Ensure we have a valid target agent key
        if (!targetAgentKey || targetAgentKey.trim() === '') {
          throw new Error('Target agent key is required');
        }

        const response = await baseAxiosInstance.current?.patch(
          `${agenticService}/ai-agents/pause-heartbeat`,
          {},
          {
            headers: {
              'x-active-agent': targetAgentKey,
            },
          }
        );

        return (
          response?.data || {
            status: false,
            message: 'No data received',
            data: null,
          }
        );
      } catch (error) {
        console.error('Pause heartbeat API error:', error);
        throw new Error(extractErrorMessage(error));
      }
    },
    [baseAxiosInstance]
  );

  return {
    fetchAllHeartbeats,
    initializeHeartbeat,
    pauseHeartbeat,
  };
};

/**
 * Custom hook for managing heartbeat operations
 * Provides a higher-level interface for heartbeat functionality
 */
export const useHeartbeatOperations = () => {
  const {
    fetchAllHeartbeats,
    initializeHeartbeat: apiInitializeHeartbeat,
    pauseHeartbeat: apiPauseHeartbeat,
  } = useHeartbeatApi();

  /**
   * Initialize heartbeat for a specific agent
   */
  const initializeHeartbeat = useCallback(
    async (agentKey: string): Promise<void> => {
      const result = await apiInitializeHeartbeat(agentKey);

      if (!result.status) {
        throw new Error(result.message || 'Failed to initialize heartbeat');
      }
    },
    [apiInitializeHeartbeat]
  );

  /**
   * Pause heartbeat for a specific agent
   */
  const pauseHeartbeat = useCallback(
    async (agentKey: string): Promise<void> => {
      const result = await apiPauseHeartbeat(agentKey);

      if (!result.status) {
        throw new Error(result.message || 'Failed to pause heartbeat');
      }
    },
    [apiPauseHeartbeat]
  );

  return {
    fetchAllHeartbeats,
    initializeHeartbeat,
    pauseHeartbeat,
  };
};

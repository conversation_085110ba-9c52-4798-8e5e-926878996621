import { SVGProps } from 'react';

export const Icons = {
  OutlineTransaction: (props: SVGProps<SVGSVGElement>) => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path d="M13 2H3C2.73478 2 2.48043 2.10536 2.29289 2.29289C2.10536 2.48043 2 2.73478 2 3V13C2 13.2652 2.10536 13.5196 2.29289 13.7071C2.48043 13.8946 2.73478 14 3 14H13C13.2652 14 13.5196 13.8946 13.7071 13.7071C13.8946 13.5196 14 13.2652 14 13V3C14 2.73478 13.8946 2.48043 13.7071 2.29289C13.5196 2.10536 13.2652 2 13 2Z" stroke="#4F4F4F" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M6.99935 10.3333L8.66602 11.6667L11.3327 8.33333M4.66602 5H11.3327M4.66602 7.66667H7.33268" stroke="#4F4F4F" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
  ),
  StatusFilled: (props: SVGProps<SVGSVGElement>) => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path d="M11.159 1.65901C11.581 1.23705 12.1533 1 12.75 1C13.3467 1 13.919 1.23705 14.341 1.65901C14.763 2.08097 15 2.65327 15 3.25001C15 3.84675 14.763 4.41905 14.341 4.84101L10.011 9.17101C9.8314 9.35067 9.60912 9.48176 9.365 9.55201L6.1385 10.4805C6.05267 10.5052 5.96178 10.5065 5.87528 10.4842C5.78879 10.4619 5.70984 10.4169 5.64666 10.3538C5.58347 10.2906 5.53835 10.2117 5.51599 10.1252C5.49363 10.0388 5.49484 9.94786 5.5195 9.86201L6.448 6.63501C6.51825 6.39089 6.64935 6.16861 6.829 5.98901L11.159 1.65901ZM8 3.00001C8.24033 3.00001 8.47617 3.01668 8.7075 3.05001L9.554 2.20301C9.0471 2.0677 8.52465 1.99945 8 2.00001C4.6865 2.00001 2 4.68651 2 8.00001C2 11.3135 4.6865 14 8 14C11.3135 14 14 11.3135 14 8.00001C14 7.46251 13.9295 6.94151 13.7965 6.44601L12.9505 7.29251C12.9832 7.52385 12.9997 7.75968 13 8.00001C13 10.7615 10.7615 13 8 13C5.2385 13 3 10.7615 3 8.00001C3 5.23851 5.2385 3.00001 8 3.00001Z" fill="#4F4F4F"/>
</svg>
  ),
  PDF: (props: SVGProps<SVGSVGElement>) => (
    <svg width="22" height="26" viewBox="0 0 22 26" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g clipPath="url(#clip0_17620_40002)">
    <path d="M1 0.25H16.5859C16.7848 0.25004 16.9756 0.32911 17.1162 0.469727L21.5303 4.88379C21.6709 5.02441 21.75 5.2152 21.75 5.41406V25C21.75 25.4142 21.4142 25.75 21 25.75H1C0.585787 25.75 0.25 25.4142 0.25 25V1L0.253906 0.922852C0.292528 0.544882 0.611834 0.25 1 0.25Z" fill="white" stroke="#D9D9DA" strokeWidth="0.5"/>
    <mask id="path-2-inside-1_17620_40002" fill="white">
    <path d="M16.5859 0C16.8511 3.98969e-05 17.1055 0.105468 17.293 0.292969L21.707 4.70703C21.8945 4.89453 22 5.1489 22 5.41406V6H17C16.4477 6 16 5.55228 16 5V0H16.5859Z"/>
    </mask>
    <path d="M16.5859 0C16.8511 3.98969e-05 17.1055 0.105468 17.293 0.292969L21.707 4.70703C21.8945 4.89453 22 5.1489 22 5.41406V6H17C16.4477 6 16 5.55228 16 5V0H16.5859Z" fill="#EFEFEF"/>
    <path d="M16.5859 0L16.586 -0.5H16.5859V0ZM17.293 0.292969L16.9394 0.646522L16.9394 0.646522L17.293 0.292969ZM22 5.41406H22.5V5.41399L22 5.41406ZM22 6V6.5H22.5V6H22ZM16 0V-0.5H15.5V0H16ZM16.5859 0L16.5859 0.5C16.7184 0.50002 16.8456 0.552732 16.9394 0.646522L17.293 0.292969L17.6465 -0.0605845C17.3653 -0.341796 16.9838 -0.49994 16.586 -0.5L16.5859 0ZM17.293 0.292969L16.9394 0.646522L21.3535 5.06058L21.707 4.70703L22.0606 4.35348L17.6465 -0.0605846L17.293 0.292969ZM21.707 4.70703L21.3535 5.06058C21.4473 5.15438 21.5 5.28161 21.5 5.41414L22 5.41406L22.5 5.41399C22.4999 5.01619 22.3418 4.63469 22.0606 4.35348L21.707 4.70703ZM22 5.41406H21.5V6H22H22.5V5.41406H22ZM22 6V5.5H17V6V6.5H22V6ZM17 6V5.5C16.7239 5.5 16.5 5.27614 16.5 5H16H15.5C15.5 5.82843 16.1716 6.5 17 6.5V6ZM16 5H16.5V0H16H15.5V5H16ZM16 0V0.5H16.5859V0V-0.5H16V0Z" fill="#D9D9DA" mask="url(#path-2-inside-1_17620_40002)"/>
    <path d="M5.66035 17.6364V11.6364H6.30808V12.3295H6.38763C6.43687 12.2538 6.50505 12.1572 6.59217 12.0398C6.68119 11.9205 6.80808 11.8144 6.97285 11.7216C7.13952 11.6269 7.3649 11.5795 7.64899 11.5795C8.01642 11.5795 8.34028 11.6714 8.62058 11.8551C8.90089 12.0388 9.11964 12.2992 9.27683 12.6364C9.43403 12.9735 9.51263 13.3712 9.51263 13.8295C9.51263 14.2917 9.43403 14.6922 9.27683 15.0312C9.11964 15.3684 8.90183 15.6297 8.62342 15.8153C8.34501 15.9991 8.02399 16.0909 7.66036 16.0909C7.38005 16.0909 7.15562 16.0445 6.98706 15.9517C6.8185 15.857 6.68876 15.75 6.59785 15.6307C6.50695 15.5095 6.43687 15.4091 6.38763 15.3295H6.33081V17.6364H5.66035ZM6.31945 13.8182C6.31945 14.1477 6.36774 14.4384 6.46433 14.6903C6.56092 14.9403 6.70202 15.1364 6.88763 15.2784C7.07323 15.4186 7.30051 15.4886 7.56945 15.4886C7.84975 15.4886 8.08365 15.4148 8.27115 15.267C8.46054 15.1174 8.60259 14.9167 8.69729 14.6648C8.79388 14.411 8.84217 14.1288 8.84217 13.8182C8.84217 13.5114 8.79482 13.2348 8.70013 12.9886C8.60732 12.7405 8.46623 12.5445 8.27683 12.4006C8.08933 12.2547 7.85354 12.1818 7.56945 12.1818C7.29672 12.1818 7.06755 12.2509 6.88195 12.3892C6.69634 12.5256 6.55619 12.7169 6.46149 12.9631C6.36679 13.2074 6.31945 13.4924 6.31945 13.8182ZM11.7731 16.0909C11.4094 16.0909 11.0884 15.9991 10.81 15.8153C10.5316 15.6297 10.3138 15.3684 10.1566 15.0312C9.99941 14.6922 9.92081 14.2917 9.92081 13.8295C9.92081 13.3712 9.99941 12.9735 10.1566 12.6364C10.3138 12.2992 10.5326 12.0388 10.8129 11.8551C11.0932 11.6714 11.417 11.5795 11.7844 11.5795C12.0685 11.5795 12.293 11.6269 12.4577 11.7216C12.6244 11.8144 12.7513 11.9205 12.8384 12.0398C12.9274 12.1572 12.9966 12.2538 13.0458 12.3295H13.1026V10.1818H13.7731V16H13.1254V15.3295H13.0458C12.9966 15.4091 12.9265 15.5095 12.8356 15.6307C12.7447 15.75 12.6149 15.857 12.4464 15.9517C12.2778 16.0445 12.0534 16.0909 11.7731 16.0909ZM11.864 15.4886C12.1329 15.4886 12.3602 15.4186 12.5458 15.2784C12.7314 15.1364 12.8725 14.9403 12.9691 14.6903C13.0657 14.4384 13.114 14.1477 13.114 13.8182C13.114 13.4924 13.0666 13.2074 12.9719 12.9631C12.8772 12.7169 12.7371 12.5256 12.5515 12.3892C12.3659 12.2509 12.1367 12.1818 11.864 12.1818C11.5799 12.1818 11.3432 12.2547 11.1538 12.4006C10.9663 12.5445 10.8252 12.7405 10.7305 12.9886C10.6377 13.2348 10.5913 13.5114 10.5913 13.8182C10.5913 14.1288 10.6386 14.411 10.7333 14.6648C10.8299 14.9167 10.9719 15.1174 11.1594 15.267C11.3488 15.4148 11.5837 15.4886 11.864 15.4886ZM16.6273 11.6364V12.2045H14.275V11.6364H16.6273ZM14.9796 16V11.0341C14.9796 10.7841 15.0383 10.5758 15.1557 10.4091C15.2731 10.2424 15.4256 10.1174 15.6131 10.0341C15.8006 9.95076 15.9985 9.90909 16.2068 9.90909C16.3716 9.90909 16.5061 9.92235 16.6102 9.94886C16.7144 9.97538 16.7921 10 16.8432 10.0227L16.65 10.6023C16.6159 10.5909 16.5686 10.5767 16.508 10.5597C16.4493 10.5426 16.3716 10.5341 16.275 10.5341C16.0534 10.5341 15.8934 10.59 15.7949 10.7017C15.6983 10.8134 15.65 10.9773 15.65 11.1932V16H14.9796Z" fill="#EB362B"/>
    </g>
    <defs>
    <clipPath id="clip0_17620_40002">
    <rect width="22" height="26" fill="white"/>
    </clipPath>
    </defs>
    </svg>
  ),
  DataDate: (props: SVGProps<SVGSVGElement>) => (
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path d="M10.5 7.25C10.3674 7.25 10.2402 7.30268 10.1464 7.39645C10.0527 7.49021 10 7.61739 10 7.75C10 7.88261 10.0527 8.00979 10.1464 8.10355C10.2402 8.19732 10.3674 8.25 10.5 8.25H13.5C13.6326 8.25 13.7598 8.19732 13.8536 8.10355C13.9473 8.00979 14 7.88261 14 7.75C14 7.61739 13.9473 7.49021 13.8536 7.39645C13.7598 7.30268 13.6326 7.25 13.5 7.25H10.5ZM9.5 12.75C9.5 13.0152 9.39464 13.2696 9.20711 13.4571C9.01957 13.6446 8.76522 13.75 8.5 13.75C8.23478 13.75 7.98043 13.6446 7.79289 13.4571C7.60536 13.2696 7.5 13.0152 7.5 12.75C7.5 12.4848 7.60536 12.2304 7.79289 12.0429C7.98043 11.8554 8.23478 11.75 8.5 11.75C8.76522 11.75 9.01957 11.8554 9.20711 12.0429C9.39464 12.2304 9.5 12.4848 9.5 12.75ZM9.5 16.25C9.5 16.5152 9.39464 16.7696 9.20711 16.9571C9.01957 17.1446 8.76522 17.25 8.5 17.25C8.23478 17.25 7.98043 17.1446 7.79289 16.9571C7.60536 16.7696 7.5 16.5152 7.5 16.25C7.5 15.9848 7.60536 15.7304 7.79289 15.5429C7.98043 15.3554 8.23478 15.25 8.5 15.25C8.76522 15.25 9.01957 15.3554 9.20711 15.5429C9.39464 15.7304 9.5 15.9848 9.5 16.25ZM12 13.75C12.2652 13.75 12.5196 13.6446 12.7071 13.4571C12.8946 13.2696 13 13.0152 13 12.75C13 12.4848 12.8946 12.2304 12.7071 12.0429C12.5196 11.8554 12.2652 11.75 12 11.75C11.7348 11.75 11.4804 11.8554 11.2929 12.0429C11.1054 12.2304 11 12.4848 11 12.75C11 13.0152 11.1054 13.2696 11.2929 13.4571C11.4804 13.6446 11.7348 13.75 12 13.75ZM13 16.25C13 16.5152 12.8946 16.7696 12.7071 16.9571C12.5196 17.1446 12.2652 17.25 12 17.25C11.7348 17.25 11.4804 17.1446 11.2929 16.9571C11.1054 16.7696 11 16.5152 11 16.25C11 15.9848 11.1054 15.7304 11.2929 15.5429C11.4804 15.3554 11.7348 15.25 12 15.25C12.2652 15.25 12.5196 15.3554 12.7071 15.5429C12.8946 15.7304 13 15.9848 13 16.25ZM15.5 13.75C15.7652 13.75 16.0196 13.6446 16.2071 13.4571C16.3946 13.2696 16.5 13.0152 16.5 12.75C16.5 12.4848 16.3946 12.2304 16.2071 12.0429C16.0196 11.8554 15.7652 11.75 15.5 11.75C15.2348 11.75 14.9804 11.8554 14.7929 12.0429C14.6054 12.2304 14.5 12.4848 14.5 12.75C14.5 13.0152 14.6054 13.2696 14.7929 13.4571C14.9804 13.6446 15.2348 13.75 15.5 13.75Z" fill="#2F2F2F"/>
<path fillRule="evenodd" clipRule="evenodd" d="M8 3.5C8.13261 3.5 8.25979 3.55268 8.35355 3.64645C8.44732 3.74021 8.5 3.86739 8.5 4V5H15.5V4C15.5 3.86739 15.5527 3.74021 15.6464 3.64645C15.7402 3.55268 15.8674 3.5 16 3.5C16.1326 3.5 16.2598 3.55268 16.3536 3.64645C16.4473 3.74021 16.5 3.86739 16.5 4V5.003C16.7447 5.005 16.9627 5.01367 17.154 5.029C17.519 5.059 17.839 5.122 18.135 5.272C18.6053 5.51189 18.9875 5.89451 19.227 6.365C19.378 6.661 19.441 6.981 19.471 7.345C19.5 7.7 19.5 8.137 19.5 8.679V16.321C19.5 16.863 19.5 17.301 19.471 17.654C19.441 18.019 19.378 18.339 19.227 18.635C18.9874 19.1051 18.6051 19.4874 18.135 19.727C17.839 19.878 17.519 19.941 17.155 19.971C16.8 20 16.363 20 15.822 20H8.179C7.637 20 7.199 20 6.846 19.971C6.481 19.941 6.161 19.878 5.865 19.727C5.39451 19.4875 5.01189 19.1053 4.772 18.635C4.622 18.339 4.559 18.019 4.529 17.655C4.5 17.3 4.5 16.862 4.5 16.32V8.68C4.5 8.205 4.5 7.812 4.52 7.483L4.529 7.347C4.559 6.982 4.622 6.662 4.772 6.366C5.01173 5.89535 5.39435 5.51272 5.865 5.273C6.161 5.123 6.481 5.06 6.845 5.03C7.03767 5.01467 7.256 5.006 7.5 5.004V4C7.5 3.86739 7.55268 3.74021 7.64645 3.64645C7.74021 3.55268 7.86739 3.5 8 3.5ZM7.5 6.5V6.003C7.3088 6.00458 7.11771 6.01225 6.927 6.026C6.625 6.05 6.451 6.096 6.319 6.163C6.03651 6.30685 5.80685 6.53651 5.663 6.819C5.596 6.951 5.55 7.125 5.526 7.427C5.5 7.736 5.5 8.132 5.5 8.7V9.25H18.5V8.7C18.5 8.132 18.5 7.736 18.474 7.427C18.45 7.125 18.404 6.951 18.337 6.819C18.1931 6.53651 17.9635 6.30685 17.681 6.163C17.549 6.096 17.375 6.05 17.073 6.026C16.8823 6.01225 16.6912 6.00458 16.5 6.003V6.5C16.5 6.63261 16.4473 6.75979 16.3536 6.85355C16.2598 6.94732 16.1326 7 16 7C15.8674 7 15.7402 6.94732 15.6464 6.85355C15.5527 6.75979 15.5 6.63261 15.5 6.5V6H8.5V6.5C8.5 6.63261 8.44732 6.75979 8.35355 6.85355C8.25979 6.94732 8.13261 7 8 7C7.86739 7 7.74021 6.94732 7.64645 6.85355C7.55268 6.75979 7.5 6.63261 7.5 6.5ZM18.5 10.25H5.5V16.3C5.5 16.868 5.5 17.265 5.526 17.573C5.55 17.875 5.596 18.049 5.663 18.181C5.80685 18.4635 6.03651 18.6931 6.319 18.837C6.451 18.904 6.625 18.95 6.927 18.974C7.236 19 7.632 19 8.2 19H15.8C16.368 19 16.765 19 17.073 18.974C17.375 18.95 17.549 18.904 17.681 18.837C17.9635 18.6931 18.1931 18.4635 18.337 18.181C18.404 18.049 18.45 17.875 18.474 17.573C18.5 17.265 18.5 16.868 18.5 16.3V10.25Z" fill="#2F2F2F"/>
</svg>

  ),
  Building: (props: SVGProps<SVGSVGElement>) => ( 
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path d="M15.0013 12.5H13.3346V14.1667H15.0013M15.0013 9.16667H13.3346V10.8333H15.0013M16.668 15.8333H10.0013V14.1667H11.668V12.5H10.0013V10.8333H11.668V9.16667H10.0013V7.5H16.668M8.33464 5.83333H6.66797V4.16667H8.33464M8.33464 9.16667H6.66797V7.5H8.33464M8.33464 12.5H6.66797V10.8333H8.33464M8.33464 15.8333H6.66797V14.1667H8.33464M5.0013 5.83333H3.33464V4.16667H5.0013M5.0013 9.16667H3.33464V7.5H5.0013M5.0013 12.5H3.33464V10.8333H5.0013M5.0013 15.8333H3.33464V14.1667H5.0013M10.0013 5.83333V2.5H1.66797V17.5H18.3346V5.83333H10.0013Z" fill="#2F2F2F"/>
</svg>

  ),
  Profile: (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path d="M3.33203 15.0013C3.33203 14.1172 3.68322 13.2694 4.30834 12.6443C4.93346 12.0192 5.78131 11.668 6.66536 11.668H13.332C14.2161 11.668 15.0639 12.0192 15.6891 12.6443C16.3142 13.2694 16.6654 14.1172 16.6654 15.0013C16.6654 15.4433 16.4898 15.8673 16.1772 16.1798C15.8646 16.4924 15.4407 16.668 14.9987 16.668H4.9987C4.55667 16.668 4.13275 16.4924 3.82019 16.1798C3.50763 15.8673 3.33203 15.4433 3.33203 15.0013Z" stroke="#2F2F2F" strokeWidth="1.66667" strokeLinejoin="round"/>
<path d="M10 8.33398C11.3807 8.33398 12.5 7.2147 12.5 5.83398C12.5 4.45327 11.3807 3.33398 10 3.33398C8.61929 3.33398 7.5 4.45327 7.5 5.83398C7.5 7.2147 8.61929 8.33398 10 8.33398Z" stroke="#2F2F2F" strokeWidth="1.66667"/>
</svg>

  ),
  Hierarchy: (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path fillRule="evenodd" clipRule="evenodd" d="M12.5008 5.0007C12.5011 5.51791 12.341 6.02247 12.0425 6.44485C11.744 6.86723 11.3218 7.18663 10.8342 7.35904V9.16737H13.3342C13.9972 9.16737 14.6331 9.43076 15.1019 9.8996C15.5708 10.3684 15.8342 11.0043 15.8342 11.6674V12.6424C16.3904 12.839 16.8593 13.2259 17.1578 13.7347C17.4564 14.2435 17.5654 14.8416 17.4657 15.423C17.366 16.0045 17.0639 16.532 16.6128 16.9123C16.1618 17.2925 15.5908 17.5011 15.0008 17.5011C14.4109 17.5011 13.8399 17.2925 13.3889 16.9123C12.9378 16.532 12.6357 16.0045 12.536 15.423C12.4363 14.8416 12.5453 14.2435 12.8439 13.7347C13.1424 13.2259 13.6113 12.839 14.1675 12.6424V11.6674C14.1675 11.4464 14.0797 11.2344 13.9234 11.0781C13.7671 10.9218 13.5552 10.834 13.3342 10.834H6.6675C6.44649 10.834 6.23453 10.9218 6.07825 11.0781C5.92197 11.2344 5.83417 11.4464 5.83417 11.6674V12.6424C6.39042 12.839 6.85925 13.2259 7.1578 13.7347C7.45636 14.2435 7.56541 14.8416 7.46568 15.423C7.36595 16.0045 7.06386 16.532 6.61281 16.9123C6.16177 17.2925 5.5908 17.5011 5.00084 17.5011C4.41088 17.5011 3.83991 17.2925 3.38886 16.9123C2.93781 16.532 2.63573 16.0045 2.536 15.423C2.43627 14.8416 2.54532 14.2435 2.84387 13.7347C3.14243 13.2259 3.61126 12.839 4.16751 12.6424V11.6674C4.16751 11.0043 4.4309 10.3684 4.89974 9.8996C5.36858 9.43076 6.00446 9.16737 6.6675 9.16737H9.1675V7.35904C8.73513 7.2064 8.35304 6.93782 8.06301 6.58267C7.77298 6.22753 7.58617 5.79948 7.52301 5.34532C7.45985 4.89116 7.52277 4.42838 7.70488 4.00757C7.88699 3.58676 8.1813 3.22412 8.55561 2.95929C8.92993 2.69446 9.36985 2.53764 9.82728 2.50597C10.2847 2.4743 10.7421 2.56901 11.1493 2.77973C11.5565 2.99045 11.898 3.30908 12.1364 3.70078C12.3747 4.09248 12.5008 4.54217 12.5008 5.0007ZM10.0008 4.16737C9.77982 4.16737 9.56786 4.25517 9.41158 4.41145C9.2553 4.56773 9.1675 4.77969 9.1675 5.0007C9.1675 5.22172 9.2553 5.43368 9.41158 5.58996C9.56786 5.74624 9.77982 5.83404 10.0008 5.83404C10.2219 5.83404 10.4338 5.74624 10.5901 5.58996C10.7464 5.43368 10.8342 5.22172 10.8342 5.0007C10.8342 4.77969 10.7464 4.56773 10.5901 4.41145C10.4338 4.25517 10.2219 4.16737 10.0008 4.16737ZM5.00084 14.1674C4.77982 14.1674 4.56786 14.2552 4.41158 14.4114C4.2553 14.5677 4.16751 14.7797 4.16751 15.0007C4.16751 15.2217 4.2553 15.4337 4.41158 15.59C4.56786 15.7462 4.77982 15.834 5.00084 15.834C5.22185 15.834 5.43381 15.7462 5.59009 15.59C5.74637 15.4337 5.83417 15.2217 5.83417 15.0007C5.83417 14.7797 5.74637 14.5677 5.59009 14.4114C5.43381 14.2552 5.22185 14.1674 5.00084 14.1674ZM15.0008 14.1674C14.7798 14.1674 14.5679 14.2552 14.4116 14.4114C14.2553 14.5677 14.1675 14.7797 14.1675 15.0007C14.1675 15.2217 14.2553 15.4337 14.4116 15.59C14.5679 15.7462 14.7798 15.834 15.0008 15.834C15.2219 15.834 15.4338 15.7462 15.5901 15.59C15.7464 15.4337 15.8342 15.2217 15.8342 15.0007C15.8342 14.7797 15.7464 14.5677 15.5901 14.4114C15.4338 14.2552 15.2219 14.1674 15.0008 14.1674Z" fill="#2F2F2F"/>
</svg>
  ),
  SidebarOpen: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width={800}
      height={800}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        stroke="#333"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M18 3H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3m-3 1v16"
      />
    </svg>
  ),
  ClaimIcon: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="25"
      height="18"
      viewBox="0 0 25 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M21.5 8.99942C20.9909 8.26705 14.7375 1.63437 14.1231 1.28516H10.7378C10.726 1.76914 17.4668 8.20227 17.787 8.99942C17.4668 9.79658 10.726 16.2297 10.7378 16.7137H14.1231C14.7375 16.3645 20.991 9.7318 21.5 8.99942ZM14.2622 8.99942C13.7532 8.26705 7.49975 1.63437 6.88531 1.28516H3.5C3.48809 1.76914 10.229 8.20227 10.5492 8.99942C10.229 9.79658 3.48814 16.2297 3.5 16.7137H6.88536C7.49975 16.3645 13.7532 9.7318 14.2622 8.99942Z"
        fill="#121212"
      />
    </svg>
  ),
  UpdateMe: (props: SVGProps<SVGSVGElement>) => (
    <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M18.5 34.625C27.4056 34.625 34.625 27.4056 34.625 18.5C34.625 9.59441 27.4056 2.375 18.5 2.375C9.59441 2.375 2.375 9.59441 2.375 18.5C2.375 27.4056 9.59441 34.625 18.5 34.625Z" stroke="#121212" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M18.4989 9.97461C20.3295 9.97449 22.1115 10.5635 23.5813 11.6546C25.0512 12.7457 26.1309 14.2809 26.6606 16.0332C27.1904 17.7854 27.1422 19.6616 26.523 21.3843C25.9039 23.107 24.7468 24.5846 23.2228 25.5987C21.6988 26.6128 19.8889 27.1095 18.0607 27.0153C16.2326 26.9211 14.4833 26.241 13.0716 25.0756C11.6599 23.9103 10.6608 22.3215 10.222 20.5443C9.78326 18.7671 9.92812 16.8959 10.6352 15.2074M10.6352 15.2074L8.55469 16.2259M10.6352 15.2074L12.0429 17.0479" stroke="#121212" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  RemoveMember: (props: SVGProps<SVGSVGElement>) => (
    <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M15.125 26.9991C15.125 28.5876 15.4242 30.1041 15.9688 31.4991H0.5C0.499176 28.1824 1.54541 24.9501 3.48955 22.263C5.43369 19.5759 8.1765 17.571 11.327 16.5343C9.8204 15.55 8.64352 14.1365 7.94834 12.4765C7.25316 10.8165 7.07162 8.98623 7.42715 7.22202C7.78269 5.4578 8.65897 3.8407 9.94284 2.57953C11.2267 1.31837 12.8592 0.47107 14.6295 0.147064C16.3997 -0.176942 18.2265 0.0372242 19.8738 0.761905C21.5212 1.48659 22.9134 2.68849 23.8707 4.21242C24.8281 5.73634 25.3066 7.51228 25.2444 9.31088C25.1822 11.1095 24.5823 12.8482 23.522 14.3023C22.8853 15.1731 21.9875 15.7806 21.0898 16.3881C20.7792 16.5936 20.4748 16.8051 20.1763 17.0226C18.6103 18.1723 17.337 19.6746 16.4595 21.4078C15.5819 23.141 15.1248 25.0564 15.125 26.9991Z" fill="#CE1111"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M36.5 27C36.5 29.3869 35.5518 31.6761 33.864 33.364C32.1761 35.0518 29.8869 36 27.5 36C25.1131 36 22.8239 35.0518 21.136 33.364C19.4482 31.6761 18.5 29.3869 18.5 27C18.5 24.6131 19.4482 22.3239 21.136 20.636C22.8239 18.9482 25.1131 18 27.5 18C29.8869 18 32.1761 18.9482 33.864 20.636C35.5518 22.3239 36.5 24.6131 36.5 27ZM32 28.6875V25.3125H23V28.6875H32Z" fill="#CE1111"/>
    </svg>

  ),
  SidebarClose: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width={50}
      height={50}
      viewBox="0 0 1.5 1.5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        stroke="#333"
        strokeWidth={0.125}
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M.375.188h.75a.19.19 0 0 1 .188.187v.75a.19.19 0 0 1-.188.188h-.75a.19.19 0 0 1-.187-.188v-.75A.19.19 0 0 1 .375.188M.563.25v1"
      />
    </svg>
  ),
  Agent: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_17129_51450)">
        <path
          d="M20.4989 8.25035V9.25035C20.533 11.0832 19.8884 12.864 18.6889 14.2503C18.6358 14.3141 18.5991 14.3898 18.5819 14.471C18.5647 14.5521 18.5675 14.6362 18.5902 14.716C18.6128 14.7958 18.6546 14.8689 18.7119 14.9289C18.7692 14.9889 18.8402 15.034 18.9189 15.0603C19.9948 15.4014 21.0282 15.8644 21.9989 16.4403C22.0749 16.4842 22.1611 16.5073 22.2489 16.5073C22.3366 16.5073 22.4229 16.4842 22.4989 16.4403C22.5746 16.3944 22.6373 16.3298 22.681 16.2528C22.7248 16.1758 22.7481 16.0889 22.7489 16.0003V7.00035C22.7466 6.1988 22.4868 5.41919 22.0079 4.77644C21.529 4.13369 20.8563 3.66181 20.0889 3.43035C19.9945 3.40129 19.8937 3.40082 19.7991 3.42899C19.7044 3.45716 19.6203 3.51271 19.5572 3.58864C19.4941 3.66458 19.4549 3.75749 19.4445 3.85567C19.4341 3.95385 19.453 4.05291 19.4989 4.14035C20.1614 5.40867 20.5047 6.81942 20.4989 8.25035ZM5.07888 15.0803C5.15754 15.054 5.22859 15.0089 5.28587 14.9489C5.34315 14.8889 5.38493 14.8158 5.40759 14.736C5.43025 14.6562 5.4331 14.5721 5.4159 14.491C5.3987 14.4098 5.36196 14.3341 5.30888 14.2703C4.10938 12.884 3.46473 11.1032 3.49888 9.27035V8.27035C3.50188 6.85004 3.84476 5.45107 4.49888 4.19035C4.54617 4.10203 4.56584 4.00155 4.55534 3.90192C4.54484 3.80229 4.50465 3.70811 4.43999 3.63159C4.37533 3.55507 4.28917 3.49975 4.19269 3.47278C4.0962 3.44581 3.99384 3.44845 3.89888 3.48035C3.12437 3.68124 2.44075 4.13859 1.95951 4.77783C1.47826 5.41707 1.22777 6.20049 1.24888 7.00035V16.0003C1.24963 16.0889 1.27299 16.1758 1.31674 16.2528C1.36049 16.3298 1.42319 16.3944 1.49888 16.4403C1.57489 16.4842 1.66111 16.5073 1.74888 16.5073C1.83665 16.5073 1.92287 16.4842 1.99888 16.4403C2.97047 15.8713 4.00382 15.415 5.07888 15.0803ZM22.5289 18.0003C20.9653 16.8727 19.1842 16.0826 17.2989 15.6803C17.1736 15.6506 17.0418 15.6685 16.9289 15.7303C15.4325 16.5962 13.7274 17.0355 11.9989 17.0003C10.2694 17.0324 8.56428 16.5897 7.06888 15.7203C6.95599 15.6585 6.82415 15.6406 6.69888 15.6703C4.81274 16.0757 3.03163 16.8692 1.46888 18.0003C1.15545 18.2304 0.90129 18.5318 0.727403 18.8796C0.553517 19.2274 0.4649 19.6115 0.468878 20.0003V23.0003C0.468878 23.133 0.521557 23.2601 0.615325 23.3539C0.709093 23.4477 0.83627 23.5003 0.968878 23.5003H22.9989C23.1315 23.5003 23.2587 23.4477 23.3524 23.3539C23.4462 23.2601 23.4989 23.133 23.4989 23.0003V20.0003C23.5063 19.6141 23.4223 19.2316 23.2537 18.884C23.0852 18.5364 22.8368 18.2336 22.5289 18.0003ZM20.2489 20.0903C20.2463 20.2885 20.1664 20.4777 20.0263 20.6178C19.8862 20.7579 19.697 20.8378 19.4989 20.8403H17.4989C17.3 20.8403 17.1092 20.7613 16.9685 20.6207C16.8279 20.48 16.7489 20.2893 16.7489 20.0903C16.7489 19.8914 16.8279 19.7007 16.9685 19.56C17.1092 19.4194 17.3 19.3403 17.4989 19.3403H19.4989C19.6961 19.3403 19.8853 19.418 20.0257 19.5565C20.1661 19.695 20.2463 19.8832 20.2489 20.0803V20.0903ZM7.24888 20.0903C7.24629 20.2885 7.16644 20.4777 7.02634 20.6178C6.88625 20.7579 6.69699 20.8378 6.49888 20.8403H4.49888C4.29997 20.8403 4.1092 20.7613 3.96855 20.6207C3.8279 20.48 3.74888 20.2893 3.74888 20.0903C3.74888 19.8914 3.8279 19.7007 3.96855 19.56C4.1092 19.4194 4.29997 19.3403 4.49888 19.3403H6.49888C6.69607 19.3403 6.88533 19.418 7.0257 19.5565C7.16607 19.695 7.24625 19.8832 7.24888 20.0803V20.0903ZM8.49888 20.0003C8.50144 19.8685 8.55494 19.7428 8.64816 19.6496C8.74138 19.5564 8.86707 19.5029 8.99888 19.5003H14.9989C15.0659 19.5016 15.1319 19.5161 15.1933 19.543C15.2547 19.5698 15.3102 19.6085 15.3566 19.6568C15.403 19.7051 15.4395 19.762 15.4639 19.8244C15.4883 19.8868 15.5002 19.9534 15.4989 20.0203V22.0203C15.4989 22.153 15.4462 22.2801 15.3524 22.3739C15.2587 22.4677 15.1315 22.5203 14.9989 22.5203H8.99888C8.86627 22.5203 8.73909 22.4677 8.64532 22.3739C8.55156 22.2801 8.49888 22.153 8.49888 22.0203V20.0003Z"
          fill="currentColor"
        />
        <path
          d="M12.0008 16C15.7308 16 19.5008 13.92 19.5008 9.28V8.28C19.5194 7.27666 19.3401 6.27949 18.9732 5.34545C18.6063 4.41141 18.059 3.5588 17.3625 2.83633C16.666 2.11386 15.834 1.53567 14.9141 1.1348C13.9941 0.733923 13.0042 0.518217 12.0008 0.5C9.97973 0.536827 8.05562 1.37312 6.64988 2.82571C5.24414 4.27831 4.47137 6.2288 4.50081 8.25V9.25C4.50081 13.92 8.27081 16 12.0008 16ZM12.0008 2.58C12.7279 2.58393 13.447 2.73119 14.1171 3.01336C14.7872 3.29553 15.395 3.70707 15.9059 4.22442C16.4168 4.74178 16.8206 5.3548 17.0943 6.0284C17.3679 6.70199 17.5061 7.42295 17.5008 8.15C17.5008 11.22 15.0408 11.86 12.0008 11.86C8.96081 11.86 6.50081 11.22 6.50081 8.15C6.49554 7.42295 6.6337 6.70199 6.90737 6.0284C7.18104 5.3548 7.58486 4.74178 8.09572 4.22442C8.60657 3.70707 9.21444 3.29553 9.88452 3.01336C10.5546 2.73119 11.2738 2.58393 12.0008 2.58Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_17129_51450">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  Dashboard: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.85 4L10.57 13H4V6H2V22H22V2H2V4H13.85ZM4 15H9.84L8 20H4V15ZM10.15 20L13.43 11H20V20H10.15ZM20 9H14.16L15.98 4H19.98L20 9Z"
        fill="currentColor"
      />
    </svg>
  ),
  Knowledge: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2 3H8C9.06087 3 10.0783 3.42143 10.8284 4.17157C11.5786 4.92172 12 5.93913 12 7V21C12 20.2044 11.6839 19.4413 11.1213 18.8787C10.5587 18.3161 9.79565 18 9 18H2V3Z"
        fill="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22 3H16C14.9391 3 13.9217 3.42143 13.1716 4.17157C12.4214 4.92172 12 5.93913 12 7V21C12 20.2044 12.3161 19.4413 12.8787 18.8787C13.4413 18.3161 14.2044 18 15 18H22V3Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Stack: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.1556 3.35889C12.4846 3.00889 11.5756 3.00389 10.8986 3.35289C8.30561 4.69289 5.95361 6.22789 3.89261 7.93089C3.57361 8.19389 3.34761 8.57689 3.35161 9.02689C3.35561 9.47589 3.58561 9.85289 3.90261 10.1119C5.94661 11.7829 8.30061 13.3199 10.8456 14.6489C11.5176 14.9989 12.4256 15.0039 13.1026 14.6549C15.6956 13.3149 18.0476 11.7799 20.1086 10.0769C20.4276 9.81389 20.6536 9.43089 20.6496 8.98089C20.6456 8.53189 20.4146 8.15489 20.0986 7.89589C18.0546 6.22489 15.7006 4.68789 13.1556 3.35889ZM11.5856 4.68589C11.7236 4.62456 11.873 4.59312 12.0239 4.59364C12.1749 4.59415 12.3241 4.62662 12.4616 4.68889C14.8906 5.95689 17.1286 7.41589 19.0676 8.99089C17.1176 10.5919 14.8856 12.0459 12.4146 13.3219C12.1746 13.4459 11.7816 13.4449 11.5396 13.3189C9.11061 12.0509 6.87261 10.5919 4.93261 9.01689C6.88361 7.41589 9.11562 5.96189 11.5866 4.68589"
        fill="currentColor"
      />
      <path
        d="M21.1951 12.6964C21.3003 12.8651 21.3342 13.0687 21.2894 13.2624C21.2446 13.4561 21.1247 13.6241 20.9561 13.7294L14.8491 17.5424C14.0201 18.0604 12.9921 18.2994 11.9981 18.2994C11.0041 18.2994 9.97607 18.0594 9.14707 17.5434L3.10707 13.7734C2.93826 13.6681 2.81819 13.5001 2.77327 13.3062C2.72836 13.1124 2.76228 12.9087 2.86757 12.7399C2.97286 12.5711 3.1409 12.451 3.33472 12.4061C3.52854 12.3612 3.73226 12.3951 3.90107 12.5004L9.94107 16.2704C10.4851 16.6104 11.2241 16.7994 11.9981 16.7994C12.7711 16.7994 13.5101 16.6094 14.0541 16.2694L20.1621 12.4574C20.3308 12.3522 20.5343 12.3182 20.728 12.3631C20.9217 12.4079 21.0897 12.5278 21.1951 12.6964Z"
        fill="currentColor"
      />
      <path
        d="M21.1987 16.4536C21.304 16.6223 21.3379 16.8259 21.2931 17.0196C21.2483 17.2133 21.1284 17.3813 20.9597 17.4866L15.6717 20.7906C14.6137 21.4506 13.2917 21.7616 12.0017 21.7616C10.7137 21.7616 9.39174 21.4516 8.33274 20.7916L3.11074 17.5316C2.94193 17.4262 2.82191 17.2581 2.77709 17.0641C2.73227 16.8702 2.76632 16.6665 2.87174 16.4976C2.97716 16.3288 3.14533 16.2088 3.33924 16.164C3.53315 16.1192 3.73693 16.1532 3.90574 16.2586L9.12774 19.5186C9.90074 20.0016 10.9347 20.2616 12.0027 20.2616C13.0707 20.2616 14.1047 20.0016 14.8777 19.5186L20.1657 16.2156C20.2493 16.1634 20.3424 16.1281 20.4396 16.1119C20.5368 16.0956 20.6362 16.0986 20.7323 16.1208C20.8283 16.143 20.919 16.1839 20.9992 16.2412C21.0794 16.2985 21.1476 16.371 21.1997 16.4546"
        fill="currentColor"
      />
    </svg>
  ),
  Wallet: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.25 7.5C5.7375 7.5 5 7.1 5 6.25V5H25V2.5H2.5V23.75C2.5 25.8125 4.1875 27.5 6.25 27.5H27.5V7.5H6.25ZM25 25H6.25C5.5625 25 5 24.4375 5 23.75V9.7875C5.4 9.925 5.825 10 6.25 10H25V25Z"
        fill="#424242"
      />
      <path
        d="M20 20.3125C20.3693 20.3125 20.7351 20.2398 21.0763 20.0984C21.4175 19.9571 21.7276 19.7499 21.9887 19.4887C22.2499 19.2276 22.4571 18.9175 22.5984 18.5763C22.7398 18.2351 22.8125 17.8693 22.8125 17.5C22.8125 17.1307 22.7398 16.7649 22.5984 16.4237C22.4571 16.0825 22.2499 15.7724 21.9887 15.5113C21.7276 15.2501 21.4175 15.0429 21.0763 14.9016C20.7351 14.7602 20.3693 14.6875 20 14.6875C19.2541 14.6875 18.5387 14.9838 18.0113 15.5113C17.4838 16.0387 17.1875 16.7541 17.1875 17.5C17.1875 18.2459 17.4838 18.9613 18.0113 19.4887C18.5387 20.0162 19.2541 20.3125 20 20.3125Z"
        fill="#424242"
      />
    </svg>
  ),
  Eye: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="14"
      height="10"
      viewBox="0 0 14 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7 0.894531C11.447 0.894531 13.5 4.99953 13.5 4.99953C13.5 4.99953 11.447 9.10453 7 9.10453C2.553 9.10453 0.5 4.99953 0.5 4.99953C0.5 4.99953 2.553 0.894531 7 0.894531Z"
        stroke="#FF3E00"
        strokeWidth="0.929"
        strokeLinejoin="round"
      />
      <path
        d="M8.94091 5C8.94686 5.26634 8.89955 5.53119 8.80174 5.77899C8.70393 6.02679 8.5576 6.25256 8.37134 6.44303C8.18508 6.63351 7.96265 6.78485 7.71709 6.88817C7.47154 6.99149 7.20781 7.04472 6.94141 7.04472C6.675 7.04472 6.41128 6.99149 6.16572 6.88817C5.92017 6.78485 5.69773 6.63351 5.51147 6.44303C5.32521 6.25256 5.17888 6.02679 5.08107 5.77899C4.98326 5.53119 4.93595 5.26634 4.94191 5C4.94191 4.46957 5.15262 3.96086 5.52769 3.58579C5.90277 3.21071 6.41147 3 6.94191 3C7.47234 3 7.98105 3.21071 8.35612 3.58579C8.73119 3.96086 8.94191 4.46957 8.94191 5H8.94091Z"
        stroke="#FF3E00"
        strokeWidth="0.929"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Bell: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2.80058 3.98172C2.90156 3.07066 3.335 2.22886 4.01793 1.61745C4.70085 1.00603 5.58528 0.667969 6.50192 0.667969C7.41855 0.667969 8.30298 1.00603 8.9859 1.61745C9.66883 2.22886 10.1023 3.07066 10.2032 3.98172L10.3712 5.49238L10.3752 5.53038C10.4613 6.27973 10.7054 7.0023 11.0912 7.65038L11.1112 7.68372L11.4959 8.32572C11.8459 8.90838 12.0206 9.19972 11.9826 9.43905C11.9575 9.59791 11.8758 9.74234 11.7526 9.84572C11.5666 10.0017 11.2266 10.0017 10.5472 10.0017H2.45658C1.77658 10.0017 1.43658 10.0017 1.25125 9.84638C1.12765 9.74295 1.04571 9.59825 1.02058 9.43905C0.983248 9.19972 1.15791 8.90838 1.50725 8.32572L1.89325 7.68305L1.91325 7.64972C2.29882 7.00178 2.54262 6.27945 2.62858 5.53038L2.63258 5.49238L2.80058 3.98172Z"
        stroke="#4A35CB"
      />
      <path
        d="M4.57031 10.2734C4.68431 10.7694 4.93565 11.2081 5.28498 11.5201C5.63431 11.8334 6.06231 12.0028 6.50231 12.0028C6.94231 12.0028 7.37031 11.8334 7.71965 11.5208C8.06898 11.2074 8.31965 10.7694 8.43431 10.2734"
        stroke="#4A35CB"
        strokeLinecap="round"
      />
    </svg>
  ),
  Check: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M1 7C1 3.68667 3.68667 1 7 1C10.3133 1 13 3.68667 13 7C13 10.3133 10.3133 13 7 13C3.68667 13 1 10.3133 1 7Z"
        stroke="#23BD33"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.33203 7.0013L6.33203 9.0013L9.66536 5.66797"
        stroke="#23BD33"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  DownloadCloud: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_14949_6379)">
        <path
          d="M8 17L12 21L16 17"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 12V21"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M20.8812 18.0899C21.7505 17.4786 22.4025 16.6061 22.7424 15.5991C23.0824 14.5921 23.0926 13.503 22.7715 12.4898C22.4505 11.4766 21.815 10.592 20.9572 9.96449C20.0994 9.33697 19.064 8.9991 18.0012 8.99993H16.7412C16.4404 7.82781 15.8776 6.73918 15.0953 5.81601C14.3129 4.89285 13.3313 4.15919 12.2244 3.67029C11.1174 3.18138 9.914 2.94996 8.70468 2.99345C7.49536 3.03694 6.31167 3.3542 5.24271 3.92136C4.17375 4.48851 3.24738 5.29078 2.53334 6.26776C1.8193 7.24474 1.33621 8.37098 1.12041 9.56168C0.904624 10.7524 0.961764 11.9765 1.28753 13.142C1.6133 14.3074 2.19921 15.3837 3.00115 16.2899"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_14949_6379">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  Search: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.9984 20.9984L16.6484 16.6484"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  ChipExtractionRound: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5 11.9994C5 13.7661 5.575 15.3077 6.725 16.6244C7.875 17.9411 9.325 18.7161 11.075 18.9494C11.3417 18.9827 11.5627 19.0994 11.738 19.2994C11.9133 19.4994 12.0007 19.7327 12 19.9994C12 20.2827 11.879 20.5161 11.637 20.6994C11.395 20.8827 11.1243 20.9577 10.825 20.9244C8.575 20.6411 6.70833 19.6534 5.225 17.9614C3.74167 16.2694 3 14.2821 3 11.9994C3 9.73273 3.73767 7.75373 5.213 6.06239C6.68833 4.37106 8.55067 3.37506 10.8 3.07439C11.1167 3.04106 11.396 3.11206 11.638 3.28739C11.88 3.46273 12.0007 3.70006 12 3.99939C12 4.26606 11.9127 4.49939 11.738 4.69939C11.5633 4.89939 11.3423 5.01606 11.075 5.04939C9.325 5.28273 7.875 6.05773 6.725 7.37439C5.575 8.69106 5 10.2327 5 11.9994ZM17.175 12.9994H10C9.71667 12.9994 9.47933 12.9034 9.288 12.7114C9.09667 12.5194 9.00067 12.2821 9 11.9994C8.99933 11.7167 9.09533 11.4794 9.288 11.2874C9.48067 11.0954 9.718 10.9994 10 10.9994H17.175L15.3 9.12439C15.1 8.92439 15 8.68706 15 8.41239C15 8.13773 15.1 7.90006 15.3 7.69939C15.5 7.49873 15.7333 7.39873 16 7.39939C16.2667 7.40006 16.5 7.50006 16.7 7.69939L20.3 11.2994C20.5 11.4994 20.6 11.7327 20.6 11.9994C20.6 12.2661 20.5 12.4994 20.3 12.6994L16.7 16.2994C16.5 16.4994 16.2667 16.5954 16 16.5874C15.7333 16.5794 15.5 16.4751 15.3 16.2744C15.1 16.0737 15 15.8404 15 15.5744C15 15.3084 15.1 15.0751 15.3 14.8744L17.175 12.9994Z"
        fill="currentColor"
      />
    </svg>
  ),
  User: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.3906 9.39844C11.099 9.64323 11.737 9.98438 12.3047 10.4219C12.8724 10.8594 13.3568 11.3698 13.7578 11.9531C14.1589 12.5365 14.4661 13.1693 14.6797 13.8516C14.8932 14.5339 15 15.25 15 16H14C14 15.1458 13.849 14.3542 13.5469 13.625C13.2448 12.8958 12.8229 12.2604 12.2812 11.7188C11.7396 11.1771 11.1068 10.7578 10.3828 10.4609C9.65885 10.1641 8.86458 10.0104 8 10C7.44271 10 6.90625 10.0703 6.39062 10.2109C5.875 10.3516 5.39583 10.5495 4.95312 10.8047C4.51042 11.0599 4.10677 11.3698 3.74219 11.7344C3.3776 12.099 3.06771 12.5026 2.8125 12.9453C2.55729 13.388 2.35677 13.8698 2.21094 14.3906C2.0651 14.9115 1.99479 15.4479 2 16H1C1 15.25 1.10938 14.5339 1.32812 13.8516C1.54688 13.1693 1.85677 12.5391 2.25781 11.9609C2.65885 11.3828 3.14323 10.8776 3.71094 10.4453C4.27865 10.013 4.91667 9.66667 5.625 9.40625C5.21875 9.1875 4.85417 8.92188 4.53125 8.60938C4.20833 8.29688 3.9349 7.95052 3.71094 7.57031C3.48698 7.1901 3.3125 6.78125 3.1875 6.34375C3.0625 5.90625 3 5.45833 3 5C3 4.30729 3.13021 3.65885 3.39062 3.05469C3.65104 2.45052 4.00781 1.91927 4.46094 1.46094C4.91406 1.0026 5.44271 0.645833 6.04688 0.390625C6.65104 0.135417 7.30208 0.00520833 8 0C8.69271 0 9.34115 0.130208 9.94531 0.390625C10.5495 0.651042 11.0807 1.00781 11.5391 1.46094C11.9974 1.91406 12.3542 2.44271 12.6094 3.04688C12.8646 3.65104 12.9948 4.30208 13 5C13 5.45833 12.9401 5.90365 12.8203 6.33594C12.7005 6.76823 12.526 7.17448 12.2969 7.55469C12.0677 7.9349 11.7943 8.28125 11.4766 8.59375C11.1589 8.90625 10.7969 9.17448 10.3906 9.39844ZM4 5C4 5.55208 4.10417 6.07031 4.3125 6.55469C4.52083 7.03906 4.80729 7.46094 5.17188 7.82031C5.53646 8.17969 5.96094 8.46615 6.44531 8.67969C6.92969 8.89323 7.44792 9 8 9C8.55208 9 9.07031 8.89583 9.55469 8.6875C10.0391 8.47917 10.4609 8.19271 10.8203 7.82812C11.1797 7.46354 11.4661 7.03906 11.6797 6.55469C11.8932 6.07031 12 5.55208 12 5C12 4.44792 11.8958 3.92969 11.6875 3.44531C11.4792 2.96094 11.1927 2.53906 10.8281 2.17969C10.4635 1.82031 10.0391 1.53385 9.55469 1.32031C9.07031 1.10677 8.55208 1 8 1C7.44792 1 6.92969 1.10417 6.44531 1.3125C5.96094 1.52083 5.53906 1.80729 5.17969 2.17188C4.82031 2.53646 4.53385 2.96094 4.32031 3.44531C4.10677 3.92969 4 4.44792 4 5Z"
          fill="#FF3E00"
      />
    </svg>
  ),
  Chat: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12.552 5.55078C13.2796 5.96791 13.8873 6.56574 14.3164 7.28649C14.7455 8.00723 14.9813 8.82651 15.0012 9.6651C15.0056 10.6454 14.6918 11.6007 14.1069 12.3873L15.0012 15.4982L11.8909 14.1527C11.0342 14.5328 10.1064 14.7263 9.1692 14.7202C8.0072 14.7632 6.85905 14.4563 5.87337 13.8393C4.88769 13.2224 4.10988 12.3236 3.64062 11.2595"
        stroke="#004814"
        strokeWidth="0.597279"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.6641 6.55546C12.6641 9.34749 10.0514 11.6105 6.83207 11.6105C5.89486 11.6166 4.96707 11.4232 4.11034 11.0431L3.60495 11.2607L1.00004 12.3882L1.89435 9.27731C1.30958 8.49074 0.99576 7.53563 1.00004 6.55546C1.00004 3.76305 3.61279 1.5 6.83207 1.5C9.65496 1.5 12.0109 3.24227 12.5477 5.55198C12.626 5.8805 12.6649 6.2176 12.6641 6.55546Z"
        stroke="#FF3E00"
        strokeWidth="0.597279"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  Chat2: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="17"
      height="18"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
    <path
    d="M5.93968 7.625C5.93968 7.44266 6.01212 7.26779 6.14105 7.13886C6.26998 7.00993 6.44485 6.9375 6.62718 6.9375H10.7522C10.9345 6.9375 11.1094 7.00993 11.2383 7.13886C11.3673 7.26779 11.4397 7.44266 11.4397 7.625C11.4397 7.80733 11.3673 7.9822 11.2383 8.11113C11.1094 8.24006 10.9345 8.3125 10.7522 8.3125H6.62718C6.44485 8.3125 6.26998 8.24006 6.14105 8.11113C6.01212 7.9822 5.93968 7.80733 5.93968 7.625ZM6.62718 9.6875C6.44485 9.6875 6.26998 9.75993 6.14105 9.88886C6.01212 10.0178 5.93968 10.1927 5.93968 10.375C5.93968 10.5573 6.01212 10.7322 6.14105 10.8611C6.26998 10.9901 6.44485 11.0625 6.62718 11.0625H9.37718C9.55952 11.0625 9.73439 10.9901 9.86332 10.8611C9.99225 10.7322 10.0647 10.5573 10.0647 10.375C10.0647 10.1927 9.99225 10.0178 9.86332 9.88886C9.73439 9.75993 9.55952 9.6875 9.37718 9.6875H6.62718ZM0.439684 9C0.440035 7.18368 1.03977 5.41825 2.14585 3.97757C3.25193 2.53689 4.80253 1.50149 6.55712 1.03198C8.3117 0.562478 10.1722 0.685118 11.8499 1.38088C13.5277 2.07664 14.929 3.30662 15.8364 4.88003C16.7438 6.45344 17.1066 8.28232 16.8685 10.083C16.6304 11.8836 15.8048 13.5553 14.5197 14.8389C13.2345 16.1224 11.5618 16.9459 9.76082 17.1818C7.95988 17.4176 6.13146 17.0525 4.55918 16.1431L1.34443 17.2156C1.22566 17.2553 1.09832 17.2617 0.976146 17.2343C0.853971 17.2069 0.741605 17.1466 0.651176 17.06C0.560746 16.9734 0.495687 16.8637 0.463019 16.7429C0.430351 16.622 0.431316 16.4945 0.465809 16.3741L1.44481 12.949C0.784836 11.7374 0.439254 10.3797 0.439684 9ZM8.68968 2.125C7.47581 2.12492 6.28355 2.44624 5.23411 3.05629C4.18467 3.66634 3.31545 4.54338 2.71483 5.59825C2.1142 6.65311 1.80358 7.8482 1.81453 9.06203C1.82548 10.2759 2.15762 11.4651 2.77718 12.509C2.82485 12.5896 2.85554 12.6792 2.86736 12.7721C2.87918 12.865 2.87189 12.9594 2.84593 13.0494L2.14606 15.4969L4.42581 14.7379C4.52263 14.7056 4.62543 14.6952 4.72674 14.7076C4.82806 14.7199 4.92535 14.7547 5.01156 14.8094C5.91255 15.3796 6.93383 15.7324 7.99466 15.8399C9.05549 15.9475 10.1268 15.8068 11.1239 15.429C12.121 15.0512 13.0166 14.4467 13.7398 13.6632C14.463 12.8797 14.9941 11.9388 15.2911 10.9147C15.588 9.8906 15.6427 8.81149 15.4508 7.76263C15.2589 6.71378 14.8257 5.72393 14.1853 4.87134C13.545 4.01876 12.7151 3.32679 11.7613 2.85015C10.8075 2.3735 9.75595 2.12525 8.68968 2.125Z"
    fill="currentColor"/>
</svg>
  ),
  DocumentStack: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.3304 11.0491V3.43006C13.3304 3.02592 13.1698 2.63833 12.884 2.35256C12.5983 2.06679 12.2107 1.90625 11.8065 1.90625H5.71131C5.30717 1.90625 4.91958 2.06679 4.63381 2.35256C4.34804 2.63833 4.1875 3.02592 4.1875 3.43006V11.0491C4.1875 11.4532 4.34804 11.8408 4.63381 12.1266C4.91958 12.4124 5.30717 12.5729 5.71131 12.5729H11.8065C12.2107 12.5729 12.5983 12.4124 12.884 12.1266C13.1698 11.8408 13.3304 11.4532 13.3304 11.0491Z"
        stroke="#FF3E00"
        strokeWidth="0.761905"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.04088 3.76172L2.51402 4.31791C2.13438 4.45619 1.8252 4.73958 1.65446 5.10577C1.48372 5.47197 1.46541 5.89098 1.60354 6.27067L4.20926 13.4303C4.27772 13.6184 4.38257 13.7911 4.5178 13.9386C4.65303 14.0862 4.81601 14.2056 4.99742 14.2902C5.17883 14.3747 5.37511 14.4227 5.57507 14.4314C5.77502 14.4401 5.97473 14.4093 6.16278 14.3408L10.4051 12.6661M6.47516 5.716H10.2847M6.47516 7.23981H11.0466M6.47516 8.76362H8.76088"
        stroke="#FF3E00"
        strokeWidth="0.761905"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  RiskAnalysis: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.99852 4.55066L1.02443 12.6005H10.9726L9.77155 10.6563C10.1042 10.8178 10.4635 10.9258 10.8346 10.9758L11.9468 12.7755C11.9797 12.8287 11.9971 12.8891 11.997 12.9505C11.997 13.012 11.9797 13.0724 11.9467 13.1256C11.9138 13.1788 11.8664 13.223 11.8093 13.2537C11.7523 13.2844 11.6876 13.3005 11.6217 13.3004H0.374969C0.309147 13.3004 0.244486 13.2843 0.187483 13.2536C0.13048 13.2228 0.0831454 13.1787 0.0502352 13.1254C0.0173251 13.0722 -4.18038e-07 13.0119 0 12.9505C4.18053e-07 12.889 0.0173267 12.8287 0.0502376 12.7755L5.67379 3.67498C5.7067 3.62178 5.75404 3.5776 5.81104 3.54689C5.86804 3.51617 5.9327 3.5 5.99852 3.5C6.06434 3.5 6.129 3.51617 6.186 3.54689C6.243 3.5776 6.29034 3.62178 6.32325 3.67498L8.20564 6.72096C8.06632 7.04638 7.98684 7.39142 7.97053 7.74153L5.99852 4.55066Z"
        fill="#FF3E00"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.3056 10.1183C10.6261 10.2448 10.9705 10.3097 11.3185 10.309C11.9189 10.3101 12.5009 10.1162 12.965 9.7606L14.4357 11.1329L15.0011 10.6051L13.4997 9.20376C13.79 8.78591 13.9356 8.29479 13.9166 7.79709C13.8975 7.29938 13.7149 6.81918 13.3935 6.42168C13.072 6.02418 12.6273 5.72863 12.1197 5.57514C11.6121 5.42165 11.0662 5.41767 10.556 5.56373C10.0459 5.70978 9.59632 5.99881 9.26824 6.39157C8.94016 6.78433 8.74951 7.2618 8.72217 7.75917C8.69482 8.25654 8.83211 8.74972 9.11539 9.17176C9.39867 9.5938 9.81422 9.92426 10.3056 10.1183ZM9.51856 7.88254C9.51856 8.3281 9.70819 8.7554 10.0457 9.07045C10.3833 9.38551 10.8411 9.5625 11.3185 9.5625C11.7958 9.5625 12.2536 9.38551 12.5912 9.07045C12.9287 8.7554 13.1184 8.3281 13.1184 7.88254C13.1184 7.43699 12.9287 7.00969 12.5912 6.69463C12.2536 6.37958 11.7958 6.20258 11.3185 6.20258C10.8411 6.20258 10.3833 6.37958 10.0457 6.69463C9.70819 7.00969 9.51856 7.43699 9.51856 7.88254Z"
        fill="#FF3E00"
      />
      <path
        d="M5.52734 10.5173H6.46479V11.3923H5.52734V10.5173ZM5.99607 7.01562C5.73733 7.01562 5.52734 7.21162 5.52734 7.45311V9.37807C5.52734 9.4941 5.57673 9.60537 5.66463 9.68742C5.75253 9.76946 5.87175 9.81556 5.99607 9.81556C6.12038 9.81556 6.2396 9.76946 6.3275 9.68742C6.41541 9.60537 6.46479 9.4941 6.46479 9.37807V7.45311C6.46479 7.21162 6.2548 7.01562 5.99607 7.01562Z"
        fill="#FF3E00"
      />
    </svg>
  ),
  Loop: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12 0V2H2C0.9 2 0 2.9 0 4V6H2V4H12V6L16 3L12 0ZM4 8L0 11L4 14V12H14C15.1 12 16 11.1 16 10V8H14V10H4V8Z"
        fill="#FF3E00"
      />
    </svg>
  ),
  Send: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M1.16641 6.99951L0.663079 2.46868C0.518913 1.17201 1.85391 0.219509 3.03308 0.778675L12.9864 5.49368C14.2572 6.09534 14.2572 7.90368 12.9864 8.50534L3.03308 13.2212C1.85391 13.7795 0.518913 12.8278 0.663079 11.5312L1.16641 6.99951ZM1.16641 6.99951H6.99975"
        stroke="#121212"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  DownloadCircle: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.05366 11.0104L4.34876 9.64648C4.12298 10.2779 4 10.9582 4 11.6672C4 14.9809 6.68629 17.6672 10 17.6672C13.3137 17.6672 16 14.9809 16 11.6672C16 10.9582 15.877 10.2779 15.6512 9.64649L13.9463 11.0104C13.9816 11.2241 14 11.4435 14 11.6672C14 13.8763 12.2091 15.6672 10 15.6672C7.79086 15.6672 6 13.8763 6 11.6672C6 11.4435 6.01836 11.2241 6.05366 11.0104Z"
        fill="white"
      />
      <path
        d="M9.9987 10.834L9.374 11.6149L9.9987 12.1146L10.6234 11.6149L9.9987 10.834ZM10.9987 3.33398C10.9987 2.7817 10.551 2.33398 9.9987 2.33398C9.44641 2.33398 8.9987 2.7817 8.9987 3.33398L10.9987 3.33398ZM5.83203 7.50065L5.20734 8.28152L9.374 11.6149L9.9987 10.834L10.6234 10.0531L6.45673 6.71978L5.83203 7.50065ZM9.9987 10.834L10.6234 11.6149L14.7901 8.28152L14.1654 7.50065L13.5407 6.71978L9.374 10.0531L9.9987 10.834ZM9.9987 10.834L10.9987 10.834L10.9987 3.33398L9.9987 3.33398L8.9987 3.33398L8.9987 10.834L9.9987 10.834Z"
        fill="white"
      />
    </svg>
  ),
  DocumentsBroken: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_14539_12752)">
        <path
          d="M12.6654 10.6654C12.6654 12.5507 12.6654 13.494 12.0794 14.0794C11.494 14.6654 10.5507 14.6654 8.66536 14.6654H7.33203C5.4467 14.6654 4.50336 14.6654 3.91803 14.0794C3.33203 13.494 3.33203 12.5507 3.33203 10.6654V7.9987M3.33203 5.33203C3.33203 3.4467 3.33203 2.50336 3.91803 1.91803C4.50336 1.33203 5.4467 1.33203 7.33203 1.33203H8.66536C10.5507 1.33203 11.494 1.33203 12.0794 1.91803C12.6654 2.50336 12.6654 3.4467 12.6654 5.33203V7.9987"
          stroke="#FF3E00"
          strokeLinecap="round"
        />
        <path
          d="M3.33203 2.71484C2.68203 2.77884 2.2447 2.92351 1.91803 3.25018C1.33203 3.83551 1.33203 4.77884 1.33203 6.66418V9.33084C1.33203 11.2162 1.33203 12.1595 1.91803 12.7448C2.2447 13.0715 2.68203 13.2162 3.33203 13.2802M12.6654 2.71484C13.3154 2.77884 13.7527 2.92351 14.0794 3.25018C14.6654 3.83551 14.6654 4.77884 14.6654 6.66418V9.33084C14.6654 11.2162 14.6654 12.1595 14.0794 12.7448C13.7527 13.0715 13.3154 13.2162 12.6654 13.2802"
          stroke="#FF3E00"
        />
        <path
          d="M6 8.66667H10M6 6H10M6 11.3333H8"
          stroke="#FF3E00"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_14539_12752">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  CreditCardAccepted: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7.33203 13.3346H6.9987C4.4927 13.3346 3.24003 13.3346 2.40336 12.6746C2.2704 12.5693 2.14683 12.4527 2.03403 12.326C1.33203 11.5393 1.33203 10.3593 1.33203 8.0013C1.33203 5.6433 1.33203 4.46397 2.03403 3.67664C2.14648 3.55041 2.26959 3.43441 2.40336 3.32864C3.24003 2.66797 4.4927 2.66797 6.9987 2.66797H8.9987C11.5047 2.66797 12.7574 2.66797 13.5934 3.32797C13.7276 3.43464 13.8509 3.55086 13.9634 3.67664C14.596 4.38597 14.6587 5.4133 14.6654 7.33464M1.33203 6.0013H14.6654M9.33203 12.0013C9.33203 12.0013 9.9987 12.0013 10.6654 13.3346C10.6654 13.3346 12.7834 10.0013 14.6654 9.33464"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  AcceptAction: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11.501 5.516L4 5.5L7.499 8L4.0015 10.5H11.5C11.6326 10.5 11.7598 10.4473 11.8536 10.3536C11.9473 10.2598 12 10.1326 12 10V6.016C12 5.88356 11.9475 5.75654 11.8539 5.6628C11.7604 5.56906 11.6334 5.51626 11.501 5.516Z"
        fill="#FF3E00"
      />
      <path
        d="M10.5 15H5.5C3.0185 15 1 12.9815 1 10.5V5.5C1 3.0185 3.0185 1 5.5 1H10.5C12.9815 1 15 3.0185 15 5.5V10.5C15 12.9815 12.9815 15 10.5 15ZM5.5 2C3.57 2 2 3.57 2 5.5V10.5C2 12.43 3.57 14 5.5 14H10.5C12.43 14 14 12.43 14 10.5V5.5C14 3.57 12.43 2 10.5 2H5.5Z"
        fill="#FF3E00"
      />
    </svg>
  ),
  CardRejected: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.33203 13.3346H6.9987C4.4927 13.3346 3.24003 13.3346 2.40336 12.6746C2.2704 12.5693 2.14683 12.4527 2.03403 12.326C1.33203 11.5393 1.33203 10.3593 1.33203 8.0013C1.33203 5.6433 1.33203 4.46397 2.03403 3.67664C2.14648 3.55041 2.26959 3.43441 2.40336 3.32864C3.24003 2.66797 4.4927 2.66797 6.9987 2.66797H8.9987C11.5047 2.66797 12.7574 2.66797 13.5934 3.32797C13.7276 3.43464 13.8509 3.55086 13.9634 3.67664C14.596 4.38597 14.6587 5.4133 14.6654 7.33464M1.33203 6.0013H14.6654M14.6654 9.33464L10.6654 13.3346M14.6654 13.3346L10.6654 9.33464"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  ChartAverage: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14 14H6.66667C4.46667 14 3.36667 14 2.68333 13.3167C2 12.6333 2 11.5333 2 9.33333V2M4 8H4.006M5.99933 8H6.00467M7.998 8H8.00333M9.99667 8H10.0027M11.9953 8H12.0013M13.994 8H14"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4 4.66536C4.44867 3.91736 5.058 3.33203 5.99533 3.33203C9.95733 3.33203 7.73 11.332 11.988 11.332C12.932 11.332 13.5373 10.7427 14 9.9987"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  DataRecord: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2 8C2 9.10467 4.388 10 7.33333 10C7.55911 10 7.78133 9.99489 8 9.98467"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.6667 3.33203V7.66536M2 3.33203V12.6654C2 13.77 4.388 14.6654 7.33333 14.6654C7.55911 14.6654 7.78133 14.6603 8 14.65"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.33333 5.33203C10.2789 5.33203 12.6667 4.4366 12.6667 3.33203C12.6667 2.22746 10.2789 1.33203 7.33333 1.33203C4.38781 1.33203 2 2.22746 2 3.33203C2 4.4366 4.38781 5.33203 7.33333 5.33203Z"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.66797 5.33203V6.66536M4.66797 9.9987V11.332M13.326 9.33203L13.6593 10.7214L13.106 10.376C12.6993 10.0665 12.2023 9.89905 11.6913 9.89936C10.39 9.89936 9.33464 10.966 9.33464 12.2827C9.33464 13.5987 10.39 14.6654 11.6913 14.6654C12.8313 14.6654 13.7826 13.8467 14.0013 12.7587"
        stroke="#FF3E00"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  AuditIcon: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12.6667 7.33203V6.66536C12.6667 4.15136 12.6667 2.89403 11.8853 2.11336C11.104 1.3327 9.84733 1.33203 7.33333 1.33203C4.81933 1.33203 3.562 1.33203 2.78133 2.11336C2.00067 2.8947 2 4.15136 2 6.66536V9.33203C2 11.846 2 13.1034 2.78133 13.884C3.56267 14.6647 4.81933 14.6654 7.33333 14.6654"
        stroke="#23BD33"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.0013 14.6641L12.8586 13.5214M4.66797 4.66406H10.0013M4.66797 7.33073H7.33464M13.2393 11.6167C13.2445 11.9202 13.1892 12.2216 13.0767 12.5035C12.9641 12.7853 12.7966 13.0419 12.5838 13.2584C12.3711 13.4748 12.1173 13.6467 11.8375 13.764C11.5576 13.8813 11.2571 13.9418 10.9536 13.9418C10.6501 13.9418 10.3497 13.8813 10.0698 13.764C9.78993 13.6467 9.5362 13.4748 9.32345 13.2584C9.11069 13.0419 8.94315 12.7853 8.83062 12.5035C8.71808 12.2216 8.66279 11.9202 8.66797 11.6167C8.67821 11.0173 8.92352 10.4459 9.35105 10.0256C9.77858 9.60528 10.3541 9.36977 10.9536 9.36977C11.5532 9.36977 12.1287 9.60528 12.5562 10.0256C12.9838 10.4459 13.2291 11.0173 13.2393 11.6167Z"
        stroke="#23BD33"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  EditIcon: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.0013 3.9985L12.0013 5.9985M8.66797 13.3318H14.0013M3.33464 10.6652L2.66797 13.3318L5.33464 12.6652L13.0586 4.94116C13.3086 4.69113 13.449 4.35205 13.449 3.9985C13.449 3.64494 13.3086 3.30587 13.0586 3.05583L12.944 2.94116C12.6939 2.6912 12.3549 2.55078 12.0013 2.55078C11.6478 2.55078 11.3087 2.6912 11.0586 2.94116L3.33464 10.6652Z"
        stroke="#4A35CB"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  FixIcon: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.3337 4.66797L13.9603 3.29464L15.3337 2.66797L13.9603 2.0413L13.3337 0.667969L12.707 2.0413L11.3337 2.66797L12.707 3.29464L13.3337 4.66797ZM11.807 6.0813L9.92033 4.19464C9.78699 4.06797 9.62033 4.0013 9.44699 4.0013C9.27366 4.0013 9.10699 4.06797 8.97366 4.19464L1.52699 11.6413C1.46519 11.703 1.41616 11.7762 1.38271 11.8569C1.34925 11.9375 1.33203 12.024 1.33203 12.1113C1.33203 12.1986 1.34925 12.2851 1.38271 12.3657C1.41616 12.4464 1.46519 12.5196 1.52699 12.5813L3.41366 14.468C3.54699 14.6013 3.71366 14.668 3.88699 14.668C4.06033 14.668 4.22699 14.6013 4.36033 14.4746L11.807 7.02797C12.067 6.76797 12.067 6.3413 11.807 6.0813ZM9.44699 5.61464L10.387 6.55464L9.60699 7.33464L8.66699 6.39464L9.44699 5.61464ZM3.88699 13.0613L2.94699 12.1213L7.72699 7.33464L8.66699 8.27464L3.88699 13.0613Z"
        fill="#D409A2"
      />
    </svg>
  ),
  AgreementFinalized: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3 2.5C3 2.23478 3.10536 1.98043 3.29289 1.79289C3.48043 1.60536 3.73478 1.5 4 1.5H8V5C8 5.39782 8.15804 5.77936 8.43934 6.06066C8.72064 6.34196 9.10218 6.5 9.5 6.5H11.158L11.6535 6H9.5C9.23478 6 8.98043 5.89464 8.79289 5.70711C8.60536 5.51957 8.5 5.26522 8.5 5V1.5365C8.66617 1.58267 8.81757 1.67103 8.9395 1.793L12.536 5.3895C12.7215 5.3195 12.9145 5.276 13.109 5.258L13.0605 5.207L9.293 1.4395C9.01176 1.15818 8.63029 1.00008 8.2325 1H4C3.60218 1 3.22064 1.15804 2.93934 1.43934C2.65804 1.72064 2.5 2.10218 2.5 2.5V13.5C2.5 13.8978 2.65804 14.2794 2.93934 14.5607C3.22064 14.842 3.60218 15 4 15H12C12.3978 15 12.7794 14.842 13.0607 14.5607C13.342 14.2794 13.5 13.8978 13.5 13.5V10.3365L13 10.8325V13.5C13 13.7652 12.8946 14.0196 12.7071 14.2071C12.5196 14.3946 12.2652 14.5 12 14.5H4C3.73478 14.5 3.48043 14.3946 3.29289 14.2071C3.10536 14.0196 3 13.7652 3 13.5V2.5ZM14.322 8.462L10.017 12.7335C9.84109 12.9076 9.62215 13.032 9.3825 13.094L7.8725 13.483C7.83249 13.4934 7.79134 13.4988 7.75 13.499V13.5H4.25C4.1837 13.5 4.12011 13.4737 4.07322 13.4268C4.02634 13.3799 4 13.3163 4 13.25C4 13.1837 4.02634 13.1201 4.07322 13.0732C4.12011 13.0263 4.1837 13 4.25 13H7.248C7.248 12.9583 7.2535 12.9162 7.2645 12.8735L7.6595 11.3475C7.7185 11.12 7.837 10.912 8.0025 10.745L12.286 6.425C12.4222 6.28771 12.5847 6.17927 12.7637 6.10613C12.9427 6.033 13.1347 5.99668 13.3281 5.99934C13.5214 6.002 13.7123 6.04359 13.8892 6.12161C14.0662 6.19964 14.2256 6.31252 14.358 6.4535C14.6148 6.727 14.7548 7.08994 14.7481 7.46506C14.7413 7.84019 14.5885 8.19788 14.322 8.462Z"
        fill="#FF3E00"
      />
    </svg>
  ),
  TrendUp: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.66699 2.66665L6.90299 2.43065L6.66699 2.19531L6.43099 2.43065L6.66699 2.66665ZM12.0003 13.6666C12.0887 13.6666 12.1735 13.6315 12.236 13.569C12.2985 13.5065 12.3337 13.4217 12.3337 13.3333C12.3337 13.2449 12.2985 13.1601 12.236 13.0976C12.1735 13.0351 12.0887 13 12.0003 13V13.6666ZM10.2363 5.76398L6.90299 2.43065L6.43099 2.90265L9.76432 6.23598L10.2363 5.76398ZM6.43099 2.43065L3.09766 5.76398L3.56966 6.23598L6.90299 2.90265L6.43099 2.43065ZM6.33366 2.66665V9.33331H7.00032V2.66665H6.33366ZM10.667 13.6666H12.0003V13H10.667V13.6666ZM6.33366 9.33331C6.33366 10.4826 6.7902 11.5848 7.60286 12.3974C8.41552 13.2101 9.51772 13.6666 10.667 13.6666V13C9.69453 13 8.7619 12.6137 8.07426 11.926C7.38663 11.2384 7.00032 10.3058 7.00032 9.33331H6.33366Z"
        fill="#23BD33"
      />
    </svg>
  ),
  TrendStable: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="17"
      height="16"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.16545 8.44723L5.45504 10.3667L4.70073 11L1.5 8.31664V7.68336L4.70073 5L5.45504 5.63327L3.16545 7.55277H13.8346L11.545 5.63327L12.2993 5L15.5 7.68336V8.31664L12.2993 11L11.545 10.3676L13.8346 8.44723H3.16545Z"
        fill="#4A35CB"
      />
    </svg>
  ),
  TrendDown: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.33464 13.332L9.09864 13.568L9.33464 13.8034L9.57064 13.568L9.33464 13.332ZM4.0013 2.33203C3.9129 2.33203 3.82811 2.36715 3.7656 2.42966C3.70309 2.49217 3.66797 2.57696 3.66797 2.66536C3.66797 2.75377 3.70309 2.83855 3.7656 2.90107C3.82811 2.96358 3.9129 2.9987 4.0013 2.9987V2.33203ZM5.7653 10.2347L9.09864 13.568L9.57064 13.096L6.2373 9.7627L5.7653 10.2347ZM9.57064 13.568L12.904 10.2347L12.432 9.7627L9.09864 13.096L9.57064 13.568ZM9.66797 13.332V6.66536H9.0013V13.332H9.66797ZM5.33464 2.33203H4.0013V2.9987H5.33464V2.33203ZM9.66797 6.66536C9.66797 5.51609 9.21142 4.41389 8.39877 3.60124C7.58611 2.78858 6.48391 2.33203 5.33464 2.33203V2.9987C6.3071 2.9987 7.23973 3.38501 7.92736 4.07264C8.61499 4.76027 9.0013 5.6929 9.0013 6.66536H9.66797Z"
        fill="#FBA320"
      />
    </svg>
  ),
  BellNotification: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="17"
      height="16"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.57138 1.78137C7.17096 1.48473 7.83109 1.33094 8.50004 1.33204C10.9847 1.33204 13 3.4227 13 6.00204V6.47204C12.9992 7.03307 13.1593 7.58257 13.4614 8.05537L14.2 9.2047C14.874 10.2547 14.3594 11.682 13.1867 12.014C10.1225 12.8818 6.87757 12.8818 3.81337 12.014C2.64071 11.682 2.12604 10.2547 2.80004 9.20537L3.53871 8.05537C3.84074 7.58257 4.00084 7.03307 4.00004 6.47204V6.00204C4.00004 5.28604 4.15537 4.60737 4.43337 4.0007M5.50004 12.6654C5.93671 13.8307 7.11471 14.6654 8.50004 14.6654C8.66315 14.6654 8.82315 14.6543 8.98004 14.632M11.5 12.6654C11.3195 13.1413 11.0208 13.5634 10.632 13.892"
        stroke="#FBA320"
        strokeLinecap="round"
      />
    </svg>
  ),
  FileText: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.12878 47.9997C0.818232 47.9997 0.566406 47.7474 0.566406 47.4373V14.9807C0.566406 13.3941 1.85695 12.1035 3.44355 12.1035H13.7898C14.1004 12.1035 14.3522 12.3556 14.3522 12.6659C14.3522 12.9764 14.1001 13.2283 13.7898 13.2283H3.44381C2.47741 13.2283 1.69154 14.0141 1.69154 14.9805V47.4372C1.69154 47.7474 1.43998 47.9997 1.12878 47.9997ZM40.0198 22.7292C39.7093 22.7292 39.4575 22.4771 39.4575 22.1668C39.4575 19.6897 38.967 17.89 36.573 17.1567C36.2759 17.0658 36.1091 16.7513 36.1999 16.4539C36.2913 16.1568 36.607 15.9908 36.9027 16.0808C40.1452 17.0743 40.5827 19.7147 40.5827 22.1668C40.5825 22.4771 40.3307 22.7292 40.02 22.7292H40.0198Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M40.0196 47.9996H1.12851C0.947595 47.9996 0.777446 47.9121 0.67192 47.7652C0.566138 47.618 0.538057 47.4293 0.596013 47.2573L7.54007 26.6161C8.26349 24.464 9.58583 21.6035 12.7026 21.6035H44.6492C45.7617 21.6035 46.5584 21.9173 47.0177 22.5357C47.7247 23.4884 47.4251 24.9005 47.0849 26.0244L40.5584 47.5995C40.4863 47.837 40.2677 47.9996 40.0201 47.9996H40.0196ZM1.91156 46.8742H39.6014L46.0074 25.6984C46.2505 24.8946 46.5259 23.7621 46.1131 23.2064C45.878 22.8896 45.3852 22.7292 44.6487 22.7292H12.7024C10.8566 22.7292 9.5934 24.0388 8.60584 26.9751L1.9113 46.8742H1.91156Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M36.7359 22.7295C36.4252 22.7295 36.1735 22.4774 36.1735 22.1671V1.12539H14.3513V22.167C14.3513 22.4772 14.0992 22.7293 13.7889 22.7293C13.4784 22.7293 13.2266 22.4772 13.2266 22.167V0.562761C13.2266 0.25221 13.4786 0 13.7889 0H36.7364C37.0471 0 37.2988 0.252082 37.2988 0.562761V22.1671C37.2989 22.4774 37.0464 22.7295 36.7359 22.7295Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.1471 5.60132H18.3788C18.0682 5.60132 17.8164 5.34923 17.8164 5.03894C17.8164 4.72839 18.0685 4.47656 18.3788 4.47656H32.1474C32.4581 4.47656 32.7098 4.72864 32.7098 5.03894C32.7098 5.34949 32.4574 5.60132 32.1468 5.60132H32.1471Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.1471 9.81811H18.3788C18.0682 9.81811 17.8164 9.56603 17.8164 9.25574C17.8164 8.94518 18.0685 8.69336 18.3788 8.69336H32.1474C32.4581 8.69336 32.7098 8.94544 32.7098 9.25574C32.7098 9.56629 32.4574 9.81811 32.1468 9.81811H32.1471Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.1471 14.0369H18.3788C18.0682 14.0369 17.8164 13.7848 17.8164 13.4745C17.8164 13.1639 18.0685 12.9121 18.3788 12.9121H32.1474C32.4581 12.9121 32.7098 13.1642 32.7098 13.4745C32.7098 13.785 32.4574 14.0369 32.1468 14.0369H32.1471Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.1471 18.2537H18.3788C18.0682 18.2537 17.8164 18.0016 17.8164 17.6913C17.8164 17.3806 18.0685 17.1289 18.3788 17.1289H32.1474C32.4581 17.1289 32.7098 17.381 32.7098 17.6913C32.7098 18.0018 32.4574 18.2537 32.1468 18.2537H32.1471Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.8877 39.4853H12.6444C12.3339 39.4853 12.082 39.2329 12.082 38.9229V30.6796C12.082 30.3689 12.3341 30.1172 12.6444 30.1172H20.8877C21.1984 30.1172 21.4501 30.3695 21.4501 30.6796V38.9229C21.4501 39.2334 21.1987 39.4853 20.8877 39.4853ZM13.2068 38.3599H20.3251V31.2422H13.2068V38.3599Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M35.3748 31.783H25.6093C25.2986 31.783 25.0469 31.5306 25.0469 31.2206C25.0469 30.9103 25.2992 30.6582 25.6093 30.6582H35.3748C35.6855 30.6582 35.9372 30.9103 35.9372 31.2206C35.9373 31.5306 35.6855 31.783 35.3748 31.783Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M35.3748 35.365H25.6093C25.2986 35.365 25.0469 35.1129 25.0469 34.8026C25.0469 34.4926 25.2992 34.2402 25.6093 34.2402H35.3748C35.6855 34.2402 35.9372 34.4926 35.9372 34.8026C35.9373 35.1129 35.6855 35.365 35.3748 35.365Z"
        fill="#121212"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M35.3748 38.9451H25.6093C25.2986 38.9451 25.0469 38.6927 25.0469 38.3827C25.0469 38.0727 25.2992 37.8203 25.6093 37.8203H35.3748C35.6855 37.8203 35.9372 38.0727 35.9372 38.3827C35.9373 38.6926 35.6855 38.9451 35.3748 38.9451Z"
        fill="#121212"
      />
    </svg>
  ),
  Upload: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="70"
      height="60"
      viewBox="0 0 70 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M46.5986 5.70117C47.0854 5.70117 47.4803 6.09627 47.4805 6.58301C47.4805 7.06987 47.0855 7.46484 46.5986 7.46484C42.4863 7.46495 38.7956 10.2157 37.624 14.1523C37.4953 14.5864 37.0607 14.848 36.623 14.7686L36.6191 14.7734L36.5273 14.7461C36.0609 14.607 35.7945 14.1162 35.9336 13.6494C37.2973 9.06679 41.5446 5.70127 46.5986 5.70117Z"
        fill="#FF3E00"
        stroke="white"
        strokeWidth="0.3"
      />
      <path
        d="M56.8438 42.4384H52.4534C52.0494 42.4384 51.7217 42.1107 51.7217 41.7067C51.7217 41.3027 52.0494 40.9749 52.4534 40.9749H56.8438C62.8956 40.9749 67.8197 36.0509 67.8197 29.999C67.8197 23.9471 62.8956 19.023 56.8438 19.023H56.7382C56.526 19.023 56.3242 18.9311 56.1852 18.7706C56.0462 18.6101 55.9834 18.3974 56.0138 18.1873C56.0791 17.7315 56.112 17.2737 56.112 16.8279C56.112 11.5829 51.8444 7.31531 46.5995 7.31531C44.559 7.31531 42.6131 7.95296 40.9719 9.15978C40.6112 9.42478 40.099 9.30718 39.8905 8.91047C35.2425 0.0596993 23.1023 -1.12887 16.8082 6.57053C14.1568 9.81417 13.115 14.0336 13.9498 18.146C14.0418 18.6002 13.6942 19.0236 13.2327 19.0236H12.9395C6.8876 19.0236 1.96353 23.9477 1.96353 29.9996C1.96353 36.0514 6.8876 40.9755 12.9395 40.9755H17.3298C17.7338 40.9755 18.0615 41.3032 18.0615 41.7072C18.0615 42.1113 17.7338 42.439 17.3298 42.439H12.9395C6.0805 42.439 0.5 36.8585 0.5 29.9995C0.5 23.3329 5.77155 17.8742 12.3651 17.5731C11.7457 13.3066 12.9301 9.00295 15.6751 5.64437C22.4138 -2.5996 35.328 -1.67556 40.7871 7.51707C42.5287 6.42522 44.5215 5.85244 46.5992 5.85244C52.9538 5.85244 57.9892 11.261 57.5486 17.58C64.0813 17.9463 69.2829 23.3763 69.2829 29.999C69.2829 36.8585 63.7024 42.4384 56.8434 42.4384L56.8438 42.4384Z"
        fill="#FF3E00"
      />
      <path
        d="M34.7939 22.8496C44.9637 22.8499 53.2373 31.1242 53.2373 41.2939C53.2371 51.4634 44.9635 59.7371 34.7939 59.7373C24.6243 59.7373 16.3499 51.4637 16.3496 41.2939C16.3496 31.1241 24.624 22.8496 34.7939 22.8496ZM34.7939 24.6143C25.5969 24.6143 18.1143 32.097 18.1143 41.2939C18.1145 50.4908 25.5972 57.9736 34.7939 57.9736C43.9905 57.9734 51.4734 50.4905 51.4736 41.2939C51.4736 32.0971 43.9906 24.6145 34.7939 24.6143Z"
        fill="#FF3E00"
        stroke="white"
        strokeWidth="0.3"
      />
      <path
        d="M35.126 34.043C35.5044 34.0432 35.8115 34.351 35.8115 34.7295V48.6572C35.8115 49.0362 35.5043 49.3435 35.126 49.3438C34.7474 49.3438 34.4395 49.0359 34.4395 48.6572V34.7295C34.4395 34.3509 34.7473 34.043 35.126 34.043Z"
        fill="#FF3E00"
        stroke="#FF3E00"
        strokeWidth="0.3"
      />
      <path
        d="M34.6416 34.2461C34.9092 33.9784 35.3436 33.9784 35.6113 34.2461L39.8965 38.5322C40.1643 38.8001 40.1643 39.2341 39.8965 39.502L39.8955 39.501C39.7617 39.6351 39.5867 39.7021 39.4121 39.7021C39.237 39.7021 39.0605 39.6356 38.9268 39.502L35.126 35.7002L31.3252 39.502C31.0576 39.7696 30.6232 39.7696 30.3555 39.502C30.0877 39.2342 30.0878 38.8001 30.3555 38.5322L34.6416 34.2461Z"
        fill="#FF3E00"
        stroke="#FF3E00"
        strokeWidth="0.3"
      />
    </svg>
  ),
  BillingInfo: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7.179 3.5H12.821C13.363 3.5 13.801 3.5 14.154 3.529C14.519 3.559 14.839 3.622 15.135 3.772C15.6053 4.0119 15.9875 4.39451 16.227 4.865C16.378 5.161 16.441 5.481 16.471 5.845C16.5 6.2 16.5 6.637 16.5 7.179V10.25C16.5 10.3826 16.4473 10.5098 16.3536 10.6036C16.2598 10.6973 16.1326 10.75 16 10.75C15.8674 10.75 15.7402 10.6973 15.6464 10.6036C15.5527 10.5098 15.5 10.3826 15.5 10.25V7.2C15.5 6.632 15.5 6.236 15.474 5.927C15.45 5.625 15.404 5.451 15.336 5.319C15.1924 5.03666 14.9631 4.80702 14.681 4.663C14.549 4.596 14.376 4.55 14.073 4.526C13.764 4.5 13.368 4.5 12.8 4.5H7.2C6.632 4.5 6.236 4.5 5.927 4.526C5.625 4.55 5.451 4.596 5.319 4.663C5.03651 4.80685 4.80685 5.03651 4.663 5.319C4.596 5.451 4.55 5.625 4.526 5.927C4.5 6.236 4.5 6.632 4.5 7.2V16.8C4.5 17.368 4.5 17.765 4.526 18.073C4.55 18.375 4.596 18.549 4.663 18.681C4.80526 18.9598 5.03113 19.187 5.309 19.331L5.319 19.333C5.337 19.337 5.381 19.347 5.463 19.359C5.589 19.3777 5.754 19.3943 5.958 19.409C6.362 19.439 6.878 19.459 7.424 19.473C8.513 19.5 9.689 19.5 10.25 19.5C10.3826 19.5 10.5098 19.5527 10.6036 19.6464C10.6973 19.7402 10.75 19.8674 10.75 20C10.75 20.1326 10.6973 20.2598 10.6036 20.3536C10.5098 20.4473 10.3826 20.5 10.25 20.5H10.249C9.689 20.5 8.501 20.5 7.399 20.473C6.89356 20.4626 6.38843 20.4406 5.884 20.407C5.69471 20.3944 5.50592 20.375 5.318 20.349C5.16154 20.3323 5.00867 20.2912 4.865 20.227C4.39451 19.9875 4.0119 19.6053 3.772 19.135C3.622 18.839 3.559 18.519 3.529 18.155C3.5 17.8 3.5 17.362 3.5 16.82V7.18C3.5 6.638 3.5 6.2 3.529 5.847C3.559 5.482 3.622 5.162 3.772 4.866C4.01173 4.39535 4.39435 4.01273 4.865 3.773C5.161 3.623 5.481 3.56 5.845 3.53C6.2 3.5 6.637 3.5 7.179 3.5Z"
        fill="currentColor"
      />
      <path
        d="M18.62 12.5C19.023 12.5 19.355 12.5 19.626 12.522C19.907 12.545 20.166 12.594 20.408 12.718C20.7843 12.9097 21.0903 13.2157 21.282 13.592C21.406 13.835 21.455 14.093 21.478 14.374C21.5 14.644 21.5 14.977 21.5 15.379V17.621C21.5 18.023 21.5 18.355 21.478 18.626C21.455 18.907 21.406 19.166 21.282 19.408C21.0903 19.7843 20.7843 20.0903 20.408 20.282C20.165 20.406 19.907 20.455 19.626 20.478C19.356 20.5 19.023 20.5 18.621 20.5H14.379C13.977 20.5 13.645 20.5 13.374 20.478C13.093 20.455 12.834 20.406 12.592 20.282C12.2157 20.0903 11.9097 19.7843 11.718 19.408C11.594 19.165 11.545 18.907 11.522 18.626C11.5 18.356 11.5 18.023 11.5 17.621V15.379C11.5 14.977 11.5 14.645 11.522 14.374C11.545 14.093 11.594 13.834 11.718 13.592C11.9097 13.2157 12.2157 12.9097 12.592 12.718C12.835 12.594 13.093 12.545 13.374 12.522C13.644 12.5 13.977 12.5 14.379 12.5H18.62ZM13.456 13.519C13.236 13.537 13.124 13.569 13.046 13.609C12.8578 13.7049 12.7049 13.8578 12.609 14.046C12.569 14.124 12.537 14.236 12.519 14.456L12.515 14.5H20.485L20.481 14.456C20.463 14.236 20.431 14.124 20.391 14.046C20.2951 13.8578 20.1422 13.7049 19.954 13.609C19.876 13.569 19.764 13.537 19.544 13.519C19.2297 13.5012 18.9148 13.4949 18.6 13.5H14.4C13.972 13.5 13.68 13.5 13.456 13.519ZM20.5 16.5H12.5V17.6C12.5 18.028 12.5 18.32 12.519 18.544C12.537 18.764 12.569 18.876 12.609 18.954C12.7049 19.1422 12.8578 19.2951 13.046 19.391C13.124 19.431 13.236 19.463 13.456 19.481C13.681 19.5 13.972 19.5 14.4 19.5H18.6C19.028 19.5 19.32 19.5 19.544 19.481C19.764 19.463 19.876 19.431 19.954 19.391C20.1422 19.2951 20.2951 19.1422 20.391 18.954C20.431 18.876 20.463 18.764 20.481 18.544C20.5 18.319 20.5 18.028 20.5 17.6V16.5ZM6.5 6.5C6.36739 6.5 6.24021 6.55268 6.14645 6.64645C6.05268 6.74021 6 6.86739 6 7C6 7.13261 6.05268 7.25979 6.14645 7.35355C6.24021 7.44732 6.36739 7.5 6.5 7.5H10.5C10.6326 7.5 10.7598 7.44732 10.8536 7.35355C10.9473 7.25979 11 7.13261 11 7C11 6.86739 10.9473 6.74021 10.8536 6.64645C10.7598 6.55268 10.6326 6.5 10.5 6.5H6.5ZM6 10C6 9.86739 6.05268 9.74021 6.14645 9.64645C6.24021 9.55268 6.36739 9.5 6.5 9.5H13.5C13.6326 9.5 13.7598 9.55268 13.8536 9.64645C13.9473 9.74021 14 9.86739 14 10C14 10.1326 13.9473 10.2598 13.8536 10.3536C13.7598 10.4473 13.6326 10.5 13.5 10.5H6.5C6.36739 10.5 6.24021 10.4473 6.14645 10.3536C6.05268 10.2598 6 10.1326 6 10ZM6.5 12.5C6.36739 12.5 6.24021 12.5527 6.14645 12.6464C6.05268 12.7402 6 12.8674 6 13C6 13.1326 6.05268 13.2598 6.14645 13.3536C6.24021 13.4473 6.36739 13.5 6.5 13.5H10C10.1326 13.5 10.2598 13.4473 10.3536 13.3536C10.4473 13.2598 10.5 13.1326 10.5 13C10.5 12.8674 10.4473 12.7402 10.3536 12.6464C10.2598 12.5527 10.1326 12.5 10 12.5H6.5ZM6 17C6 16.8674 6.05268 16.7402 6.14645 16.6464C6.24021 16.5527 6.36739 16.5 6.5 16.5H8.5C8.63261 16.5 8.75979 16.5527 8.85355 16.6464C8.94732 16.7402 9 16.8674 9 17C9 17.1326 8.94732 17.2598 8.85355 17.3536C8.75979 17.4473 8.63261 17.5 8.5 17.5H6.5C6.36739 17.5 6.24021 17.4473 6.14645 17.3536C6.05268 17.2598 6 17.1326 6 17Z"
        fill="currentColor"
      />
    </svg>
  ),
  Users: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M22.5 21C22.5 21 24 21 24 19.5C24 18 22.5 13.5 16.5 13.5C10.5 13.5 9 18 9 19.5C9 21 10.5 21 10.5 21H22.5ZM10.533 19.5L10.5 19.494C10.5015 19.098 10.7505 17.949 11.64 16.914C12.468 15.9435 13.923 15 16.5 15C19.0755 15 20.5305 15.945 21.36 16.914C22.2495 17.949 22.497 19.0995 22.5 19.494L22.488 19.497L22.467 19.5H10.533ZM16.5 10.5C17.2956 10.5 18.0587 10.1839 18.6213 9.62132C19.1839 9.05871 19.5 8.29565 19.5 7.5C19.5 6.70435 19.1839 5.94129 18.6213 5.37868C18.0587 4.81607 17.2956 4.5 16.5 4.5C15.7044 4.5 14.9413 4.81607 14.3787 5.37868C13.8161 5.94129 13.5 6.70435 13.5 7.5C13.5 8.29565 13.8161 9.05871 14.3787 9.62132C14.9413 10.1839 15.7044 10.5 16.5 10.5ZM21 7.5C21 8.09095 20.8836 8.67611 20.6575 9.22208C20.4313 9.76804 20.0998 10.2641 19.682 10.682C19.2641 11.0998 18.768 11.4313 18.2221 11.6575C17.6761 11.8836 17.0909 12 16.5 12C15.9091 12 15.3239 11.8836 14.7779 11.6575C14.232 11.4313 13.7359 11.0998 13.318 10.682C12.9002 10.2641 12.5687 9.76804 12.3425 9.22208C12.1164 8.67611 12 8.09095 12 7.5C12 6.30653 12.4741 5.16193 13.318 4.31802C14.1619 3.47411 15.3065 3 16.5 3C17.6935 3 18.8381 3.47411 19.682 4.31802C20.5259 5.16193 21 6.30653 21 7.5ZM10.404 13.92C9.80364 13.7324 9.18526 13.6082 8.559 13.5495C8.20706 13.5152 7.85361 13.4987 7.5 13.5C1.5 13.5 0 18 0 19.5C0 20.5 0.5 21 1.5 21H7.824C7.60174 20.5317 7.49084 20.0183 7.5 19.5C7.5 17.985 8.0655 16.437 9.135 15.144C9.4995 14.703 9.924 14.2905 10.404 13.92ZM7.38 15C6.49279 16.3342 6.01327 17.8978 6 19.5H1.5C1.5 19.11 1.746 17.955 2.64 16.914C3.4575 15.96 4.878 15.03 7.38 15.0015V15ZM2.25 8.25C2.25 7.05653 2.72411 5.91193 3.56802 5.06802C4.41193 4.22411 5.55653 3.75 6.75 3.75C7.94347 3.75 9.08807 4.22411 9.93198 5.06802C10.7759 5.91193 11.25 7.05653 11.25 8.25C11.25 9.44347 10.7759 10.5881 9.93198 11.432C9.08807 12.2759 7.94347 12.75 6.75 12.75C5.55653 12.75 4.41193 12.2759 3.56802 11.432C2.72411 10.5881 2.25 9.44347 2.25 8.25ZM6.75 5.25C5.95435 5.25 5.19129 5.56607 4.62868 6.12868C4.06607 6.69129 3.75 7.45435 3.75 8.25C3.75 9.04565 4.06607 9.80871 4.62868 10.3713C5.19129 10.9339 5.95435 11.25 6.75 11.25C7.54565 11.25 8.30871 10.9339 8.87132 10.3713C9.43393 9.80871 9.75 9.04565 9.75 8.25C9.75 7.45435 9.43393 6.69129 8.87132 6.12868C8.30871 5.56607 7.54565 5.25 6.75 5.25Z"
        fill="currentColor"
      />
    </svg>
  ),
  Notification: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_15599_17039)">
        <path
          d="M21.6752 18.5536C21.0323 17.9805 20.4695 17.3235 20.0018 16.6003C19.4908 15.6022 19.1848 14.5118 19.1018 13.3936V10.1003C19.1062 8.344 18.4692 6.64658 17.3103 5.32691C16.1515 4.00724 14.5506 3.15617 12.8085 2.93359V2.07359C12.8085 1.83755 12.7147 1.61118 12.5478 1.44427C12.3809 1.27736 12.1545 1.18359 11.9185 1.18359C11.6824 1.18359 11.4561 1.27736 11.2892 1.44427C11.1223 1.61118 11.0285 1.83755 11.0285 2.07359V2.94693C9.30199 3.18555 7.72047 4.04176 6.57684 5.357C5.43321 6.67223 4.80498 8.35736 4.80849 10.1003V13.3936C4.72553 14.5118 4.41951 15.6022 3.90849 16.6003C3.44908 17.3218 2.89529 17.9788 2.26182 18.5536C2.19071 18.6161 2.13372 18.693 2.09463 18.7792C2.05555 18.8654 2.03528 18.9589 2.03516 19.0536V19.9603C2.03516 20.1371 2.10539 20.3066 2.23042 20.4317C2.35544 20.5567 2.52501 20.6269 2.70182 20.6269H21.2352C21.412 20.6269 21.5815 20.5567 21.7066 20.4317C21.8316 20.3066 21.9018 20.1371 21.9018 19.9603V19.0536C21.9017 18.9589 21.8814 18.8654 21.8423 18.7792C21.8033 18.693 21.7463 18.6161 21.6752 18.5536ZM3.42182 19.2936C4.04195 18.6943 4.58807 18.0228 5.04849 17.2936C5.69235 16.0882 6.06761 14.7578 6.14849 13.3936V10.1003C6.12205 9.31895 6.25311 8.54031 6.53385 7.81071C6.8146 7.0811 7.2393 6.41545 7.78265 5.85339C8.32601 5.29134 8.97692 4.84437 9.69661 4.53911C10.4163 4.23385 11.1901 4.07653 11.9718 4.07653C12.7536 4.07653 13.5273 4.23385 14.247 4.53911C14.9667 4.84437 15.6176 5.29134 16.161 5.85339C16.7043 6.41545 17.129 7.0811 17.4098 7.81071C17.6905 8.54031 17.8216 9.31895 17.7952 10.1003V13.3936C17.876 14.7578 18.2513 16.0882 18.8952 17.2936C19.3556 18.0228 19.9017 18.6943 20.5218 19.2936H3.42182Z"
          fill="currentColor"
        />
        <path
          d="M12.0015 22.854C12.4215 22.8443 12.8245 22.6864 13.1392 22.4083C13.454 22.1301 13.6603 21.7496 13.7215 21.334H10.2148C10.2778 21.7609 10.4937 22.1503 10.8224 22.4299C11.151 22.7095 11.5701 22.8602 12.0015 22.854Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_15599_17039">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  Group: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M19 9.99987C19.0028 11.7866 18.4714 13.5334 17.474 15.0159C16.6514 16.2429 15.5391 17.2483 14.2356 17.9432C12.932 18.638 11.4772 19.001 10 18.9999C8.52277 19.001 7.06804 18.638 5.76444 17.9432C4.46085 17.2483 3.34858 16.2429 2.526 15.0159C1.74273 13.8483 1.2439 12.5134 1.06951 11.1183C0.895109 9.72319 1.05 8.30662 1.52175 6.98215C1.9935 5.65768 2.769 4.46216 3.78607 3.49142C4.80314 2.52068 6.0335 1.80173 7.37851 1.39221C8.72352 0.982677 10.1458 0.893962 11.5312 1.13317C12.9167 1.37238 14.2269 1.93286 15.3567 2.76969C16.4865 3.60651 17.4046 4.69639 18.0373 5.95197C18.67 7.20755 18.9997 8.59389 19 9.99987Z"
        stroke="currentColor"
      />
      <path
        d="M11.5004 7C11.5004 7.39782 11.3423 7.77936 11.061 8.06066C10.7797 8.34196 10.3982 8.5 10.0004 8.5V9.5C10.3287 9.5 10.6538 9.43534 10.9571 9.3097C11.2604 9.18406 11.536 8.99991 11.7681 8.76777C12.0003 8.53562 12.1844 8.26002 12.3101 7.95671C12.4357 7.65339 12.5004 7.3283 12.5004 7H11.5004ZM10.0004 8.5C9.60255 8.5 9.22102 8.34196 8.93971 8.06066C8.65841 7.77936 8.50037 7.39782 8.50037 7H7.50037C7.50037 7.3283 7.56504 7.65339 7.69068 7.95671C7.81631 8.26002 8.00046 8.53562 8.23261 8.76777C8.70145 9.23661 9.33733 9.5 10.0004 9.5V8.5ZM8.50037 7C8.50037 6.60218 8.65841 6.22064 8.93971 5.93934C9.22102 5.65804 9.60255 5.5 10.0004 5.5V4.5C9.33733 4.5 8.70145 4.76339 8.23261 5.23223C7.76377 5.70107 7.50037 6.33696 7.50037 7H8.50037ZM10.0004 5.5C10.3982 5.5 10.7797 5.65804 11.061 5.93934C11.3423 6.22064 11.5004 6.60218 11.5004 7H12.5004C12.5004 6.33696 12.237 5.70107 11.7681 5.23223C11.2993 4.76339 10.6634 4.5 10.0004 4.5V5.5ZM3.16637 15.856L2.68637 15.714L2.60938 15.975L2.78637 16.182L3.16637 15.856ZM16.8344 15.856L17.2144 16.182L17.3914 15.975L17.3134 15.714L16.8344 15.856ZM7.00037 13.5H13.0004V12.5H7.00037V13.5ZM7.00037 12.5C6.03123 12.4999 5.08794 12.8126 4.31083 13.3917C3.53372 13.9708 2.96333 14.7853 2.68637 15.714L3.64537 15.999C3.86095 15.2767 4.30393 14.6434 4.90843 14.1931C5.51292 13.7428 6.24662 13.4997 7.00037 13.5V12.5ZM10.0004 18.5C8.77364 18.5013 7.56123 18.2364 6.4468 17.7237C5.33237 17.2109 4.34246 16.4625 3.54537 15.53L2.78637 16.182C3.67743 17.2238 4.78381 18.0599 6.02924 18.6327C7.27468 19.2055 8.62953 19.5014 10.0004 19.5V18.5ZM13.0004 13.5C13.7543 13.4998 14.4881 13.743 15.0926 14.1935C15.6971 14.6439 16.14 15.2775 16.3554 16L17.3134 15.714C17.0364 14.7853 16.467 13.9708 15.6899 13.3917C14.9128 12.8126 13.9695 12.4999 13.0004 12.5V13.5ZM16.4554 15.53C15.6583 16.4625 14.6684 17.2109 13.5539 17.7237C12.4395 18.2364 11.2271 18.5013 10.0004 18.5V19.5C11.3712 19.5014 12.7261 19.2055 13.9715 18.6327C15.2169 18.0599 16.3233 17.2238 17.2144 16.182L16.4554 15.53Z"
        fill="currentColor"
      />
    </svg>
  ),
  PriorityLow: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="16"
      height="10"
      viewBox="0 0 16 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.7 3.29727L8.81667 1.12227C8.1 0.805599 7.01667 0.805599 6.3 1.12227L1.41667 3.29727C0.183333 3.84727 0 4.59727 0 4.99727C0 5.39726 0.183333 6.14727 1.41667 6.69727L6.3 8.87226C6.65833 9.0306 7.10833 9.11393 7.55833 9.11393C8.00833 9.11393 8.45833 9.0306 8.81667 8.87226L13.7 6.69727C14.9333 6.14727 15.1167 5.39726 15.1167 4.99727C15.1167 4.59727 14.9417 3.84727 13.7 3.29727Z"
        fill="currentColor"
      />
    </svg>
  ),
  PriorityMedium: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="17"
      height="14"
      viewBox="0 0 17 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14.2078 3.14297L9.32448 0.967969C8.60781 0.651302 7.52448 0.651302 6.80781 0.967969L1.92448 3.14297C0.691146 3.69297 0.507812 4.44297 0.507812 4.84297C0.507812 5.24297 0.691146 5.99297 1.92448 6.54297L6.80781 8.71797C7.16615 8.8763 7.61615 8.95963 8.06615 8.95963C8.51615 8.95963 8.96615 8.8763 9.32448 8.71797L14.2078 6.54297C15.4411 5.99297 15.6245 5.24297 15.6245 4.84297C15.6245 4.44297 15.4495 3.69297 14.2078 3.14297Z"
        fill="currentColor"
      />
      <path
        d="M8.06667 13.2677C7.75 13.2677 7.43333 13.201 7.14167 13.076L1.525 10.576C0.666667 10.1927 0 9.16771 0 8.22604C0 7.88437 0.275 7.60938 0.616667 7.60938C0.958333 7.60938 1.23333 7.88437 1.23333 8.22604C1.23333 8.67604 1.60833 9.25937 2.025 9.44271L7.64167 11.9427C7.90833 12.0594 8.21667 12.0594 8.48333 11.9427L14.1 9.44271C14.5167 9.25937 14.8917 8.68437 14.8917 8.22604C14.8917 7.88437 15.1667 7.60938 15.5083 7.60938C15.85 7.60938 16.125 7.88437 16.125 8.22604C16.125 9.15937 15.4583 10.1927 14.6 10.576L8.98333 13.076C8.7 13.201 8.38333 13.2677 8.06667 13.2677Z"
        fill="currentColor"
      />
    </svg>
  ),
  PriorityHigh: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="17"
      height="20"
      viewBox="0 0 17 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14.2078 3.31484L9.32448 1.13984C8.60781 0.823177 7.52448 0.823177 6.80781 1.13984L1.92448 3.31484C0.691146 3.86484 0.507812 4.61484 0.507812 5.01484C0.507812 5.41484 0.691146 6.16484 1.92448 6.71484L6.80781 8.88984C7.16615 9.04818 7.61615 9.13151 8.06615 9.13151C8.51615 9.13151 8.96615 9.04818 9.32448 8.88984L14.2078 6.71484C15.4411 6.16484 15.6245 5.41484 15.6245 5.01484C15.6245 4.61484 15.4495 3.86484 14.2078 3.31484Z"
        fill="currentColor"
      />
      <path
        d="M8.06667 13.4396C7.75 13.4396 7.43333 13.3729 7.14167 13.2479L1.525 10.7479C0.6 10.3396 0 9.41458 0 8.39792C0 8.05625 0.275 7.78125 0.616667 7.78125C0.958333 7.78125 1.23333 8.05625 1.23333 8.39792C1.23333 8.92292 1.54167 9.39792 2.025 9.61458L7.64167 12.1146C7.90833 12.2313 8.21667 12.2313 8.48333 12.1146L14.1 9.61458C14.575 9.40625 14.8917 8.92292 14.8917 8.39792C14.8917 8.05625 15.1667 7.78125 15.5083 7.78125C15.85 7.78125 16.125 8.05625 16.125 8.39792C16.125 9.41458 15.525 10.3313 14.6 10.7479L8.98333 13.2479C8.7 13.3729 8.38333 13.4396 8.06667 13.4396Z"
        fill="currentColor"
      />
      <path
        d="M8.06667 19.0978C7.75 19.0978 7.43333 19.0311 7.14167 18.9061L1.525 16.4061C0.6 15.9978 0 15.0728 0 14.0561C0 13.7145 0.275 13.4395 0.616667 13.4395C0.958333 13.4395 1.23333 13.7145 1.23333 14.0561C1.23333 14.5811 1.54167 15.0561 2.025 15.2728L7.64167 17.7728C7.90833 17.8895 8.21667 17.8895 8.48333 17.7728L14.1 15.2728C14.575 15.0645 14.8917 14.5811 14.8917 14.0561C14.8917 13.7145 15.1667 13.4395 15.5083 13.4395C15.85 13.4395 16.125 13.7145 16.125 14.0561C16.125 15.0728 15.525 15.9895 14.6 16.4061L8.98333 18.9061C8.7 19.0311 8.38333 19.0978 8.06667 19.0978Z"
        fill="currentColor"
      />
    </svg>
  ),
  AssignedByIcon: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4.9987 5.00065H8.33203V3.33398H3.33203V8.33398H4.9987V5.00065ZM8.33203 15.0007H4.9987V11.6673H3.33203V16.6673H8.33203V15.0007ZM11.6654 5.00065H14.9987V8.33398H16.6654V3.33398H11.6654V5.00065ZM11.6654 15.0007H14.9987V11.6673H16.6654V16.6673H11.6654V15.0007ZM9.9987 7.08398C9.61568 7.08398 9.2364 7.15943 8.88254 7.306C8.52867 7.45258 8.20714 7.66742 7.9363 7.93826C7.66547 8.20909 7.45063 8.53062 7.30405 8.88449C7.15747 9.23836 7.08203 9.61763 7.08203 10.0007C7.08203 10.3837 7.15747 10.7629 7.30405 11.1168C7.45063 11.4707 7.66547 11.7922 7.9363 12.063C8.20714 12.3339 8.52867 12.5487 8.88254 12.6953C9.2364 12.8419 9.61568 12.9173 9.9987 12.9173C10.7722 12.9173 11.5141 12.61 12.0611 12.063C12.6081 11.5161 12.9154 10.7742 12.9154 10.0007C12.9154 9.2271 12.6081 8.48524 12.0611 7.93826C11.5141 7.39128 10.7722 7.08398 9.9987 7.08398Z"
        fill="#FF3E00"
      />
    </svg>
  ),
  TypeIcon: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.9856 19.0004L11.5006 13.5164V5.95239L8.40059 9.05239L7.68759 8.33939L12.0006 4.02539L16.3086 8.33339L15.5946 9.04639L12.5006 5.95239V13.1004L17.6926 18.2924L16.9856 19.0004ZM7.01559 19.0064L6.30859 18.2984L9.88759 14.7134L10.6006 15.4274L7.01559 19.0064Z"
        fill="#FF3E00"
      />
    </svg>
  ),
  EscalationPolicyIcon: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12 20.9612C9.99067 20.3645 8.32167 19.1475 6.993 17.3102C5.66433 15.4728 5 13.4025 5 11.0992V5.69117L12 3.07617L19 5.69117V11.0992C19 12.2212 18.8257 13.3208 18.477 14.3982C18.1283 15.4762 17.5873 16.5085 16.854 17.4952L13.769 14.4112C13.5203 14.6072 13.2443 14.7542 12.941 14.8522C12.6377 14.9502 12.324 14.9992 12 14.9992C11.1753 14.9992 10.469 14.7055 9.881 14.1182C9.293 13.5308 8.99933 12.8245 9 11.9992C9.00067 11.1738 9.29433 10.4675 9.881 9.88017C10.4677 9.29284 11.174 8.99917 12 8.99917C12.826 8.99917 13.5323 9.29284 14.119 9.88017C14.7057 10.4675 14.9993 11.1738 15 11.9992C15 12.3078 14.9543 12.6058 14.863 12.8932C14.771 13.1805 14.6463 13.4505 14.489 13.7032L16.719 15.9332C17.1037 15.2118 17.4133 14.4538 17.648 13.6592C17.8827 12.8638 18 12.0105 18 11.0992V6.37417L12 4.14417L6 6.37417V11.0992C6 13.1158 6.56667 14.9492 7.7 16.5992C8.83333 18.2492 10.2667 19.3492 12 19.8992C12.4333 19.7658 12.846 19.5952 13.238 19.3872C13.63 19.1792 14.0173 18.9332 14.4 18.6492L15.03 19.3952C14.532 19.7552 14.0487 20.0605 13.58 20.3112C13.1113 20.5612 12.5847 20.7778 12 20.9612ZM12 13.9992C12.55 13.9992 13.021 13.8035 13.413 13.4122C13.805 13.0208 14.0007 12.5498 14 11.9992C13.9993 11.4485 13.8037 10.9778 13.413 10.5872C13.0223 10.1965 12.5513 10.0005 12 9.99917C11.4487 9.99784 10.978 10.1938 10.588 10.5872C10.198 10.9805 10.002 11.4512 10 11.9992C9.998 12.5472 10.194 13.0182 10.588 13.4122C10.982 13.8062 11.4527 14.0018 12 13.9992Z"
        fill="#FF3E00"
      />
    </svg>
  ),
  PasswordView: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="22"
      height="16"
      viewBox="0 0 22 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14.0027 8C14.0027 9.65685 12.6595 11 11.0027 11C9.34583 11 8.00269 9.65685 8.00269 8C8.00269 6.34315 9.34583 5 11.0027 5C12.6595 5 14.0027 6.34315 14.0027 8Z"
        stroke="#BAB9B9"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.46094 7.99997C2.73521 3.94288 6.5255 1 11.0031 1C15.4808 1 19.2711 3.94291 20.5453 8.00004C19.2711 12.0571 15.4808 15 11.0031 15C6.52549 15 2.73519 12.0571 1.46094 7.99997Z"
        stroke="#BAB9B9"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  PasswordHide: (props: SVGProps<SVGSVGElement>) => (
    <svg
      width="22"
      height="19"
      viewBox="0 0 22 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.83 6L14 9.16V9C14 8.20435 13.6839 7.44129 13.1213 6.87868C12.5587 6.31607 11.7956 6 11 6H10.83ZM6.53 6.8L8.08 8.35C8.03 8.56 8 8.77 8 9C8 9.79565 8.31607 10.5587 8.87868 11.1213C9.44129 11.6839 10.2044 12 11 12C11.22 12 11.44 11.97 11.65 11.92L13.2 13.47C12.53 13.8 11.79 14 11 14C9.67392 14 8.40215 13.4732 7.46447 12.5355C6.52678 11.5979 6 10.3261 6 9C6 8.21 6.2 7.47 6.53 6.8ZM1 1.27L3.28 3.55L3.73 4C2.08 5.3 0.78 7 0 9C1.73 13.39 6 16.5 11 16.5C12.55 16.5 14.03 16.2 15.38 15.66L15.81 16.08L18.73 19L20 17.73L2.27 0M11 4C12.3261 4 13.5979 4.52678 14.5355 5.46447C15.4732 6.40215 16 7.67392 16 9C16 9.64 15.87 10.26 15.64 10.82L18.57 13.75C20.07 12.5 21.27 10.86 22 9C20.27 4.61 16 1.5 11 1.5C9.6 1.5 8.26 1.75 7 2.2L9.17 4.35C9.74 4.13 10.35 4 11 4Z"
        fill="#112244"
      />
    </svg>
  ),
  SearchX: (props: SVGProps<SVGSVGElement>) => (
    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M14.5 14.5L20.5 20.5M20.5 14.5L14.5 20.5M27.786 27.84L33.946 34M32 17C32 20.9782 30.4196 24.7936 27.6066 27.6066C24.7936 30.4196 20.9782 32 17 32C13.0218 32 9.20644 30.4196 6.3934 27.6066C3.58035 24.7936 2 20.9782 2 17C2 13.0218 3.58035 9.20644 6.3934 6.3934C9.20644 3.58035 13.0218 2 17 2C20.9782 2 24.7936 3.58035 27.6066 6.3934C30.4196 9.20644 32 13.0218 32 17Z" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
    
  ),
  FileEmpty: (props: SVGProps<SVGSVGElement>) => (
    <svg width="36" height="44" viewBox="0 0 36 44" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M9.99219 28H16.8502M9.99219 20H25.9922M22.9922 2H12.9922C12.1965 2 11.4335 2.31607 10.8709 2.87868C10.3083 3.44129 9.99219 4.20435 9.99219 5C9.99219 5.79565 10.3083 6.55871 10.8709 7.12132C11.4335 7.68393 12.1965 8 12.9922 8H22.9922C23.7878 8 24.5509 7.68393 25.1135 7.12132C25.6761 6.55871 25.9922 5.79565 25.9922 5C25.9922 4.20435 25.6761 3.44129 25.1135 2.87868C24.5509 2.31607 23.7878 2 22.9922 2Z" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M25.9922 5C29.0982 5.094 30.9522 5.44 32.2342 6.724C33.9942 8.48 33.9942 11.31 33.9942 16.964V30C33.9942 35.656 33.9942 38.484 32.2342 40.242C30.4782 42 27.6482 42 21.9942 42H13.9942C8.33419 42 5.50619 42 3.75019 40.242C1.99419 38.484 1.99219 35.656 1.99219 30V16.966C1.99219 11.31 1.99219 8.48 3.75019 6.724C5.03219 5.44 6.88619 5.094 9.99219 5" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
    
  ),
  Record: (props: SVGProps<SVGSVGElement>) => (
    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      {...props}
                    >
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        fill="white"
                      />
                      <rect
                        x="5.5"
                        y="5.5"
                        width="21"
                        height="21"
                        rx="10.5"
                        stroke="#FF3E00"
                        strokeWidth="11"
                      />
                    </svg>
  ),
  HeartbeatActive: (props: SVGProps<SVGSVGElement>) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M7.5 4C6.04131 4 4.64236 4.57946 3.61091 5.61091C2.57946 6.64236 2 8.04131 2 9.5C2 10 2.09 10.5 2.22 11H6.3L7.57 7.63C7.87 6.83 9.05 6.75 9.43 7.63L11.5 13L12.09 11.58C12.22 11.25 12.57 11 13 11H21.78C21.91 10.5 22 10 22 9.5C22 8.04131 21.4205 6.64236 20.3891 5.61091C19.3576 4.57946 17.9587 4 16.5 4C14.64 4 13 4.93 12 6.34C11 4.93 9.36 4 7.5 4ZM3 12.5C2.73478 12.5 2.48043 12.6054 2.29289 12.7929C2.10536 12.9804 2 13.2348 2 13.5C2 13.7652 2.10536 14.0196 2.29289 14.2071C2.48043 14.3946 2.73478 14.5 3 14.5H5.44L11 20C12 20.9 12 20.9 13 20L18.56 14.5H21C21.2652 14.5 21.5196 14.3946 21.7071 14.2071C21.8946 14.0196 22 13.7652 22 13.5C22 13.2348 21.8946 12.9804 21.7071 12.7929C21.5196 12.6054 21.2652 12.5 21 12.5H13.4L12.47 14.8C12.07 15.81 10.92 15.67 10.55 14.83L8.5 9.5L7.54 11.83C7.39 12.21 7.05 12.5 6.6 12.5H3Z" fill="#FF3E00"/>
    </svg>
    ),
  HeartbeatPaused: (props: SVGProps<SVGSVGElement>) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M12.1 18.55L12 18.65L11.89 18.55C7.14 14.24 4 11.39 4 8.5C4 6.5 5.5 5 7.5 5C9.04 5 10.54 6 11.07 7.36H12.93C13.46 6 14.96 5 16.5 5C18.5 5 20 6.5 20 8.5C20 11.39 16.86 14.24 12.1 18.55ZM16.5 3C14.76 3 13.09 3.81 12 5.08C10.91 3.81 9.24 3 7.5 3C4.42 3 2 5.41 2 8.5C2 12.27 5.4 15.36 10.55 20.03L12 21.35L13.45 20.03C18.6 15.36 22 12.27 22 8.5C22 5.41 19.58 3 16.5 3Z" fill="black"/>
    </svg>

    ),
};

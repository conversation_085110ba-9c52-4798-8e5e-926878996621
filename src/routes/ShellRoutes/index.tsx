import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

import { withSuspense } from '@/components/hocs/suspense/withSuspense';
import { retryChunkLoad } from '@/utils/chunkErrorHandler';

// Lazy load public pages with chunk error handling
const HomePage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/site/HomePage')))
);
const OurStoryPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/site/OurStoryPage')))
);

const LoginPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/auth/LoginPage')))
);

const SignupPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/auth/SignupPage')))
);

const ContactPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/site/ContactPage')))
);

const TermsAndConditionsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/site/TermsAndConditionPage'))
  )
);

const BlogPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/site/BlogPage')))
);

const AgentsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/site/AgentPage/Marketplace'))
  )
);

const AgentDetailsPage = withSuspense(
  lazy(() =>
    retryChunkLoad(() => import('../../pages/site/AgentPage/AgentDetails'))
  )
);

export const ShellRoutes = [
  {
    index: true,
    element: <HomePage />,
  },
  {
    path: 'our-story',
    element: <OurStoryPage />,
  },
  {
    path: 'agents',
    element: <AgentsPage />,
  },
  {
    path: 'agents/:agentId',
    element: <AgentDetailsPage />,
  },
  {
    path: 'login',
    element: <LoginPage />,
  },
  {
    path: 'signup',
    element: <SignupPage />,
  },
  {
    path: 'contact',
    element: <ContactPage />,
  },
  {
    path: 'terms',
    element: <TermsAndConditionsPage />,
  },
  {
    path: 'blog',
    element: <BlogPage />,
  },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
];

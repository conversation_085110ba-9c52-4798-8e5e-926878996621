// Styles
import 'react-toastify/dist/ReactToastify.css';
import './index.css';

import { ReactKeycloakProvider } from '@react-keycloak/web';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useEffect } from 'react';
import { ToastContainer } from 'react-toastify';

import keycloak from './config/keycloak';
import { queryClient } from './config/queryClient';
import { AuthProvider } from './context/AuthContext';
import { NotificationProvider } from './context/NotificationContext';
import { TenantProvider } from './context/TenantContext';
import { TimezoneProvider } from './context/TimezoneContext';
import MainAppRoutes from './routes';
import { setupChunkErrorHandler } from './utils/chunkErrorHandler';

function App() {
  useEffect(() => {
    // Set up global chunk error handler to handle failed dynamic imports
    setupChunkErrorHandler();
  }, []);

  return (
    <ReactKeycloakProvider authClient={keycloak}>
      <QueryClientProvider client={queryClient}>
        <TenantProvider>
          <AuthProvider>
            <TimezoneProvider>
              <NotificationProvider maxNotifications={5}>
                <MainAppRoutes />
                <ToastContainer
                  position="top-right"
                  autoClose={5000}
                  hideProgressBar={false}
                  newestOnTop={false}
                  closeOnClick
                  rtl={false}
                  pauseOnFocusLoss
                  draggable
                  pauseOnHover
                  theme="light"
                />
                <ReactQueryDevtools
                  initialIsOpen={false}
                  position="bottom-right"
                />
              </NotificationProvider>
            </TimezoneProvider>
          </AuthProvider>
        </TenantProvider>
      </QueryClientProvider>
    </ReactKeycloakProvider>
  );
}

export default App;

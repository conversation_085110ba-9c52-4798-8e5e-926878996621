import { StylesConfig } from 'react-select';

/**
 * Default react-select styles with primary color theme
 * Used across the application for consistent styling
 */
export const getDefaultSelectStyles = <T = any>(): StylesConfig<T> => ({
  control: (provided, state) => ({
    ...provided,
    height: '48px',
    border: state.isFocused ? '2px solid #FF5C02' : '1px solid #d1d5db',
    borderRadius: '8px',
    boxShadow: 'none',
    '&:hover': {
      border: state.isFocused ? '2px solid #FF5C02' : '1px solid #d1d5db',
    },
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? '#FF5C02'
      : state.isFocused
        ? '#FFF5F0'
        : 'white',
    color: state.isSelected ? 'white' : '#374151',
    '&:hover': {
      backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
    },
  }),
  menu: provided => ({
    ...provided,
    border: '1px solid #d1d5db',
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    maxHeight: '200px',
    zIndex: 9999,
  }),
  menuList: provided => ({
    ...provided,
    maxHeight: '200px',
    overflowY: 'auto' as const,
  }),
});

/**
 * Compact react-select styles for inline/embedded selects
 * Used for country code selectors and similar compact use cases
 */
export const getCompactSelectStyles = <T = any>(): StylesConfig<T> => ({
  control: provided => ({
    ...provided,
    height: 'auto',
    minHeight: 'auto',
    border: 'none',
    boxShadow: 'none',
    backgroundColor: 'transparent',
    width: 'auto',
    '&:hover': {
      border: 'none',
    },
  }),
  valueContainer: provided => ({
    ...provided,
    padding: '0',
    fontSize: '14px',
  }),
  input: provided => ({
    ...provided,
    margin: '0',
    padding: '0',
  }),
  indicatorsContainer: provided => ({
    ...provided,
    padding: '0',
  }),
  dropdownIndicator: provided => ({
    ...provided,
    padding: '0 0 0 4px',
  }),
  menu: provided => ({
    ...provided,
    border: '1px solid #d1d5db',
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    maxHeight: '200px',
    zIndex: 9999,
    width: '200px',
  }),
  menuList: provided => ({
    ...provided,
    maxHeight: '200px',
    overflowY: 'auto' as const,
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? '#FF5C02'
      : state.isFocused
        ? '#FFF5F0'
        : 'white',
    color: state.isSelected ? 'white' : '#374151',
    '&:hover': {
      backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
    },
  }),
  singleValue: provided => ({
    ...provided,
    display: 'flex',
    alignItems: 'center',
  }),
});

/**
 * Profile settings select styles with custom border color
 */
export const getProfileSelectStyles = <T = any>(): StylesConfig<T> => ({
  control: provided => ({
    ...provided,
    height: '40px',
    border: '1px solid #DFEAF2',
    borderRadius: '8px',
    boxShadow: 'none',
    '&:hover': {
      border: '1px solid #DFEAF2',
    },
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? '#FF5C02'
      : state.isFocused
        ? '#FFF5F0'
        : 'white',
    color: state.isSelected ? 'white' : '#374151',
    '&:hover': {
      backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
    },
  }),
  menu: provided => ({
    ...provided,
    border: '1px solid #DFEAF2',
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  }),
});

import { AxiosError } from 'axios';

/**
 * Backend error response structure
 */
interface BackendErrorResponse {
  status: boolean;
  message?: string;
  data?: {
    timestamp?: string;
    message?: string;
    details?: string;
  };
}

/**
 * Extracts user-friendly error messages from API errors
 * Priority:
 * 1. Backend message from response.data.message
 * 2. User-friendly message based on HTTP status code
 * 3. Generic fallback message
 */
export const extractErrorMessage = (error: unknown): string => {
  // Handle AxiosError
  if (error instanceof AxiosError) {
    const response = error.response;
    const backendData = response?.data as BackendErrorResponse | undefined;

    // Priority 1: Extract backend message
    if (backendData?.message) {
      return cleanBackendMessage(backendData.message);
    }

    // Priority 2: User-friendly messages based on status code
    const statusCode = response?.status;
    if (statusCode) {
      const friendlyMessage = getStatusCodeMessage(statusCode);
      if (friendlyMessage) {
        return friendlyMessage;
      }
    }

    // Check for timeout errors
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return 'The request took too long to complete. Please try again.';
    }

    // Check for network errors
    if (error.message === 'Network Error' || !response) {
      return 'Unable to connect to the server. Please try again.';
    }

    // Fallback to error message if available
    if (error.message) {
      return error.message;
    }
  }

  // Handle standard Error
  if (error instanceof Error) {
    return error.message;
  }

  // Generic fallback
  return 'An unexpected error occurred. Please try again.';
};

/**
 * Returns user-friendly messages for common HTTP status codes
 */
const getStatusCodeMessage = (statusCode: number): string | null => {
  const statusMessages: Record<number, string> = {
    400: 'The request was invalid. Please check your input and try again.',
    401: 'You need to be logged in to perform this action.',
    403: 'You do not have permission to perform this action.',
    404: 'The requested resource could not be found.',
    408: 'The request took too long. Please try again.',
    409: 'There was a conflict with your request. Please refresh and try again.',
    413: 'The file or data you are trying to send is too large.',
    422: 'The data provided could not be processed. Please check your input.',
    429: 'Too many requests. Please wait a moment and try again.',
    500: 'The server encountered an error. Please try again later.',
    502: 'The server is temporarily unavailable. Please try again in a moment.',
    503: 'The service is temporarily unavailable. Please try again later.',
    504: 'The server took too long to respond. Please try again.',
  };

  return statusMessages[statusCode] || null;
};

/**
 * Cleans backend error messages by removing brackets and extra formatting
 */
const cleanBackendMessage = (message: string): string => {
  // Remove square brackets at the beginning and end
  let cleaned = message.replace(/^\[|\]$/g, '');

  // Trim whitespace
  cleaned = cleaned.trim();

  // Capitalize first letter if not already
  if (cleaned.length > 0) {
    cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
  }

  // Ensure message ends with a period if it doesn't have punctuation
  if (cleaned.length > 0 && !/[.!?]$/.test(cleaned)) {
    cleaned += '.';
  }

  return cleaned;
};

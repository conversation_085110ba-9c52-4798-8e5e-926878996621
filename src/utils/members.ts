import { MemberRole, ROLE_DESCRIPTIONS } from '../types/members';

export const getRoleColor = (role: MemberRole): string => {
  const colors: Record<MemberRole, string> = {
    MANAGER: 'bg-primary text-white',
    LEAD: 'bg-[#FFECE3] text-grayTen',
    MEMBER: 'bg-[#FFECE3] text-grayTen',
  };
  return colors[role] || colors.MEMBER;
};

export const getRoleDisplayName = (role: MemberRole): string => {
  const names: Record<MemberRole, string> = {
    MANAGER: 'Manager',
    LEAD: 'Lead',
    MEMBER: 'Member',
  };
  return names[role] || 'Member';
};

export const getRoleDescription = (role: MemberRole): string => {
  return ROLE_DESCRIPTIONS[role]?.description || '';
};

export const canUserPerformAction = (
  userRole: MemberRole,
  targetRole: MemberRole,
  action: 'update' | 'remove'
): boolean => {
  // Only managers can perform actions on other members
  if (userRole !== 'MANAGER') return false;

  // Managers cannot remove/update other managers
  if (action === 'remove' && targetRole === 'MANAGER') return false;
  if (action === 'update' && targetRole === 'MANAGER') return false;

  return true;
};

/**
 * Format last login date for member display
 * Returns 'Today' or 'Yesterday' for recent dates, otherwise returns null
 * to indicate that the caller should use formatUserTimestamp for absolute dates
 *
 * For absolute dates, use formatUserTimestamp from TimezoneContext instead
 */
export const formatLastLogin = (lastLogin: string | null): string | null => {
  if (!lastLogin) return '--';

  const date = new Date(lastLogin);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 24) {
    return 'Today';
  } else if (diffInHours < 48) {
    return 'Yesterday';
  } else {
    // Return null to indicate caller should use formatUserTimestamp
    return null;
  }
};

export const generateMemberInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2);
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const parseEmailList = (emailString: string): string[] => {
  return emailString
    .split(/[,\n]/)
    .map(email => email.trim())
    .filter(email => email.length > 0);
};

export const validateEmailList = (
  emails: string[]
): { valid: string[]; invalid: string[] } => {
  const valid: string[] = [];
  const invalid: string[] = [];

  emails.forEach(email => {
    if (validateEmail(email)) {
      valid.push(email);
    } else {
      invalid.push(email);
    }
  });

  return { valid, invalid };
};

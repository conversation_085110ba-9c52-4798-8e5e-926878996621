import { ApiResponse } from './user';

export interface DepartmentInfo {
  id: string;
  tenantId: string;
  agentSuiteKey: string;
  name: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
}

export interface CreateDepartmentInfoRequest {
  agentSuiteKey: string;
  name: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
}

export interface UpdateDepartmentInfoRequest {
  agentSuiteKey: string; // Based on Swagger, this is in body for PATCH
  name: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
}

export interface CompanyInfo {
  id: string;
  tenantId: string;
  agentSuiteKey: string;
  name: string;
  email: string;
  address1: string;
  address2: string;
  country: string;
  stateOrProvince: string;
  city: string;
  zipOrPostalCode: string;
  phone: string;
}

export interface CreateCompanyInfoRequest {
  agentSuiteKey: string;
  name: string;
  email: string;
  address1: string;
  address2: string;
  country: string;
  stateOrProvince: string;
  city: string;
  zipOrPostalCode: string;
  phone: string;
}

export interface UpdateCompanyInfoRequest {
  agentSuiteKey: string; // Based on Swagger, this is in body for PATCH
  name: string;
  email: string;
  address1: string;
  address2: string;
  country: string;
  stateOrProvince: string;
  city: string;
  zipOrPostalCode: string;
  phone: string;
}

// Response types
export type DepartmentInfoResponse = ApiResponse<DepartmentInfo>;
export type CompanyInfoResponse = ApiResponse<CompanyInfo>;

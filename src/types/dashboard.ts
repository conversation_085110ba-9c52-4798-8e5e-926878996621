export interface DailyInsight {
  id: string;
  insightId: string;
  source: string;
  observation: string;
  impact: string;
  recommendation: string;
  createdAt: string;
  createdBy: string;
  tenantId?: string;
  actionTaken?: string; // Optional field for action taken
}

export interface DailyInsightFilter {
  createdBy: string;
  tenantId: string;
  search?: string;
  from?: string;
  to?: string;
  page?: number;
  pageSize?: number;
  source?: string; // Filter by insight source/type (business/technical)
}

export interface CreateDailyInsightRequest {
  agentName: string;
  tenantId: string;
  insight: string;
}

export interface DailyInsightListResponse {
  status: boolean;
  message: string;
  data: {
    insights: DailyInsight[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface DailyInsightResponse {
  status: boolean;
  message: string;
  data: DailyInsight;
}

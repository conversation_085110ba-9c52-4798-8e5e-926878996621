export interface CountryWithStates {
  name: string;
  iso2: string;
  flagUrl: string;
  states: string[]; // Array of state names
}

export interface CountriesWithStatesResponse {
  status: boolean;
  message: string;
  data: {
    countries: CountryWithStates[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface LocationOption {
  value: string;
  label: string;
  countryCode?: string;
  stateName?: string;
}

export interface FetchOptionsResult {
  options: LocationOption[];
  hasMore: boolean;
}

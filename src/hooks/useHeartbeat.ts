/**
 * Custom hook for managing agent heartbeat state and operations
 *
 * This hook provides a complete interface for heartbeat functionality,
 * including fetching status, initializing, and pausing heartbeats.
 */

import { useCallback, useState } from 'react';

import { useNotifications } from '@/hooks/useNotifications';
import { useHeartbeatOperations } from '@/services/heartbeatService';
import type {
  HeartbeatState,
  HeartbeatStatus,
  UseHeartbeatReturn,
} from '@/types/heartbeat';

/**
 * Hook for managing heartbeat operations and state
 */
export const useHeartbeat = (): UseHeartbeatReturn => {
  const { notify } = useNotifications();
  const { fetchAllHeartbeats, initializeHeartbeat, pauseHeartbeat } =
    useHeartbeatOperations();

  const [heartbeatState, setHeartbeatState] = useState<HeartbeatState>({
    heartbeatData: {},
    isLoading: false,
    error: null,
    loadingAgent: null,
  });

  /**
   * Fetch heartbeat status for all agents
   */
  const fetchHeartbeats = useCallback(async (): Promise<void> => {
    setHeartbeatState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
    }));

    try {
      const response = await fetchAllHeartbeats();

      if (response.status) {
        // Convert array to object for easier lookup
        const heartbeatData: Record<string, HeartbeatStatus> = {};
        response.data.forEach(item => {
          heartbeatData[item.agentKey] = item.status;
        });

        setHeartbeatState(prev => ({
          ...prev,
          heartbeatData,
          isLoading: false,
          error: null,
        }));
      } else {
        throw new Error(response.message || 'Failed to fetch heartbeat data');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';

      setHeartbeatState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));

      console.error(
        `Failed to fetch heartbeat status: ${errorMessage}`,
        'error'
      );
      notify(errorMessage);
    }
  }, [fetchAllHeartbeats, notify]);

  /**
   * Initialize heartbeat for a specific agent
   */
  const handleInitializeHeartbeat = useCallback(
    async (agentKey: string): Promise<void> => {
      setHeartbeatState(prev => ({
        ...prev,
        loadingAgent: agentKey,
      }));

      try {
        await initializeHeartbeat(agentKey);

        // Update local state optimistically
        setHeartbeatState(prev => ({
          ...prev,
          heartbeatData: {
            ...prev.heartbeatData,
            [agentKey]: 'ACTIVE',
          },
          loadingAgent: null,
        }));

        notify('Agent heartbeat successfully initialized', 'success');
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';

        setHeartbeatState(prev => ({
          ...prev,
          loadingAgent: null,
        }));

        notify(errorMessage);
      }
    },
    [initializeHeartbeat, notify]
  );

  /**
   * Pause heartbeat for a specific agent
   */
  const handlePauseHeartbeat = useCallback(
    async (agentKey: string): Promise<void> => {
      setHeartbeatState(prev => ({
        ...prev,
        loadingAgent: agentKey,
      }));

      try {
        await pauseHeartbeat(agentKey);

        // Update local state optimistically
        setHeartbeatState(prev => ({
          ...prev,
          heartbeatData: {
            ...prev.heartbeatData,
            [agentKey]: 'IDLE',
          },
          loadingAgent: null,
        }));

        notify('Agent heartbeat successfully paused', 'success');
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';

        setHeartbeatState(prev => ({
          ...prev,
          loadingAgent: null,
        }));

        notify(errorMessage);
      }
    },
    [pauseHeartbeat, notify]
  );

  /**
   * Get heartbeat status for a specific agent
   */
  const getHeartbeatStatus = useCallback(
    (agentKey: string): HeartbeatStatus | null => {
      return heartbeatState.heartbeatData[agentKey] || null;
    },
    [heartbeatState.heartbeatData]
  );

  return {
    heartbeatState,
    fetchHeartbeats,
    initializeHeartbeat: handleInitializeHeartbeat,
    pauseHeartbeat: handlePauseHeartbeat,
    getHeartbeatStatus,
  };
};

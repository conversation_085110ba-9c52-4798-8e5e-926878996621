import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useTaskLogService } from '@/services/taskLogService';
import type { TaskLogFilter } from '@/types/taskLog';
import { CreateTaskLogRequest, UpdateTaskLogRequest } from '@/types/taskLog';

// Query keys for React Query cache management
export const TASK_LOG_QUERY_KEYS = {
  all: ['task-logs'] as const,
  lists: () => [...TASK_LOG_QUERY_KEYS.all, 'list'] as const,
  list: (filter: TaskLogFilter) =>
    [...TASK_LOG_QUERY_KEYS.lists(), filter] as const,
  details: () => [...TASK_LOG_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...TASK_LOG_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook to fetch a single task log by ID
 */
export const useTaskLogDetails = (id: string, enabled = true) => {
  const { getTaskLog } = useTaskLogService();
  return useQuery({
    queryKey: TASK_LOG_QUERY_KEYS.detail(id),
    queryFn: () => getTaskLog(id),
    enabled: enabled && !!id,
    select: data => data,
  });
};

/**
 * Hook to fetch all task logs for a tenant
 */
export const useTaskLogsList = (filter: TaskLogFilter, enabled = true) => {
  const { getTaskLogs } = useTaskLogService();
  return useQuery({
    queryKey: TASK_LOG_QUERY_KEYS.list(filter),
    queryFn: () => getTaskLogs(filter),
    enabled: enabled,
    select: data => data,
  });
};

/**
 * Hook to create a new task log
 */
export const useCreateTaskLogMutation = () => {
  const queryClient = useQueryClient();
  const { createTaskLog } = useTaskLogService();

  return useMutation({
    mutationFn: (payload: CreateTaskLogRequest) => createTaskLog(payload),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: TASK_LOG_QUERY_KEYS.lists() });

      queryClient.setQueryData(TASK_LOG_QUERY_KEYS.detail(data.data.id), data);
    },
    onError: error => {
      console.error('Failed to create task log:', error);
    },
  });
};

/**
 * Hook to update a task log
 */
export const useUpdateTaskLogMutation = () => {
  const queryClient = useQueryClient();
  const { updateTaskLog } = useTaskLogService();

  return useMutation({
    mutationFn: (payload: UpdateTaskLogRequest) =>
      updateTaskLog(payload.id, payload),
    onSuccess: (data, variables) => {
      queryClient.setQueryData(TASK_LOG_QUERY_KEYS.detail(variables.id), data);

      queryClient.invalidateQueries({ queryKey: TASK_LOG_QUERY_KEYS.lists() });
    },
    onError: error => {
      console.error('Failed to update task log:', error);
    },
  });
};

/**
 * Hook to delete a task log
 */
export const useDeleteTaskLogMutation = () => {
  const queryClient = useQueryClient();
  const { deleteTaskLog } = useTaskLogService();

  return useMutation({
    mutationFn: (id: string) => deleteTaskLog(id),
    onSuccess: (_, deletedId) => {
      queryClient.removeQueries({
        queryKey: TASK_LOG_QUERY_KEYS.detail(deletedId),
      });

      queryClient.invalidateQueries({
        queryKey: TASK_LOG_QUERY_KEYS.lists(),
      });
    },
    onError: error => {
      console.error('Failed to delete task log:', error);
    },
  });
};

/**
 * Utility hook to prefetch task log details
 */
export const usePrefetchTaskLog = () => {
  const queryClient = useQueryClient();
  const { getTaskLog } = useTaskLogService();

  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: TASK_LOG_QUERY_KEYS.detail(id),
      queryFn: () => getTaskLog(id),
    });
  };
};

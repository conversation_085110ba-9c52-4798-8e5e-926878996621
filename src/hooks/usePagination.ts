import { useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';

interface UsePaginationResult {
  page: number;
  setPage: (page: number) => void;
}

export const usePagination = (
  defaultPage: number = 1,
  paramName: string = 'page'
): UsePaginationResult => {
  const [searchParams, setSearchParams] = useSearchParams();

  const page = parseInt(searchParams.get(paramName) || String(defaultPage), 10);

  const setPage = useCallback(
    (newPage: number) => {
      setSearchParams(
        prev => {
          const newParams = new URLSearchParams(prev);
          if (newPage === 1) {
            newParams.delete(paramName); // Cleaner URL for page 1
          } else {
            newParams.set(paramName, String(newPage));
          }
          return newParams;
        },
        { replace: true }
      );
    },
    [setSearchParams, paramName]
  );

  return { page, setPage };
};

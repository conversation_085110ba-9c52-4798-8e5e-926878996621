import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { createAuthenticatedAxiosInstance } from '@/helpers/axiosConfig';
import { MEMBERS_QUERY_KEYS } from '@/hooks/useMembers';
import type {
  ApiStandardResponse,
  CreateJoinRequestPayload,
  JoinRequestQueryParams,
  JoinRequestsListResponse,
  RejectJoinRequestPayload,
  UpdateJoinRequestRolePayload,
} from '@/services/requestToJoinService';
import requestToJoinService from '@/services/requestToJoinService';

// Query keys for React Query cache management
export const JOIN_REQUEST_QUERY_KEYS = {
  all: ['join-requests'] as const,
  suiteRequests: () => [...JOIN_REQUEST_QUERY_KEYS.all, 'suite'] as const,
  suiteRequestsList: (agentSuiteKey: string, filters: object) =>
    [
      ...JOIN_REQUEST_QUERY_KEYS.suiteRequests(),
      agentSuiteKey,
      filters,
    ] as const,
  myRequests: () => [...JOIN_REQUEST_QUERY_KEYS.all, 'my'] as const,
  myRequestsList: (filters: object) =>
    [...JOIN_REQUEST_QUERY_KEYS.myRequests(), filters] as const,
};

// ===== Query Hooks =====

/**
 * Hook to fetch join requests for a specific suite (for admins/managers)
 * GET /tenants/suites/{agentSuiteKey}/join-requests
 */
export const useSuiteJoinRequests = (
  agentSuiteKey: string,
  params: JoinRequestQueryParams,
  enabled = true
) => {
  const authToken = createAuthenticatedAxiosInstance();

  return useQuery({
    queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(agentSuiteKey, params),
    queryFn: () =>
      requestToJoinService.getSuiteJoinRequests(
        authToken,
        agentSuiteKey,
        params
      ),
    enabled: enabled && !!agentSuiteKey && !!authToken,
    select: (data: JoinRequestsListResponse) => data.data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook to fetch current user's join requests
 * GET /tenants/my-join-requests
 */
export const useMyJoinRequests = (
  params: JoinRequestQueryParams,
  enabled = true
) => {
  const authToken = createAuthenticatedAxiosInstance();

  return useQuery({
    queryKey: JOIN_REQUEST_QUERY_KEYS.myRequestsList(params),
    queryFn: () => requestToJoinService.getMyJoinRequests(authToken, params),
    enabled: enabled && !!authToken,
    select: (data: JoinRequestsListResponse) => data.data,
    retry: 2,
    refetchOnWindowFocus: true,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// ===== Mutation Hooks =====

/**
 * Hook to create a new join request
 * POST /tenants/suite-join-requests
 */
export const useCreateJoinRequestMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  const invalidateMembersData = () => {
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.all });
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.invitesAll });
  };

  return useMutation({
    mutationFn: (
      payload: CreateJoinRequestPayload
    ): Promise<ApiStandardResponse> =>
      requestToJoinService.createJoinRequest(authToken, payload),
    onSuccess: () => {
      // Invalidate my join requests list
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.myRequests(),
      });
      invalidateMembersData();
    },
    onError: () => {
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.myRequests(),
      });
      invalidateMembersData();
    },
  });
};

/**
 * Hook to approve a join request
 * POST /tenants/suite-join-requests/{requestId}/approve
 */
export const useApproveJoinRequestMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  const invalidateMembersData = () => {
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.all });
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.invitesAll });
  };

  return useMutation({
    mutationFn: ({
      requestId,
    }: {
      requestId: string;
      agentSuiteKey?: string;
    }): Promise<ApiStandardResponse> =>
      requestToJoinService.approveJoinRequest(authToken, requestId),
    onSuccess: (_data, variables) => {
      // Invalidate suite join requests list if agentSuiteKey is provided
      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      // Also invalidate all suite requests as a fallback
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequests(),
      });
      invalidateMembersData();
    },
    onError: (_error, variables) => {
      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequests(),
      });
      invalidateMembersData();
    },
  });
};

/**
 * Hook to reject a join request
 * POST /tenants/suite-join-requests/{requestId}/reject
 */
export const useRejectJoinRequestMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  const invalidateMembersData = () => {
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.all });
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.invitesAll });
  };

  return useMutation({
    mutationFn: ({
      requestId,
      payload,
    }: {
      requestId: string;
      payload: RejectJoinRequestPayload;
      agentSuiteKey?: string;
    }): Promise<ApiStandardResponse> =>
      requestToJoinService.rejectJoinRequest(authToken, requestId, payload),
    onSuccess: (_data, variables) => {
      // Invalidate suite join requests list if agentSuiteKey is provided
      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      // Also invalidate all suite requests as a fallback
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequests(),
      });
      invalidateMembersData();
    },
    onError: (_error, variables) => {
      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequests(),
      });
      invalidateMembersData();
    },
  });
};

/**
 * Hook to update join request role
 * PATCH /tenants/suite-join-requests/{requestId}/role
 */
export const useUpdateJoinRequestRoleMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  const invalidateMembersData = () => {
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.all });
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.invitesAll });
  };

  return useMutation({
    mutationFn: ({
      requestId,
      payload,
      agentSuiteKey,
    }: {
      requestId: string;
      payload: UpdateJoinRequestRolePayload;
      agentSuiteKey?: string;
    }): Promise<ApiStandardResponse> =>
      requestToJoinService.updateJoinRequestRole(authToken, requestId, payload),
    onSuccess: (_data, variables) => {
      // Invalidate suite join requests if agentSuiteKey is provided
      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      // Also invalidate all suite requests as a fallback
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequests(),
      });
      invalidateMembersData();
    },
    onError: (_error, variables) => {
      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequests(),
      });
      invalidateMembersData();
    },
  });
};

/**
 * Hook to delete a join request
 * DELETE /tenants/suite-join-requests/{requestId}
 */
export const useDeleteJoinRequestMutation = () => {
  const queryClient = useQueryClient();
  const authToken = createAuthenticatedAxiosInstance();

  const invalidateMembersData = () => {
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.all });
    queryClient.invalidateQueries({ queryKey: MEMBERS_QUERY_KEYS.invitesAll });
  };

  return useMutation({
    mutationFn: ({
      requestId,
    }: {
      requestId: string;
      agentSuiteKey?: string;
    }): Promise<ApiStandardResponse> =>
      requestToJoinService.deleteJoinRequest(authToken, requestId),
    onSuccess: (_data, variables) => {
      // Invalidate my join requests
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.myRequests(),
      });

      // Invalidate suite join requests if agentSuiteKey is provided
      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      invalidateMembersData();
    },
    onError: (_error, variables) => {
      queryClient.invalidateQueries({
        queryKey: JOIN_REQUEST_QUERY_KEYS.myRequests(),
      });

      if (variables.agentSuiteKey) {
        queryClient.invalidateQueries({
          queryKey: JOIN_REQUEST_QUERY_KEYS.suiteRequestsList(
            variables.agentSuiteKey,
            {}
          ),
        });
      }
      invalidateMembersData();
    },
  });
};

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { usePrivateRequest } from '@/lib/axios/usePrivateRequest';
import departmentInfoService from '@/services/departmentInfoService';
import {
  CreateDepartmentInfoRequest,
  UpdateDepartmentInfoRequest,
} from '@/types/organization';
import { BASE_URL } from '@/utils/apiUrls';

export const DEPARTMENT_INFO_QUERY_KEYS = {
  all: ['department-info'] as const,
  byKey: (agentSuiteKey: string) =>
    [...DEPARTMENT_INFO_QUERY_KEYS.all, agentSuiteKey] as const,
  byTenant: (tenantId: string, agentSuiteKey: string) =>
    [
      ...DEPARTMENT_INFO_QUERY_KEYS.all,
      'tenant',
      tenantId,
      agentSuiteKey,
    ] as const,
};

export const useGetDepartmentInfo = (agentSuiteKey: string, options = {}) => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useQuery({
    queryKey: DEPARTMENT_INFO_QUERY_KEYS.byKey(agentSuiteKey),
    queryFn: async () => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return departmentInfoService.getDepartmentInfo(
        axiosInstance.current,
        agentSuiteKey
      );
    },
    enabled: !!axiosInstance.current && !!agentSuiteKey,
    ...options,
  });
};

export const useCreateDepartmentInfoMutation = () => {
  const queryClient = useQueryClient();
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: CreateDepartmentInfoRequest) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return departmentInfoService.createDepartmentInfo(
        axiosInstance.current,
        payload
      );
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: DEPARTMENT_INFO_QUERY_KEYS.byKey(variables.agentSuiteKey),
      });
    },
  });
};

export const useUpdateDepartmentInfoMutation = () => {
  const queryClient = useQueryClient();
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: UpdateDepartmentInfoRequest) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return departmentInfoService.updateDepartmentInfo(
        axiosInstance.current,
        payload
      );
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: DEPARTMENT_INFO_QUERY_KEYS.byKey(variables.agentSuiteKey),
      });
    },
  });
};

export const useGetDepartmentInfoByTenant = (
  tenantId: string,
  agentSuiteKey: string,
  options = {}
) => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useQuery({
    queryKey: DEPARTMENT_INFO_QUERY_KEYS.byTenant(tenantId, agentSuiteKey),
    queryFn: async () => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return departmentInfoService.getDepartmentInfoByTenant(
        axiosInstance.current,
        tenantId,
        agentSuiteKey
      );
    },
    enabled: !!axiosInstance.current && !!tenantId && !!agentSuiteKey,
    ...options,
  });
};

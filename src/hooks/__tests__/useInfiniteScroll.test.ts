import { renderHook, waitFor } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

import { PaginatedChatHistoryResponse } from '@/types/agents';

import { useInfiniteScroll } from '../useInfiniteScroll';

// Mock fetch function
const mockFetchFunction = vi.fn();

// Mock data
const mockChatHistoryItem = {
  userId: 'user1',
  tenantId: 'tenant1',
  conversationId: 'conv1',
  message: 'Test message',
  sender: 'user',
  createdAt: '2023-01-01T00:00:00Z',
};

const mockPaginatedResponse: PaginatedChatHistoryResponse = {
  status: true,
  message: 'Operation successful.',
  data: {
    items: [mockChatHistoryItem],
    total: 1,
    page: 1,
    pageSize: 20,
  },
};

describe('useInfiniteScroll', () => {
  beforeEach(() => {
    mockFetchFunction.mockClear();
  });

  it('should initialize with default state', () => {
    mockFetchFunction.mockResolvedValue(mockPaginatedResponse);
    
    const { result } = renderHook(() =>
      useInfiniteScroll(mockFetchFunction, { pageSize: 20 })
    );

    expect(result.current.items).toEqual([]);
    expect(result.current.currentPage).toBe(0);
    expect(result.current.totalItems).toBe(0);
    expect(result.current.hasNextPage).toBe(true);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isLoadingMore).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should load first page automatically', async () => {
    mockFetchFunction.mockResolvedValue(mockPaginatedResponse);
    
    const { result } = renderHook(() =>
      useInfiniteScroll(mockFetchFunction, { pageSize: 20 })
    );

    await waitFor(() => {
      expect(mockFetchFunction).toHaveBeenCalledWith(1, 20);
    });

    await waitFor(() => {
      expect(result.current.items).toHaveLength(1);
      expect(result.current.currentPage).toBe(1);
      expect(result.current.totalItems).toBe(1);
      expect(result.current.hasNextPage).toBe(false);
    });
  });

  it('should handle loadMore correctly', async () => {
    const multiPageResponse: PaginatedChatHistoryResponse = {
      status: true,
      message: 'Operation successful.',
      data: {
        items: [mockChatHistoryItem],
        total: 40,
        page: 1,
        pageSize: 20,
      },
    };

    mockFetchFunction.mockResolvedValue(multiPageResponse);
    
    const { result } = renderHook(() =>
      useInfiniteScroll(mockFetchFunction, { pageSize: 20 })
    );

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.items).toHaveLength(1);
      expect(result.current.hasNextPage).toBe(true);
    });

    // Load more
    await result.current.loadMore();

    await waitFor(() => {
      expect(mockFetchFunction).toHaveBeenCalledTimes(2);
      expect(mockFetchFunction).toHaveBeenLastCalledWith(2, 20);
    });
  });

  it('should reset state correctly', async () => {
    mockFetchFunction.mockResolvedValue(mockPaginatedResponse);
    
    const { result } = renderHook(() =>
      useInfiniteScroll(mockFetchFunction, { pageSize: 20 })
    );

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.items).toHaveLength(1);
    });

    // Reset
    result.current.reset();

    expect(result.current.items).toEqual([]);
    expect(result.current.currentPage).toBe(0);
    expect(result.current.totalItems).toBe(0);
    expect(result.current.hasNextPage).toBe(true);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isLoadingMore).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should handle errors gracefully', async () => {
    const errorMessage = 'Network error';
    mockFetchFunction.mockRejectedValue(new Error(errorMessage));
    
    const { result } = renderHook(() =>
      useInfiniteScroll(mockFetchFunction, { pageSize: 20 })
    );

    await waitFor(() => {
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isLoadingMore).toBe(false);
    });
  });
});

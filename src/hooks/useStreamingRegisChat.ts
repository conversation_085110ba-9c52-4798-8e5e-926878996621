import { useKeycloak } from '@react-keycloak/web';
import { createParser } from 'eventsource-parser';
import { useCallback, useEffect, useRef, useState } from 'react';

import { ROUTES } from '../constants/routes';
import { generateSecureSessionId } from '../services/upivotalAgenticService';
import { agenticService } from '../utils/apiServiceControllersRoute';
import { BASE_URL } from '../utils/apiUrls';
import { extractErrorMessage } from '../utils/errorUtils';

export type OnboardingStep =
  | 'initial'
  | 'firstName'
  | 'lastName'
  | 'email'
  | 'verification'
  | 'password'
  | 'completed';

export interface RegisMessage {
  id: string;
  sender: 'user' | 'regis';
  content: string;
  timestamp: Date;
  senderName: string;
}

export interface OnboardingData {
  firstName?: string;
  lastName?: string;
  email?: string;
  emailVerified?: boolean;
  password?: string;
}

export interface RegisState {
  messages: RegisMessage[];
  isLoading: boolean;
  sessionId: string;
  currentStep: OnboardingStep;
  onboardingData: OnboardingData;
  isSignupFlow: boolean;
}

const initialState: RegisState = {
  messages: [],
  isLoading: false,
  sessionId: generateSecureSessionId(),
  currentStep: 'initial',
  onboardingData: {},
  isSignupFlow: false,
};

export const useStreamingRegisChat = (flowType: 'signup' | 'login') => {
  const [state, setState] = useState<RegisState>({
    ...initialState,
    isSignupFlow: flowType === 'signup',
  });
  const hasInitializedRef = useRef(false);
  const { keycloak } = useKeycloak();

  // Custom streaming chat implementation
  const [streamingMessage, setStreamingMessage] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const addMessage = useCallback(
    (sender: 'user' | 'regis', content: string, senderName: string) => {
      const newMessage: RegisMessage = {
        id: Date.now().toString(),
        sender,
        content,
        timestamp: new Date(),
        senderName,
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, newMessage],
      }));
    },
    []
  );

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const hashPasswordForDisplay = useCallback(
    (message: string, currentStep: OnboardingStep) => {
      if (currentStep === 'password' && message.length >= 8) {
        return '•'.repeat(message.length);
      }
      return message;
    },
    []
  );

  const updateOnboardingData = useCallback((data: Partial<OnboardingData>) => {
    setState(prev => ({
      ...prev,
      onboardingData: { ...prev.onboardingData, ...data },
    }));
  }, []);

  const updateCurrentStep = useCallback((step: OnboardingStep) => {
    setState(prev => ({ ...prev, currentStep: step }));
  }, []);

  const abortCurrentRequest = useCallback(() => {
    if (
      abortControllerRef.current &&
      !abortControllerRef.current.signal.aborted
    ) {
      abortControllerRef.current.abort();
    }
  }, []);

  const extractOnboardingDataFromMessage = useCallback(
    (response: string) => {
      const requestFieldMatch = response.match(
        /\{\s*"requestField"\s*:\s*"([^"]+)"\s*\}/
      );
      if (requestFieldMatch) {
        const requestField = requestFieldMatch[1];

        const stepMapping: Record<string, OnboardingStep> = {
          firstName: 'firstName',
          lastName: 'lastName',
          email: 'email',
          verificationCode: 'verification',
          password: 'password',
        };

        if (stepMapping[requestField]) {
          updateCurrentStep(stepMapping[requestField]);
        }
      } else if (state.isSignupFlow && state.currentStep === 'password') {
        const lowerResponse = response.toLowerCase();
        const successIndicators = [
          'your account has been successfully created',
          'successfully created',
          'created successfully',
          'successful',
          'welcome aboard',
          'welcome',
        ];

        const isSignupComplete = successIndicators.some(indicator =>
          lowerResponse.includes(indicator)
        );

        if (isSignupComplete) {
          updateCurrentStep('completed');
          setTimeout(() => {
            try {
              keycloak?.login({
                redirectUri: window.location.origin + ROUTES.DASHBOARD_BASE,
              });
            } catch (error) {
              console.error('Error initiating Keycloak login:', error);
              window.location.href = ROUTES.LOGIN;
            }
          }, 2000);
        }
      }
    },
    [updateCurrentStep, state.isSignupFlow, state.currentStep, keycloak]
  );

  const sendMessage = useCallback(
    async (userMessage: string) => {
      try {
        // Only cancel existing request if one is in progress
        if (abortControllerRef.current && (state.isLoading || isStreaming)) {
          abortControllerRef.current.abort();
        }

        // Create new abort controller for this request
        abortControllerRef.current = new AbortController();

        // Add user message immediately
        const userDisplayMessage = hashPasswordForDisplay(
          userMessage,
          state.currentStep
        );
        addMessage('user', userDisplayMessage, 'You');

        // Set loading state
        setLoading(true);
        setStreamingMessage('');
        setIsStreaming(false);

        // Use POST fetch for streaming
        const response = await fetch(
          `${BASE_URL}${agenticService}/ai/regis/chat`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Accept: 'text/event-stream',
            },
            body: JSON.stringify({
              userMessage,
              sessionId: state.sessionId,
            }),
            signal: abortControllerRef.current.signal,
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Check if response supports streaming
        const reader = response.body?.getReader();
        if (!reader) {
          // Fallback to regular text response
          const fullResponse = await response.text();
          extractOnboardingDataFromMessage(fullResponse);
          const displayMessage = fullResponse
            .replace(/\{\s*"requestField"\s*:\s*"[^"]+"\s*\}\s*/g, '')
            .replace(/\s+/g, ' ')
            .trim();
          addMessage('regis', displayMessage, 'Regis');
          return;
        }

        // Start streaming
        setLoading(false);
        setIsStreaming(true);

        const decoder = new TextDecoder('utf-8');
        let accumulatedMessage = '';

        const parser = createParser({
          onEvent(event) {
            // 'event' is of type EventSourceMessage, which does not have a 'type' property.
            // The parser only calls onEvent for 'event' type messages.
            const content = event.data;
            if (content && content !== '[DONE]') {
              // Add space before each word unless:
              // - It's the first word (accumulatedMessage is empty)
              // - It's punctuation (starts with punctuation)
              // - Previous content already ends with space
              const shouldAddSpace =
                accumulatedMessage &&
                !content.match(/^[.,!?;:]/) &&
                !accumulatedMessage.endsWith(' ');

              if (shouldAddSpace) {
                accumulatedMessage += ' ';
              }
              accumulatedMessage += content;

              // Clean the accumulated text for display and update incrementally
              const cleanedText = accumulatedMessage
                .replace(/\{\s*"requestField"\s*:\s*"[^"]+"\s*\}\s*/g, '')
                .trim();

              // Update streaming message immediately for live typing effect
              setStreamingMessage(cleanedText);
            }
          },
        });

        try {
          // eslint-disable-next-line no-constant-condition
          while (true) {
            // Check if we should abort before each read
            if (abortControllerRef.current?.signal.aborted) {
              throw new DOMException(
                'The operation was aborted.',
                'AbortError'
              );
            }

            const { done, value } = await reader.read();

            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            parser.feed(chunk);
          }
        } finally {
          reader.releaseLock();
        }

        // Process final message
        setIsStreaming(false);
        extractOnboardingDataFromMessage(accumulatedMessage);
        const displayMessage = accumulatedMessage
          .replace(/\{\s*"requestField"\s*:\s*"[^"]+"\s*\}\s*/g, '')
          .replace(/\s+/g, ' ')
          .trim();

        addMessage('regis', displayMessage, 'Regis');
      } catch (error: any) {
        if (error.name === 'AbortError') {
          console.log('Stream aborted by user');
          addMessage(
            'regis',
            'Request was cancelled. Please try again.',
            'Regis'
          );
          return;
        }

        console.error('Chat error:', error);
        const errorMessage = extractErrorMessage(error);
        addMessage('regis', errorMessage, 'Regis');
      } finally {
        setLoading(false);
        setIsStreaming(false);
        setStreamingMessage('');
        // Only clear if this is still the current request
        if (abortControllerRef.current?.signal.aborted !== true) {
          abortControllerRef.current = null;
        }
      }
    },
    [
      addMessage,
      setLoading,
      state.sessionId,
      state.currentStep,
      state.isLoading,
      isStreaming,
      extractOnboardingDataFromMessage,
      hashPasswordForDisplay,
    ]
  );

  // Auto-trigger initial message
  useEffect(() => {
    if (!hasInitializedRef.current && state.messages.length === 0) {
      hasInitializedRef.current = true;
      if (flowType === 'signup') {
        sendMessage('Hey Regis, I want to create a new account.');
      } else {
        addMessage(
          'regis',
          'Welcome back. You can go ahead and type your email and password into the fields on the right to log in. If you need help with your password or anything else, please let me know!',
          'Regis'
        );
      }
    }
  }, [flowType, state.messages.length, sendMessage, addMessage]);

  return {
    state,
    sendMessage,
    addMessage,
    setLoading,
    updateOnboardingData,
    updateCurrentStep,
    isStreaming,
    streamingMessage,
    abortCurrentRequest,
  };
};

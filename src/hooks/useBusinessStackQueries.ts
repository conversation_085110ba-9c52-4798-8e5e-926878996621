import { useQuery } from '@tanstack/react-query';

import { useTenant } from '@/context/TenantContext';
import {
  useAppCategoriesApi,
  useAvailableAppsApi,
} from '@/services/businessStackService';

interface UseAvailableAppsParams {
  page?: number;
  size?: number;
  search?: string;
  appCategory?: string;
}

/**
 * React Query hook for fetching available apps with caching
 */
export const useAvailableAppsQuery = (params: UseAvailableAppsParams = {}) => {
  const { activeAgent } = useTenant();
  const getAvailableApps = useAvailableAppsApi();

  return useQuery({
    queryKey: [
      'availableApps',
      activeAgent,
      params.page || 1,
      params.size || 10,
      params.search || '',
      params.appCategory || '',
    ],
    queryFn: () => getAvailableApps(params),
    enabled: !!activeAgent, // Only run query when activeAgent is available
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    cacheTime: 10 * 60 * 1000, // Keep cached data for 10 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * React Query hook for fetching app categories with caching
 */
export const useAppCategoriesQuery = () => {
  const { activeAgent } = useTenant();
  const getAppCategories = useAppCategoriesApi();

  return useQuery({
    queryKey: ['appCategories', activeAgent],
    queryFn: getAppCategories,
    enabled: !!activeAgent,
    staleTime: 10 * 60 * 1000, // Categories rarely change, keep fresh for 10 minutes
    cacheTime: 30 * 60 * 1000, // Keep cached for 30 minutes
    refetchOnWindowFocus: false,
  });
};

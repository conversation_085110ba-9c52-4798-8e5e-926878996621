import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { usePrivateRequest } from '@/lib/axios/usePrivateRequest';
import companyInfoService from '@/services/companyInfoService';
import {
  CreateCompanyInfoRequest,
  UpdateCompanyInfoRequest,
} from '@/types/organization';
import { BASE_URL } from '@/utils/apiUrls';

export const COMPANY_INFO_QUERY_KEYS = {
  all: ['company-info'] as const,
  byKey: (agentSuiteKey: string) =>
    [...COMPANY_INFO_QUERY_KEYS.all, agentSuiteKey] as const,
  byTenant: (tenantId: string, agentSuiteKey: string) =>
    [
      ...COMPANY_INFO_QUERY_KEYS.all,
      'tenant',
      tenantId,
      agentSuiteKey,
    ] as const,
};

export const useGetCompanyInfo = (agentSuiteKey: string, options = {}) => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useQuery({
    queryKey: COMPANY_INFO_QUERY_KEYS.byKey(agentSuiteKey),
    queryFn: async () => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return companyInfoService.getCompanyInfo(
        axiosInstance.current,
        agentSuiteKey
      );
    },
    enabled: !!axiosInstance.current && !!agentSuiteKey,
    ...options,
  });
};

export const useCreateCompanyInfoMutation = () => {
  const queryClient = useQueryClient();
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: CreateCompanyInfoRequest) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return companyInfoService.createCompanyInfo(
        axiosInstance.current,
        payload
      );
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: COMPANY_INFO_QUERY_KEYS.byKey(variables.agentSuiteKey),
      });
    },
  });
};

export const useUpdateCompanyInfoMutation = () => {
  const queryClient = useQueryClient();
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useMutation({
    mutationFn: async (payload: UpdateCompanyInfoRequest) => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return companyInfoService.updateCompanyInfo(
        axiosInstance.current,
        payload
      );
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: COMPANY_INFO_QUERY_KEYS.byKey(variables.agentSuiteKey),
      });
    },
  });
};

export const useGetCompanyInfoByTenant = (
  tenantId: string,
  agentSuiteKey: string,
  options = {}
) => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return useQuery({
    queryKey: COMPANY_INFO_QUERY_KEYS.byTenant(tenantId, agentSuiteKey),
    queryFn: async () => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return companyInfoService.getCompanyInfoByTenant(
        axiosInstance.current,
        tenantId,
        agentSuiteKey
      );
    },
    enabled: !!axiosInstance.current && !!tenantId && !!agentSuiteKey,
    ...options,
  });
};

import { usePrivateRequest } from '@/lib/axios/usePrivateRequest';
import locationsService from '@/services/locationsService';
import {
  CountryWithStates,
  FetchOptionsResult,
  LocationOption,
} from '@/types/locations';
import { BASE_URL } from '@/utils/apiUrls';

export const LOCATIONS_QUERY_KEYS = {
  all: ['locations'] as const,
  countriesWithStates: (search?: string, page?: number, pageSize?: number) =>
    [
      ...LOCATIONS_QUERY_KEYS.all,
      'countries-with-states',
      search,
      page,
      pageSize,
    ] as const,
};

/**
 * Hook to fetch countries with states
 * Used for paginated and searchable country/state selection
 */
export const useGetCountriesWithStates = (
  search?: string,
  page: number = 0,
  pageSize: number = 10,
  options = {}
) => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return {
    queryKey: LOCATIONS_QUERY_KEYS.countriesWithStates(search, page, pageSize),
    queryFn: async () => {
      if (!axiosInstance.current) {
        throw new Error('Axios instance not ready');
      }
      return locationsService.getCountriesWithStates(axiosInstance.current, {
        search,
        page,
        pageSize,
      });
    },
    enabled: !!axiosInstance.current,
    ...options,
  };
};

/**
 * Hook that returns a function to fetch countries options for react-select-async-paginate
 * Transforms API response into LocationOption format
 */
export const useFetchCountriesOptions = () => {
  const axiosInstance = usePrivateRequest(BASE_URL);

  return async (search: string, page: number): Promise<FetchOptionsResult> => {
    const current = axiosInstance.current;

    if (!current) {
      return { options: [], hasMore: false };
    }

    try {
      const response = await locationsService.getCountriesWithStates(current, {
        search,
        page: page - 1, // Convert to 0-based indexing
        pageSize: 10,
      });

      const countries: CountryWithStates[] = response.data?.countries || [];
      const options: LocationOption[] = countries.map(country => ({
        value: country.iso2,
        label: country.name,
        countryCode: country.iso2,
      }));

      // Determine if there are more pages
      const hasMore = options.length === 10; // If we got a full page, there might be more

      return {
        options,
        hasMore,
      };
    } catch (error) {
      return { options: [], hasMore: false };
    }
  };
};

/**
 * Helper function to create a states fetch function from a country's states array
 * This is used when a country is already selected and we have its states
 */
export const createStatesFetchFunction = (
  country: CountryWithStates | null
) => {
  return async (search: string, page: number): Promise<FetchOptionsResult> => {
    if (!country) {
      return { options: [], hasMore: false };
    }

    // Get states array from the country
    let states = country.states || [];

    // Filter states based on search query
    if (search) {
      states = states.filter(state =>
        state.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Paginate states
    const startIndex = (page - 1) * 10;
    const endIndex = startIndex + 10;
    const paginatedStates = states.slice(startIndex, endIndex);

    const options: LocationOption[] = paginatedStates.map(stateName => ({
      value: stateName,
      label: stateName,
      stateName: stateName,
      countryCode: country.iso2,
    }));

    const hasMore = endIndex < states.length;

    return {
      options,
      hasMore,
    };
  };
};

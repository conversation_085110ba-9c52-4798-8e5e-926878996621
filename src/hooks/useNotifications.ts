import { useCallback } from 'react';

import { useNotificationContext } from '@/context/NotificationContext';
import {
  AddNotificationOptions,
  NotificationType,
} from '@/types/notifications';

export const useNotifications = () => {
  const {
    addNotification,
    removeNotification,
    clearNotifications,
    notifications,
    isLoading,
  } = useNotificationContext();

  /**
   * Add a simple notification with just a message
   */
  const notify = useCallback(
    (message: string | React.ReactNode, type?: NotificationType) => {
      if (!message || (typeof message === 'string' && message.trim() === '')) {
        console.error('[useNotifications] notify: Message is required');
        throw new Error('Notification message is required');
      }

      return addNotification({
        message: typeof message === 'string' ? message.trim() : message,
        type,
      });
    },
    [addNotification]
  );

  /**
   * Add a notification with additional image (e.g., service logo)
   */
  const notifyWithImage = useCallback(
    (
      message: string | React.ReactNode,
      additionalImage: string,
      additionalImageClassname?: string,
      type?: NotificationType
    ) => {
      if (!message || (typeof message === 'string' && message.trim() === '')) {
        console.error(
          '[useNotifications] notifyWithImage: Message is required'
        );
        throw new Error('Notification message is required');
      }

      if (!additionalImage || additionalImage.trim() === '') {
        console.error(
          '[useNotifications] notifyWithImage: Additional image URL is required'
        );
        throw new Error('Additional image URL is required');
      }

      return addNotification({
        message: typeof message === 'string' ? message.trim() : message,
        additionalImage: additionalImage.trim(),
        additionalImageClassname,
        type: type || 'info',
      });
    },
    [addNotification]
  );

  /**
   * Add a notification with custom agent override
   */
  const notifyWithAgent = useCallback(
    (message: string, agentName: string, avatar: string) => {
      if (!message || message.trim() === '') {
        console.error(
          '[useNotifications] notifyWithAgent: Message is required'
        );
        throw new Error('Notification message is required');
      }

      if (!agentName || agentName.trim() === '') {
        console.error(
          '[useNotifications] notifyWithAgent: Agent name is required'
        );
        throw new Error('Agent name is required');
      }

      if (!avatar || avatar.trim() === '') {
        console.error(
          '[useNotifications] notifyWithAgent: Avatar URL is required'
        );
        throw new Error('Avatar URL is required');
      }

      return addNotification({
        message: message.trim(),
        activeAgent: {
          agentName: agentName.trim(),
          avatar: avatar.trim(),
        },
      });
    },
    [addNotification]
  );

  /**
   * Add a notification with full customization options
   */
  const notifyCustom = useCallback(
    (options: AddNotificationOptions) => {
      if (
        !options.message ||
        (typeof options.message === 'string' && options.message.trim() === '')
      ) {
        console.error('[useNotifications] notifyCustom: Message is required');
        throw new Error('Notification message is required');
      }

      return addNotification(options);
    },
    [addNotification]
  );

  /**
   * Remove a specific notification
   */
  const dismiss = useCallback(
    (id: string) => {
      if (!id || id.trim() === '') {
        console.error(
          '[useNotifications] dismiss: Notification ID is required'
        );
        return;
      }
      removeNotification(id);
    },
    [removeNotification]
  );

  /**
   * Clear all notifications
   */
  const dismissAll = useCallback(() => {
    clearNotifications();
  }, [clearNotifications]);

  /**
   * Get notification by ID for programmatic access
   */
  const getNotification = useCallback(
    (id: string) => {
      return notifications.find(notification => notification.id === id);
    },
    [notifications]
  );

  /**
   * Check if a specific notification exists
   */
  const hasNotification = useCallback(
    (id: string) => {
      return notifications.some(notification => notification.id === id);
    },
    [notifications]
  );

  return {
    // Core functions
    addNotification,
    removeNotification,
    clearNotifications,

    // Convenience methods with validation
    notify,
    notifyWithImage,
    notifyWithAgent,
    notifyCustom,
    dismiss,
    dismissAll,

    // Query methods
    getNotification,
    hasNotification,

    // State
    notifications,
    isLoading,
    hasNotifications: notifications.length > 0,
    notificationCount: notifications.length,

    // Accessibility helpers
    getAriaLabel: () =>
      notifications.length === 1
        ? '1 notification available'
        : `${notifications.length} notifications available`,
  };
};

export default useNotifications;

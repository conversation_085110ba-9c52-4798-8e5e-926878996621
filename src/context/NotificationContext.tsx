import React, { createContext, useCallback, useContext, useState } from 'react';

import { useTenant } from '@/context/TenantContext';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import {
  AddNotificationOptions,
  NotificationContextValue,
  NotificationData,
  NotificationProviderProps,
} from '@/types/notifications';
import { UserBasicInfoPayload } from '@/types/user';
import { getAgentAvatar, getAgentName } from '@/utils/agentUtils';

/**
 * Context for managing notification state across the application
 */
const NotificationContext = createContext<NotificationContextValue | undefined>(
  undefined
);

/**
 * Generate a unique ID for notifications
 */
const generateNotificationId = (): string => {
  return `notification_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  maxNotifications = 5,
  autoRemoveDelay = 0, // 0 means no auto-removal
}) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Get current agent context to capture agent info at notification creation time
  const { activeAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  /**
   * Add a new notification to the stack
   */
  const addNotification = useCallback(
    (options: AddNotificationOptions): string => {
      // Form validation for required fields
      if (
        !options.message ||
        (typeof options.message === 'string' && options.message.trim() === '')
      ) {
        console.error(
          '[NotificationProvider] Message is required for notifications'
        );
        throw new Error('Notification message is required');
      }

      setIsLoading(true);

      const id = options.id || generateNotificationId();

      // Capture agent information at notification creation time
      // If no custom agent is provided, use the current active agent
      const capturedAgent = options.activeAgent || {
        agentName: getAgentName(activeAgent, userData),
        avatar: getAgentAvatar(activeAgent, userData),
      };

      const newNotification: NotificationData = {
        id,
        message:
          typeof options.message === 'string'
            ? options.message.trim()
            : options.message,
        additionalImage: options.additionalImage,
        additionalImageClassname: options.additionalImageClassname,
        activeAgent: capturedAgent, // Always store captured agent info
        type: options.type,
        createdAt: new Date(),
        isRemoving: false,
      };

      setNotifications(prev => {
        // Add new notification to the top (newest first)
        const updated = [newNotification, ...prev];

        // Enforce maximum notifications limit
        if (updated.length > maxNotifications) {
          // const removed = updated.slice(maxNotifications);
          // console.log(
          //   `[NotificationProvider] Removing ${removed.length} old notifications due to limit`
          // );
          return updated.slice(0, maxNotifications);
        }

        return updated;
      });

      setIsLoading(false);

      // Auto-removal if configured
      if (autoRemoveDelay > 0) {
        setTimeout(() => {
          removeNotification(id);
        }, autoRemoveDelay);
      }

      return id;
    },
    [maxNotifications, autoRemoveDelay, activeAgent, userData]
  );

  /**
   * Remove a notification by ID
   */
  const removeNotification = useCallback((id: string) => {
    setIsLoading(true);

    // Mark notification as removing for exit animation
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, isRemoving: true }
          : notification
      )
    );

    // Remove after animation delay
    setTimeout(() => {
      setNotifications(prev => {
        const filtered = prev.filter(notification => notification.id !== id);
        return filtered;
      });
      setIsLoading(false);
    }, 300); // Match animation duration
  }, []);

  /**
   * Clear all notifications
   */
  const clearNotifications = useCallback(() => {
    setIsLoading(true);

    // Mark all as removing for smooth exit animations
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRemoving: true }))
    );

    // Clear after animation delay
    setTimeout(() => {
      setNotifications([]);
      setIsLoading(false);
    }, 300);
  }, [notifications.length]);

  const contextValue: NotificationContextValue = {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    isLoading,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

/**
 * Hook to access the notification context
 */
export const useNotificationContext = (): NotificationContextValue => {
  const context = useContext(NotificationContext);

  if (!context) {
    throw new Error(
      'useNotificationContext must be used within a NotificationProvider'
    );
  }

  return context;
};

/**
 * Convenience hook for adding notifications with simplified API
 */
export const useNotifications = () => {
  const {
    addNotification,
    removeNotification,
    clearNotifications,
    notifications,
    isLoading,
  } = useNotificationContext();

  /**
   * Add a simple notification with just a message
   */
  const notify = useCallback(
    (message: string, type?: 'success' | 'error' | 'info') => {
      return addNotification({ message, type });
    },
    [addNotification]
  );

  /**
   * Add a notification with additional image (e.g., service logo)
   */
  const notifyWithImage = useCallback(
    (
      message: string,
      additionalImage: string,
      additionalImageClassname?: string,
      type?: 'success' | 'error' | 'info'
    ) => {
      return addNotification({
        message,
        additionalImage,
        additionalImageClassname,
        type,
      });
    },
    [addNotification]
  );

  /**
   * Add a notification with custom agent
   */
  const notifyWithAgent = useCallback(
    (message: string, agentName: string, avatar: string) => {
      return addNotification({
        message,
        activeAgent: { agentName, avatar },
      });
    },
    [addNotification]
  );

  return {
    // Core functions
    addNotification,
    removeNotification,
    clearNotifications,

    // Convenience methods
    notify,
    notifyWithImage,
    notifyWithAgent,

    // State
    notifications,
    isLoading,
    hasNotifications: notifications.length > 0,
    notificationCount: notifications.length,
  };
};

import React, { createContext, useContext, useEffect, useState } from 'react';

import { detectTimezoneViaIP } from '@/services/geolocationService';
import {
  formatTimestamp,
  isValidTimezone,
  TimestampFormat,
} from '@/utils/timezone';

import { useAuth } from './AuthContext';

interface TimezoneContextType {
  userTimezone: string;
  isTimezoneLoaded: boolean;
  formatUserTimestamp: (
    utcTimestamp: string | Date,
    format?: TimestampFormat
  ) => string;
  getCurrentUserTimestamp: (format?: TimestampFormat) => string;
}

const TimezoneContext = createContext<TimezoneContextType | undefined>(
  undefined
);

interface TimezoneProviderProps {
  children: React.ReactNode;
}

export const TimezoneProvider: React.FC<TimezoneProviderProps> = ({
  children,
}) => {
  const [userTimezone, setUserTimezone] = useState<string>('UTC');
  const [isTimezoneLoaded, setIsTimezoneLoaded] = useState<boolean>(false);
  const [isDetecting, setIsDetecting] = useState<boolean>(false);
  const { user, isAuthenticated } = useAuth();

  // Detect timezone using the hierarchy: User Profile -> Browser -> IP Geolocation
  const detectTimezone = async (): Promise<string> => {
    // 1. Try user profile timezone first
    if (isAuthenticated && user?.timezone && isValidTimezone(user.timezone)) {
      return user.timezone;
    }

    // 2. Try browser timezone detection
    try {
      const browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      if (isValidTimezone(browserTimezone)) {
        return browserTimezone;
      }
    } catch (error) {
      console.warn('Failed to detect browser timezone:', error);
    }

    // 3. Try IP-based geolocation as fallback
    const ipTimezone = await detectTimezoneViaIP();
    if (ipTimezone) {
      return ipTimezone;
    }

    // 4. Final fallback to UTC (should rarely happen)
    console.warn('All timezone detection methods failed, falling back to UTC');
    return 'UTC';
  };

  // Load user timezone with the new detection hierarchy
  useEffect(() => {
    const loadTimezone = async () => {
      if (isDetecting) return; // Prevent multiple simultaneous detections

      setIsDetecting(true);
      try {
        const detectedTimezone = await detectTimezone();
        setUserTimezone(detectedTimezone);
        setIsTimezoneLoaded(true);
      } catch (error) {
        console.error('Timezone detection failed:', error);
        setUserTimezone('UTC');
        setIsTimezoneLoaded(true);
      } finally {
        setIsDetecting(false);
      }
    };

    // Only run detection if timezone is not loaded or user profile changed
    if (
      !isTimezoneLoaded ||
      (isAuthenticated && user?.timezone !== userTimezone)
    ) {
      loadTimezone();
    }
  }, [
    isAuthenticated,
    user?.timezone,
    isTimezoneLoaded,
    userTimezone,
    isDetecting,
  ]);

  // Helper function to format timestamps using user's timezone
  const formatUserTimestamp = (
    utcTimestamp: string | Date,
    format: TimestampFormat = 'full'
  ): string => {
    return formatTimestamp(utcTimestamp, userTimezone, format);
  };

  // Helper function to get current timestamp in user's timezone
  const getCurrentUserTimestamp = (
    format: TimestampFormat = 'full'
  ): string => {
    return formatTimestamp(new Date(), userTimezone, format);
  };

  const value: TimezoneContextType = {
    userTimezone,
    isTimezoneLoaded,
    formatUserTimestamp,
    getCurrentUserTimestamp,
  };

  return (
    <TimezoneContext.Provider value={value}>
      {children}
    </TimezoneContext.Provider>
  );
};

export const useTimezone = (): TimezoneContextType => {
  const context = useContext(TimezoneContext);
  if (context === undefined) {
    throw new Error('useTimezone must be used within a TimezoneProvider');
  }
  return context;
};

// Export the context for testing purposes
export { TimezoneContext };

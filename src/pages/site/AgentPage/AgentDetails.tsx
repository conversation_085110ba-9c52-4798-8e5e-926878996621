import { AnimatePresence, motion } from 'framer-motion';
import { ArrowDown, ChevronRight } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import { setIq, vesa } from '@/assets/images';
import { ChatInput } from '@/components/chat/ChatInput';
import MessageComponent from '@/components/chat/MessageComponent';
import { TypingIndicator } from '@/components/chat/TypingIndicator';
import { AgentCard } from '@/components/common/AgentCard';
import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';
import { NotificationContainer } from '@/components/ui';
import AgentSuiteSkeletonLoader from '@/components/ui/AgentSuiteSkeleton';
import { ROUTES } from '@/constants/routes';
import { useAuth } from '@/context/AuthContext';
import { useTenant } from '@/context/TenantContext';
import { agentSuites as mockAgents, featureIcons } from '@/data/constants';
import { useGetAIAgentsData } from '@/hooks/useAgents';
import { useHeartbeat } from '@/hooks/useHeartbeat';
import { useNotifications } from '@/hooks/useNotifications';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { useScyraChatApi } from '@/services/scyraChatService';
import { generateSecureSessionId } from '@/services/upivotalAgenticService';
import { AIAgent, ChatMessage, ChatState } from '@/types/agents';
import { UserBasicInfoPayload } from '@/types/user';
import { capitalizeAgentName } from '@/utils/agentUtils';

interface PageAgentProps {
  isSuite: boolean;
  id: string;
  name: string;
  description: string;
  roleDescription: string;
  roles: string[];
  avatar: string;
}

const AgentDetails = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const chatWithAgent = useScyraChatApi();
  const { isAuthenticated } = useAuth();

  // Chat state management
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    sessionId: generateSecureSessionId(),
  });

  // Chat scroll functionality
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const { agentSuites, isLoadingAgents, isLoadingSuites } =
    useGetAIAgentsData();
  const {
    activeAgent: tenantActiveAgent,
    setActiveAgent,
    waitForAgentReady,
    isAgentSwitching: isTenantAgentSwitching,
  } = useTenant();

  const { agentId: agentSuiteId } = useParams<{ agentId: string }>();
  const agentSuite = agentSuiteId
    ? agentSuites.find(agentSuite => agentSuite.agentSuiteKey === agentSuiteId)
    : null;

  const agents = useMemo(() => {
    if (!agentSuite?.availableAgents) return [];

    // Sort agents by displayOrder in ascending order
    return [...agentSuite.availableAgents].sort(
      (a, b) => a.displayOrder - b.displayOrder
    );
  }, [agentSuite]);

  // Handle location state from HeroSection navigation
  const locationState = location.state as {
    selectedAgent?: AIAgent;
    userMessage?: string;
  } | null;

  // Track if we came from HeroSection with location state
  const [isFromHeroSection, setIsFromHeroSection] = useState(
    !!locationState?.selectedAgent
  );

  const activeAgent =
    (isFromHeroSection && locationState?.selectedAgent) ||
    agents.filter(agent => agent.agentKey === tenantActiveAgent)[0];

  const [pageAgent, setPageAgent] = useState<PageAgentProps>({
    isSuite: true,
    id: agentSuiteId || '',
    name: agentSuite?.agentSuiteName || '',
    description: agentSuite?.description || '',
    roleDescription: agentSuite?.roleDescription || '',
    roles: agentSuite?.roles || [],
    avatar: agentSuite?.avatar || '',
  });

  // Handle suite change
  const handleSuiteChange = (suiteKey: string) => {
    handleShowSuiteDetails();
    navigate(ROUTES.AGENTS_DETAILS(suiteKey));
  };

  // Ensure the overall page is scrolled to top on initial navigation to this page.
  useEffect(() => {
    // Use a setTimeout to allow the route transition to complete before forcing scroll.
    const id = window.setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
      } catch (e) {
        window.scrollTo(0, 0);
      }
    }, 0);

    return () => window.clearTimeout(id);
  }, []);

  useEffect(() => {
    if (!tenantActiveAgent || tenantActiveAgent === 'regis') {
      setPageAgent({
        isSuite: true,
        id: agentSuiteId || '',
        name: agentSuite?.agentSuiteName || '',
        description: agentSuite?.description || '',
        roleDescription: agentSuite?.roleDescription || '',
        roles: agentSuite?.roles || [],
        avatar: agentSuite?.avatar || '',
      });
    } else {
      setPageAgent({
        isSuite: false,
        id: tenantActiveAgent || '',
        name: activeAgent?.agentName || '',
        description: activeAgent?.description || '',
        roleDescription: activeAgent?.roleDescription || '',
        roles: activeAgent?.roles || [],
        avatar: activeAgent?.avatar || '',
      });
    }
  }, [
    tenantActiveAgent,
    agentSuite,
    agentSuiteId,
    activeAgent?.agentName,
    activeAgent?.avatar,
    activeAgent?.description,
    activeAgent?.roleDescription,
    activeAgent?.roles,
  ]);

  // Set active agent in context when coming from HeroSection
  useEffect(() => {
    const switchAgent = async () => {
      if (locationState?.selectedAgent && isFromHeroSection) {
        try {
          await setActiveAgent(locationState.selectedAgent.agentKey);
          // console.log(
          //   `Successfully switched to agent: ${locationState.selectedAgent.agentKey}`
          // );
        } catch (error) {
          // Failed to switch agent from HeroSection
        }
      }
    };

    switchAgent();
  }, [locationState?.selectedAgent, setActiveAgent, isFromHeroSection]);

  // Track auto-send to prevent duplicates
  const hasAutoSentMessage = useRef(false);

  // Track current agent to detect switches
  const currentAgentRef = useRef(activeAgent?.agentKey || null);
  const [isAgentSwitching, setIsAgentSwitching] = useState(false);
  const [isSelectingAgent, setIsSelectingAgent] = useState(false);

  const [showAgentFeatures, setShowAgentFeatures] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const agentFeaturesRef = useRef<HTMLDivElement>(null);
  const toggleAgentFeatures = () => setShowAgentFeatures(previous => !previous);

  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const handleShowSuiteDetails = () => {
    setActiveAgent('');
    setShowAgentFeatures(true);
    setPageAgent({
      isSuite: true,
      id: agentSuiteId || '',
      name: agentSuite?.agentSuiteName || '',
      description: agentSuite?.description || '',
      roleDescription: agentSuite?.roleDescription || '',
      roles: agentSuite?.roles || [],
      avatar: agentSuite?.avatar || '',
    });
  };

  // useOnClickOutside(agentFeaturesRef, () => setShowAgentFeatures(false));

  // Scroll functionality
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, []);

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => container.removeEventListener('scroll', handleScroll);
  }, [chatState.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    if (isUserAtBottom || chatState.messages.length === 0) {
      scrollToBottom();
    }
  }, [chatState.messages, isUserAtBottom, scrollToBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!chatState.isLoading && isUserAtBottom) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [chatState.isLoading, isUserAtBottom, scrollToBottom]);

  const sendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim() || chatState.isLoading) return;

      // Check if we need to set an active agent
      let currentActiveAgent = activeAgent;
      if (!currentActiveAgent) {
        setIsSelectingAgent(true);
        // Find the current suite
        const currentSuite = agentSuites.find(
          agentSuite => agentSuite.agentSuiteKey === agentSuiteId
        );

        if (!currentSuite || !currentSuite.availableAgents?.length) {
          // No suite or agents found for message sending
          return;
        }

        // Get the first agent (agents are already sorted alphabetically by the hook)
        const firstAgent = currentSuite.availableAgents[0];

        try {
          // Set the active agent and wait for the state update to complete
          await setActiveAgent(firstAgent.agentKey);

          // Wait for the agent to be ready before proceeding
          // The waitForAgentReady function will wait for the switching process to complete
          const isReady = await waitForAgentReady(firstAgent.agentKey);
          if (!isReady) {
            // If waitForAgentReady times out or fails, we still try to continue
            // because we just set the agent and it should be valid
            // This handles edge cases where the timing doesn't work perfectly
          }

          // Update our local reference to the active agent
          // Use the firstAgent we just set since we know it's the correct one
          currentActiveAgent = firstAgent;
          setIsSelectingAgent(false);
        } catch (error) {
          // Failed to set active agent
          setIsSelectingAgent(false);
          return;
        }
      }

      // Final validation - ensure we have an active agent
      if (!currentActiveAgent) {
        // No active agent available for message sending
        setIsSelectingAgent(false);
        return;
      }

      // Add user message
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        sender: 'user',
        content: messageContent.trim(),
        timestamp: new Date(),
        senderName: 'You',
      };

      setChatState(prev => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));
      setIsSelectingAgent(false);

      try {
        setIsSelectingAgent(false);

        // Call agent API with the confirmed active agent
        const response = await chatWithAgent({
          userMessage: messageContent.trim(),
          sessionId: chatState.sessionId,
        });

        // Add agent response
        const agentMessage: ChatMessage = {
          id: `${currentActiveAgent.agentKey}-${Date.now()}`,
          sender: currentActiveAgent.agentKey,
          content: response,
          timestamp: new Date(),
          senderName: capitalizeAgentName(currentActiveAgent.agentName),
        };

        setChatState(prev => ({
          ...prev,
          messages: [...prev.messages, agentMessage],
          isLoading: false,
        }));
        setIsSelectingAgent(false);

        window.scrollTo({ top: 10, behavior: 'smooth' });
      } catch (error) {
        setIsSelectingAgent(false);

        // Error sending message to agent

        // Add error message
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          sender: currentActiveAgent.agentKey,
          content: 'Sorry, I encountered an error. Please try again.',
          timestamp: new Date(),
          senderName: capitalizeAgentName(currentActiveAgent.agentName),
        };

        setChatState(prev => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));
      }
    },
    [
      chatWithAgent,
      chatState.sessionId,
      chatState.isLoading,
      activeAgent,
      agentSuites,
      agentSuiteId,
      setActiveAgent,
      waitForAgentReady,
    ]
  );

  // Handle agent switching - clear chat state when agent changes
  useEffect(() => {
    const currentAgentKey = activeAgent?.agentKey || null;
    if (currentAgentRef.current !== currentAgentKey) {
      // Agent has switched, clear chat state and show switching indicator
      setIsAgentSwitching(true);
      setChatState({
        messages: [],
        isLoading: false,
        sessionId: generateSecureSessionId(),
      });

      // Reset auto-send flag for new agent
      hasAutoSentMessage.current = false;

      // Update current agent ref
      currentAgentRef.current = currentAgentKey;

      // Clear switching indicator after a brief moment
      setTimeout(() => {
        setIsAgentSwitching(false);
      }, 500);
    }
  }, [activeAgent?.agentKey]);

  // Auto-send message from HeroSection - use useCallback to prevent re-runs
  const autoSendMessage = useCallback(async () => {
    if (
      locationState?.userMessage &&
      chatState.messages.length === 0 &&
      !hasAutoSentMessage.current &&
      !chatState.isLoading &&
      !isTenantAgentSwitching &&
      isFromHeroSection &&
      locationState?.selectedAgent
    ) {
      // Double-check that we're targeting the correct agent
      const expectedAgent = locationState.selectedAgent.agentKey;
      const currentAgent = tenantActiveAgent;

      if (currentAgent !== expectedAgent) {
        // Agent mismatch before auto-send, waiting for correct agent
        return; // Don't send yet, wait for agent to be correct
      }

      // Wait for agent to be fully ready
      const isReady = await waitForAgentReady(expectedAgent);
      if (!isReady) {
        // Agent not ready for auto-send, skipping message
        return;
      }

      // Final check after waiting
      if (tenantActiveAgent === expectedAgent) {
        hasAutoSentMessage.current = true;
        sendMessage(locationState.userMessage);
      } else {
        // Agent still mismatched after waiting
      }
    }
  }, [
    locationState?.userMessage,
    locationState?.selectedAgent,
    chatState.messages.length,
    chatState.isLoading,
    isTenantAgentSwitching,
    isFromHeroSection,
    tenantActiveAgent,
    waitForAgentReady,
    sendMessage,
  ]);

  // Auto-send message from HeroSection
  useEffect(() => {
    autoSendMessage();
  }, [autoSendMessage]);

  // Initialize notification system
  const { notifications, dismiss } = useNotifications();

  // Get user data to check if suite is already claimed
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites;
  // Check if current user is a member of THIS specific suite and get user role
  const currentUserSuiteInfo = claimedSuites?.find(claimedSuite => {
    // First check if this is the current suite being viewed
    const isCurrentSuite = claimedSuite.suite.agentSuiteKey === agentSuiteId;
    // Then check if user is a member of this suite
    const isUserMember = claimedSuite.members.some(
      member => member?.user?.userId === userData?.userInfo?.userId
    );
    // Both conditions must be true
    return isCurrentSuite && isUserMember;
  });
  const isUserMember = !!currentUserSuiteInfo;

  // Heartbeat functionality
  const {
    heartbeatState,
    fetchHeartbeats,
    initializeHeartbeat,
    pauseHeartbeat,
    getHeartbeatStatus,
  } = useHeartbeat();

  // Fetch heartbeat data when component mounts - only if user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchHeartbeats();
    }
  }, [fetchHeartbeats, isAuthenticated]);

  // Handle heartbeat actions
  const handleHeartbeatAction = async (
    action: 'initialize' | 'pause',
    agentKey: string
  ): Promise<void> => {
    try {
      if (action === 'initialize') {
        await initializeHeartbeat(agentKey);
      } else {
        await pauseHeartbeat(agentKey);
      }
    } catch (error) {
      // Error handling is done in the useHeartbeat hook
      // console.error('Heartbeat action failed:', error);
    }
  };

  if (!agentSuite) {
    if (isLoadingSuites) {
      return <MainLoaderSkeleton />;
    } else {
      return (
        <div className="-mt-20 flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-blackOne">
              Agent Not Found
            </h1>
            <p className="mb-6 text-gray-600">
              The agent you're looking for doesn't exist.
            </p>
            <Link
              to={ROUTES.HOME}
              className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
            >
              Back to Home
            </Link>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="mx-auto mt-2 flex max-w-7xl gap-8 px-4">
      {/* --- Left Sidebar - Agent Suites Navigation --- */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
        className="hidden flex-shrink-0 lg:block"
      >
        <div className="sticky top-8 mt-10 w-[148px] overflow-hidden rounded-2xl bg-lightOrangeTwo">
          {/* Header Section */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="flex h-[50px] items-center justify-between rounded-2xl bg-lightPeach px-3 py-3"
          >
            <h3 className="font-inter text-sm font-medium text-subText sm:text-base">
              Agent Suites
            </h3>
            <motion.button
              onClick={() => setSidebarCollapsed(prev => !prev)}
              animate={{ rotate: sidebarCollapsed ? 0 : 90 }}
              transition={{ duration: 0.3 }}
              aria-label={
                sidebarCollapsed
                  ? 'Expand suites sidebar'
                  : 'Collapse suites sidebar'
              }
              className="focus:outline-none"
              style={{
                background: 'none',
                border: 'none',
                padding: 0,
                margin: 0,
              }}
            >
              <ChevronRight className="h-4 w-4 text-primary" />
            </motion.button>
          </motion.div>

          {/* Suite List */}
          <AnimatePresence>
            {!sidebarCollapsed && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden bg-lightOrangeTwo"
              >
                <div className="pb-2">
                  {agentSuites.map((suite, index) => (
                    <motion.div
                      key={suite.agentSuiteKey}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
                    >
                      <motion.button
                        onClick={() => handleSuiteChange(suite.agentSuiteKey)}
                        whileHover={{ scale: 1.02, x: 4 }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ duration: 0.2 }}
                        className={`w-full px-3 py-2.5 text-left font-inter text-sm font-medium text-subText transition-colors sm:text-base ${
                          suite.agentSuiteKey === agentSuiteId
                            ? 'border-b border-primary'
                            : 'border-b'
                        }`}
                      >
                        {suite.agentSuiteName}
                      </motion.button>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Vertical Divider */}
      <div className="hidden lg:block">
        <div className="h-full w-px bg-lightPeach" />
      </div>

      {/* --- Main Content and Right Sidebar --- */}
      <div className="flex-1 pb-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* --- Main Content --- */}
          <div className="lg:col-span-2">
            {/* Notifications Container */}
            <NotificationContainer
              notifications={notifications}
              onClose={dismiss}
              className="mb-4 w-full"
              maxNotifications={2}
            />
            {/* LHS - Header */}
            <div className="relative h-[198px] overflow-hidden rounded-[14px] bg-cover bg-center">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: agentSuite.avatar
                    ? `url(${agentSuite.avatar})`
                    : `url(${setIq})`,
                }}
              />
              {/* Dark Overlay */}
              <div className="absolute inset-0 bg-black/20" />
              {/* Content */}
              <div className="relative z-10 flex h-full flex-col justify-center p-6">
                <p
                  className="flex w-fit items-center justify-center rounded-lg bg-white px-4 py-3 text-lg font-semibold backdrop-blur-sm transition-all hover:shadow-md md:text-[26px]"
                  // onClick={handleShowSuiteDetails}
                >
                  {agentSuite.agentSuiteName}
                </p>

                <h2 className="mt-6 w-fit whitespace-pre-line font-spartan text-base font-semibold text-white md:text-xl">
                  {agentSuite.description}
                </h2>
                <div className="text-sm text-white md:text-lg">
                  {agentSuite.roleDescription}
                </div>
              </div>
            </div>

            {/* Agent Chips Carousel - Mobile Only */}
            <div className="relative mt-4 bg-[#FFF1EB] py-4 lg:hidden">
              {agents.length > 0 && (
                <>
                  <div className="overflow-hidden">
                    <div
                      className="flex gap-4 overflow-x-auto px-5 [&::-webkit-scrollbar]:hidden"
                      style={{
                        scrollbarWidth: 'none',
                        msOverflowStyle: 'none',
                      }}
                    >
                      {agents.map(agent => (
                        <button
                          key={agent.agentKey}
                          onClick={async () => {
                            setIsFromHeroSection(false);
                            window.history.replaceState(
                              null,
                              '',
                              window.location.pathname
                            );
                            try {
                              await setActiveAgent(agent.agentKey);
                            } catch (error) {
                              // Failed to switch agent
                            }
                          }}
                          className="flex flex-shrink-0 touch-manipulation flex-row items-center gap-1 bg-white px-3 py-2 font-inter text-xs font-medium transition"
                        >
                          <div className="relative flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-[#FFE0D1]">
                            <img
                              src={agent.avatar}
                              className="mb-px h-6 w-6 shrink-0 object-contain"
                              alt={agent.agentName}
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-blackOne">
                              {agent.agentName}
                            </span>
                            <Icons.Chat2 className="h-4 w-4" />
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                  {agents.length > 2 && (
                    <>
                      <button
                        onClick={() => {
                          const container =
                            document.querySelector('.overflow-x-auto');
                          if (container)
                            container.scrollBy({
                              left: -200,
                              behavior: 'smooth',
                            });
                        }}
                        className="absolute left-2 top-1/2 z-10 flex h-8 w-8 -translate-y-1/2 items-center justify-center rounded-full bg-grayTwentyEight transition hover:bg-gray-50"
                        aria-label="Previous agent"
                      >
                        <ChevronRight className="h-5 w-5 rotate-180 text-blackOne" />
                      </button>
                      <button
                        onClick={() => {
                          const container =
                            document.querySelector('.overflow-x-auto');
                          if (container)
                            container.scrollBy({
                              left: 200,
                              behavior: 'smooth',
                            });
                        }}
                        className="absolute right-2 top-1/2 z-10 flex h-8 w-8 -translate-y-1/2 items-center justify-center rounded-full bg-grayTwentyEight transition hover:bg-gray-50"
                        aria-label="Next agent"
                      >
                        <ChevronRight className="h-5 w-5 text-blackOne" />
                      </button>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Active agent */}
            {chatState.messages && chatState.messages.length === 0 && (
              <div className="relative mt-4 w-full rounded-[14px] border border-peachTwo">
                <div
                  ref={agentFeaturesRef}
                  className="flex cursor-pointer items-center gap-4 rounded-lg p-3 md:p-4"
                  onClick={!isSelectingAgent ? toggleAgentFeatures : undefined}
                >
                  {/* Agent Avatar */}
                  <div className="h-12 min-w-12 rounded-full bg-peachTwo">
                    <img
                      src={pageAgent.avatar}
                      className="h-12 w-12 rounded-full object-cover"
                      alt={pageAgent.name}
                      onError={e => {
                        const fallbackAgent = mockAgents.find(
                          agent =>
                            agent.id.toLowerCase() ===
                            pageAgent.id.toLowerCase()
                        );
                        if (fallbackAgent) {
                          (e.target as HTMLImageElement).src =
                            fallbackAgent.image;
                        }
                      }}
                    />
                  </div>

                  <div className="flex w-full items-center justify-between text-sm md:text-base">
                    {isSelectingAgent ? (
                      // Enhanced Connecting state with rotating messages
                      <div className="flex items-center gap-3">
                        {/* Animated dots */}
                        <div className="flex space-x-1">
                          <div className="h-2 w-2 animate-pulse rounded-full bg-orangeFourFade"></div>
                          <div className="animation-delay-150 h-2 w-2 animate-pulse rounded-full bg-orangeFourFade"></div>
                          <div className="animation-delay-300 h-2 w-2 animate-pulse rounded-full bg-orangeFourFade"></div>
                        </div>
                        <div className="flex flex-col">
                          <p className="text-sm font-medium text-darkGray">
                            <ConnectionStatusRotator agent={pageAgent.name} />
                          </p>
                          <p className="mt-1 text-xs text-gray-500">
                            Initializing {pageAgent.name || 'agent'}...
                          </p>
                        </div>
                      </div>
                    ) : pageAgent.isSuite ? (
                      // Suite agent state
                      <div>
                        <p className="text-lg font-bold text-blackOne">
                          {pageAgent.name} Features
                        </p>
                        <p className="text-lg text-blackOne">
                          Suite — Team Capabilities
                        </p>
                      </div>
                    ) : (
                      // Individual agent state
                      <div>
                        <p className="text-darkGray">
                          Hi, I'm{' '}
                          <span className="font-semibold">
                            {`${pageAgent.name} — ${pageAgent.description}`}
                          </span>
                        </p>
                        <p>{pageAgent.roleDescription}</p>
                      </div>
                    )}

                    {/* Chevron - only show when not connecting */}
                    {!isSelectingAgent && (
                      <ChevronRight
                        className={`${
                          showAgentFeatures ? '-rotate-90' : 'rotate-0'
                        } text-primary transition-transform duration-200`}
                      />
                    )}
                  </div>
                </div>

                {/* Agent Features - only show when expanded and not connecting */}
                {showAgentFeatures && !isSelectingAgent && (
                  <div className="mb-2 w-full border-t border-peachTwo p-3 md:p-6">
                    <div className="flex h-[20vh] flex-col gap-2.5 overflow-auto md:h-[25vh] md:gap-4">
                      {pageAgent.roles.map((feature, index) => (
                        <div
                          key={`${feature}-${index}`}
                          className="flex items-center gap-4"
                        >
                          <img
                            src={featureIcons[index % featureIcons.length]}
                            alt=""
                            className="w-4 md:w-5"
                          />
                          <p className="text-sm font-medium leading-relaxed text-blackTwo">
                            {feature}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Enhanced Chat Interface */}
            <div className="bg-white font-inter">
              {chatState.messages.length === 0 ? (
                // Initial state - only show input field
                !isSelectingAgent && (
                  <div className="flex h-20 items-center gap-2">
                    <div className="h-12 min-w-12 rounded-full bg-peachTwo">
                      <img
                        key={pageAgent.id}
                        src={
                          pageAgent.isSuite && agentSuite.availableAgents?.[0]
                            ? agentSuite.availableAgents[0].avatar
                            : pageAgent.avatar
                        }
                        className="h-12 w-12 rounded-full object-cover"
                        alt={
                          pageAgent.isSuite && agentSuite.availableAgents?.[0]
                            ? agentSuite.availableAgents[0].agentName
                            : pageAgent.name
                        }
                        onError={e => {
                          const fallbackAgent = mockAgents.find(
                            agent =>
                              agent.id.toLowerCase() ===
                              (pageAgent.isSuite &&
                              agentSuite.availableAgents?.[0]
                                ? agentSuite.availableAgents[0].agentKey.toLowerCase()
                                : pageAgent.id.toLowerCase())
                          );
                          if (fallbackAgent) {
                            (e.target as HTMLImageElement).src =
                              fallbackAgent.image;
                          }
                        }}
                      />
                    </div>
                    <ChatInput
                      onSendMessage={sendMessage}
                      agent={
                        activeAgent?.agentKey || agentSuite.availableAgents?.[0]
                          ? agentSuite.availableAgents[0].agentKey
                          : 'colton'
                      }
                      placeholder={`${
                        pageAgent.isSuite && agentSuite.availableAgents?.[0]
                          ? agentSuite.availableAgents[0].agentName
                          : pageAgent.name
                      } here — ${isMobile ? 'when' : 'whenever'} you're ready.`}
                      disabled={chatState.isLoading || isAgentSwitching}
                    />
                  </div>
                )
              ) : (
                // Full chat interface with messages
                <div className="relative flex h-[calc(100vh-400px)] flex-col md:h-[calc(100vh-320px)]">
                  {/* Messages Container */}
                  <div
                    ref={messagesContainerRef}
                    className="flex-1 overflow-y-auto py-4"
                    style={{ minHeight: 0 }}
                  >
                    {chatState.messages.map(message => (
                      <MessageComponent
                        key={message.id}
                        message={message}
                        agentAvatar={pageAgent.avatar}
                      />
                    ))}

                    {/* Typing Indicator */}
                    {(chatState.isLoading || isAgentSwitching) && (
                      <TypingIndicator
                        agentImageSrc={pageAgent.avatar || vesa}
                        agentName={capitalizeAgentName(pageAgent.name)}
                        message={
                          isAgentSwitching
                            ? `${capitalizeAgentName(pageAgent.name)} is connecting`
                            : undefined
                        }
                      />
                    )}
                  </div>

                  {/* Floating Down Arrow */}
                  {showScrollToBottom && (
                    <button
                      className="absolute bottom-24 right-6 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-lg transition hover:bg-gray-50"
                      onClick={scrollToBottom}
                      aria-label="Scroll to latest message"
                    >
                      <ArrowDown className="h-6 w-6 text-primary" />
                    </button>
                  )}

                  {/* Chat Input */}
                  <div className="flex-shrink-0 px-4 py-4">
                    <ChatInput
                      onSendMessage={sendMessage}
                      placeholder="I'm here — whenever you're ready."
                      disabled={chatState.isLoading || isAgentSwitching}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* --- Sidebar --- */}
          <div className="hidden lg:col-span-1 lg:block">
            {isLoadingAgents || isLoadingSuites ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <div className="flex flex-col gap-4">
                {agents.map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    className="w-full max-w-[334px]"
                    agent={agent}
                    showChatButton
                    showHeartbeatControl={isAuthenticated && isUserMember}
                    heartbeatStatus={getHeartbeatStatus(agent.agentKey)}
                    onHeartbeatAction={handleHeartbeatAction}
                    isHeartbeatLoading={
                      heartbeatState.loadingAgent === agent.agentKey
                    }
                    isActiveAgent={tenantActiveAgent === agent.agentKey}
                    link="#"
                    onAgentSelect={async () => {
                      setIsFromHeroSection(false);
                      window.history.replaceState(
                        null,
                        '',
                        window.location.pathname
                      );
                      try {
                        await setActiveAgent(agent.agentKey);
                      } catch (error) {
                        // Failed to switch agent
                      }
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDetails;

const ConnectionStatusRotator: React.FC<{ agent?: string }> = ({
  agent = 'agent',
}) => {
  const [currentStatus, setCurrentStatus] = useState(0);

  const statusMessages = [
    `Establishing connection to ${agent}...`,
    `Connected to ${agent}`,
    'Sending message...',
    'Ready for interaction...',
    'Ready for interaction...',
    'Syncing data streams...',
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStatus(prev => (prev + 1) % statusMessages.length);
    }, 1500); // Change message every 1.5 seconds

    return () => clearInterval(interval);
  }, [statusMessages.length]);

  return (
    <span className="inline-block min-w-[200px]">
      {statusMessages[currentStatus]}
    </span>
  );
};

import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';

import { bgGradient, eclipseShadow } from '@/assets/images';
import AgentSkeleton from '@/components/ui/AgentSkeleton';
import { ROUTES } from '@/constants/routes';
import { agentSuites as mockAgents } from '@/data/constants';
import { useGetAIAgentSuites } from '@/hooks/useAgents';

export const AgentTeamsSection = () => {
  // Use React Query hook for data fetching
  const { data: rawAgentSuites = [], isLoading } = useGetAIAgentSuites();

  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Check if device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Initialize arrow visibility based on content and screen size
  useEffect(() => {
    if (scrollRef.current && rawAgentSuites.length > 0) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Determine if right arrow should be shown based on array length and screen size
      const shouldShowRightArrow = isMobile
        ? rawAgentSuites.length > 1
        : rawAgentSuites.length > 4;

      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(
        shouldShowRightArrow && scrollLeft < scrollWidth - clientWidth - 1
      );
    }
  }, [rawAgentSuites.length, isMobile]);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Determine if right arrow should be shown based on array length and screen size
      const shouldShowRightArrow = isMobile
        ? rawAgentSuites.length > 1
        : rawAgentSuites.length > 4;

      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(
        shouldShowRightArrow && scrollLeft < scrollWidth - clientWidth - 1
      );
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = direction === 'left' ? -300 : 300;
      scrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  return (
    <section className="bg-white pb-10 pt-20">
      {/* Gradient overlay */}
      <div
        className="pointer-events-none absolute inset-0 z-0"
        style={{
          backgroundImage: `url(${bgGradient})`,
          backgroundSize: 'auto',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />

      <div className="app-container relative z-10 mx-auto -mt-20">
        <div
          className="pointer-events-none absolute left-[18%] top-3 z-0 h-72 w-72 bg-no-repeat"
          style={{
            backgroundImage: `url(${eclipseShadow})`,
            backgroundSize: 'contain',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            // transform: 'translate(15%, -30%)',
          }}
        />
        <div className="text-center">
          <h2 className="mb-4 text-left text-xl font-semibold md:text-center md:text-3xl md:font-bold">
            Meet The AI Agent Teams
          </h2>
          <p className="mx-auto mb-12 text-left font-inter text-[#262626] sm:text-lg md:text-center">
            Each Agentous team is a suite of purpose-trained AI Agents designed
            to deliver and sustain key business functions.
          </p>
        </div>

        <div className="relative w-full">
          {/* Left Arrow */}
          {!isLoading && showLeftArrow && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>
          )}

          {/* AI Agents */}
          <div className="flex justify-center">
            <div
              ref={scrollRef}
              onScroll={handleScroll}
              className={`no-scrollbar flex snap-x snap-mandatory gap-4 overflow-x-auto scroll-smooth pb-6 ${
                !isLoading &&
                rawAgentSuites.length < 5 &&
                'justify-start lg:justify-center'
              }`}
            >
              {isLoading
                ? Array.from({ length: 4 }).map((_, index) => (
                    <AgentSkeleton key={`skeleton-${index}`} />
                  ))
                : rawAgentSuites.map((suite, index) => (
                    <Link
                      to={ROUTES.AGENTS_DETAILS(suite.agentSuiteKey)}
                      key={index}
                      className="flex w-[172px] flex-shrink-0 cursor-pointer flex-col items-center overflow-hidden rounded-md shadow-[0px_4px_8px_0px_#98A2B31A] transition-all sm:w-[292px] sm:border sm:shadow-none"
                    >
                      <div
                      // onClick={() => {
                      //   if (suite.agentKey) {
                      //     setActiveAgent(suite.agentKey);
                      //   } else {
                      //     setActiveAgent('');
                      //   }
                      // }}
                      >
                        <img
                          src={suite.avatar}
                          className="h-[120px] w-full bg-peachTwo object-cover text-xs sm:h-56"
                          alt={suite.agentSuiteName}
                          onError={e => {
                            const agentKey = suite.availableAgents.find(
                              agent =>
                                agent.agentKey ===
                                suite.availableAgents[0].agentKey
                            )?.avatar;

                            // Fallback to mock logo if agent avatar fails to load
                            (e.target as HTMLImageElement).src =
                              mockAgents.filter(
                                agent => agent.id.toLowerCase() === agentKey
                              )[0].image;
                          }}
                        />
                        <div className="mt-2 flex flex-col gap-2 p-2 text-blackOne sm:mt-0 sm:gap-4 sm:p-4">
                          <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] text-sm font-semibold sm:text-base">
                            {suite.agentSuiteName}
                          </div>
                          <p className="whitespace-pre-line text-[13px] font-medium sm:text-lg sm:font-semibold">
                            {suite.description}
                          </p>
                          <p className="mb-2 font-inter text-[13px] text-darkGray sm:text-base">
                            {suite.roleDescription}
                          </p>
                        </div>
                      </div>
                    </Link>
                  ))}
            </div>
          </div>

          {/* Right Arrow */}
          {!isLoading && showRightArrow && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll right"
            >
              <ChevronRight className="h-8 w-8" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
};

import { checkCircle, enterpriseLayers } from '@/assets/images';

const features = [
  'Subscription billing with usage-based overage tracking',
  'Low-latency orchestration logic built for real-time response',
  'Seamless integration with your existing business stack',
  'Scalable deployment across departments and workflows',
  'Built-in usage insights, access control, and audit trails',
  'Intelligent routing, retry logic, and policy enforcement for every task',
];

export const TransformationSection = () => {
  return (
    <section className="font-inter text-darkGray">
      <div className="container relative z-10 mx-auto max-w-screen-3xl bg-lightOrangeTwo py-10 md:py-16">
        <div className="app-container flex flex-col md:flex-row">
          {/* Left Column */}
          <div className="relative md:w-1/2">
            <div className="w-full border md:absolute md:inset-y-0 md:left-0 md:border-gray-300">
              <div className="h-full overflow-auto">
                <div className="min-h-1/2 flex flex-col items-center justify-center gap-2 p-4 text-center md:max-w-[540px] md:p-6 md:text-left">
                  <h2 className="text-left text-xl font-semibold leading-[28px] text-blue-midnight sm:text-2xl sm:font-bold lg:text-[32px]">
                    The Transformation Layer Your Enterprise Is Missing
                  </h2>
                  <p className="text-left text-base leading-[24px] lg:text-lg">
                    Across every agent, PivoTL coordinates decisions, tasks,
                    data, and actions through a secure, orchestrated
                    infrastructure.
                  </p>
                </div>

                <hr />

                <div className="min-h-1/2 flex flex-col justify-center space-y-4 p-4 sm:space-y-2 md:max-w-[540px] md:p-6">
                  {features.map((feature, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-3 overflow-hidden"
                    >
                      <img
                        src={checkCircle}
                        alt="✓"
                        className="h-4 w-4 flex-shrink-0"
                      />
                      <p className="text-sm leading-[100%] text-darkGray sm:font-medium">
                        {feature}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="md:w-1/2">
            <img
              src={enterpriseLayers}
              alt="Transformation Layer Diagram"
              className="h-full max-h-[500px] w-full object-contain"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

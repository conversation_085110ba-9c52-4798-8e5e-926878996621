import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import AgentSkeleton from '@/components/ui/AgentSkeleton';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import {
  agentCategories,
  marketplaceAgents as mockAgents,
} from '@/data/constants';
import { useGetAIAgents, useGetAIAgentSuites } from '@/hooks/useAgents';

export const MarketplaceSection = () => {
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const [showCategoryLeftArrow, setShowCategoryLeftArrow] = useState(false);
  const [showCategoryRightArrow, setShowCategoryRightArrow] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const categoryScrollRef = useRef<HTMLDivElement>(null);
  const { setActiveAgent } = useTenant();
  const navigate = useNavigate();

  // Use React Query hooks for data fetching
  const { data: agents = [], isLoading } = useGetAIAgents();
  const { data: suites = [] } = useGetAIAgentSuites();

  // Build suite order map (suites come sorted alphabetically from hook changes)
  const suiteOrderMap = new Map<string, number>(
    suites.map((s, idx) => [s.agentSuiteKey, idx])
  );
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Map categories to suite keys
  const categoryToSuiteMap: { [key: string]: string[] } = {
    COLLECTION_SERVICES: ['set-iq'],
    UNDERWRITING_SERVICES: ['under-wing'],
    ADMINISTRATIVE_SERVICES: ['admin-wing'],
    SALES_OPERATIONS: ['sales-wing'],
  };

  // Compute filtered agents based on selected category
  const agentsList =
    selectedCategory === 'all'
      ? agents // Show all agents
      : agents.filter(agent => {
          const suiteKeys = categoryToSuiteMap[selectedCategory];
          return suiteKeys?.includes(agent.agentSuiteKey || '');
        });

  const orderedAgentsList = [...agentsList].sort((a, b) => {
    const aOrder =
      suiteOrderMap.get(a.agentSuiteKey || '') ?? Number.MAX_SAFE_INTEGER;
    const bOrder =
      suiteOrderMap.get(b.agentSuiteKey || '') ?? Number.MAX_SAFE_INTEGER;
    if (aOrder !== bOrder) return aOrder - bOrder;
    return (a.agentName || '').localeCompare(b.agentName || '', undefined, {
      sensitivity: 'base',
    });
  });

  // Check if device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Initialize arrow visibility based on content and screen size
  useEffect(() => {
    if (scrollRef.current && orderedAgentsList.length > 0) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Determine if right arrow should be shown based on array length and screen size
      const shouldShowRightArrow = isMobile
        ? orderedAgentsList.length > 1
        : orderedAgentsList.length > 4;

      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(
        shouldShowRightArrow && scrollLeft < scrollWidth - clientWidth - 1
      );
    }
  }, [orderedAgentsList.length, isMobile]);

  // Initialize category arrow visibility
  useEffect(() => {
    if (categoryScrollRef.current && isMobile) {
      const { scrollLeft, scrollWidth, clientWidth } =
        categoryScrollRef.current;
      setShowCategoryLeftArrow(scrollLeft > 0);
      setShowCategoryRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, [isMobile]);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // Determine if right arrow should be shown based on array length and screen size
      const shouldShowRightArrow = isMobile
        ? orderedAgentsList.length > 1
        : orderedAgentsList.length > 4;

      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(
        shouldShowRightArrow && scrollLeft < scrollWidth - clientWidth - 1
      );
    }
  };

  const handleCategoryScroll = () => {
    if (categoryScrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        categoryScrollRef.current;
      setShowCategoryLeftArrow(scrollLeft > 0);
      setShowCategoryRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = direction === 'left' ? -300 : 300;
      scrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  const scrollCategories = (direction: 'left' | 'right') => {
    if (categoryScrollRef.current) {
      const scrollAmount = direction === 'left' ? -200 : 200;
      categoryScrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  const handleAgentsFilter = (categoryId: string) => {
    if (categoryId === selectedCategory) {
      setSelectedCategory('all');
    } else {
      setSelectedCategory(categoryId);
    }
  };

  return (
    <section className="bg-white pb-8 pt-8 sm:pt-12">
      <div className="app-container relative z-10">
        <div className="text-center">
          <h2 className="mb-4 text-left text-xl font-semibold capitalize sm:text-center sm:text-3xl sm:font-bold">
            Agentic AI marketplace for Enterprise automation
          </h2>
          <p className="mx-auto mb-6 text-left font-inter text-darkGray sm:text-center sm:text-lg">
            Deploy AI agents that work around the clock — scoring leads,
            resolving tasks, and scaling operations with precision.
          </p>
        </div>

        {/* Agent Categories */}
        <div className="relative mb-6">
          {/* Category Left Arrow - Mobile Only */}
          {isMobile && showCategoryLeftArrow && (
            <button
              onClick={() => scrollCategories('left')}
              className="absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll categories left"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
          )}

          {/* Category Right Arrow - Mobile Only */}
          {isMobile && showCategoryRightArrow && (
            <button
              onClick={() => scrollCategories('right')}
              className="absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll categories right"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          )}

          <div
            ref={categoryScrollRef}
            onScroll={handleCategoryScroll}
            className={`bg-darkGray p-4 ${
              isMobile
                ? 'no-scrollbar flex gap-4 overflow-x-auto scroll-smooth'
                : 'flex flex-wrap justify-center gap-4'
            }`}
          >
            {agentCategories.map((category, index) => (
              <button
                key={index}
                className={`rounded-md ${
                  selectedCategory === category.id
                    ? 'bg-primary text-white hover:bg-darkOrangeTwo'
                    : 'bg-grayFifteen text-blackOne hover:bg-blue-50'
                } whitespace-nowrap px-4 py-2.5 font-inter font-medium transition ${
                  isMobile ? 'flex-shrink-0' : ''
                }`}
                onClick={() => handleAgentsFilter(category.id)}
              >
                {category.alias}
              </button>
            ))}
          </div>
        </div>

        <div className="relative w-full">
          {/* Left Arrow */}
          {!isLoading && showLeftArrow && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>
          )}

          {/* AI Agents */}
          <div className="flex justify-center font-inter">
            <div
              ref={scrollRef}
              onScroll={handleScroll}
              className={`no-scrollbar flex snap-x snap-mandatory gap-6 overflow-x-auto scroll-smooth pb-6 pt-2 ${
                !isLoading &&
                agents.length < 4 &&
                'justify-start lg:justify-center'
              }`}
            >
              {isLoading ? (
                Array.from({ length: 4 }).map((_, index) => (
                  <AgentSkeleton key={`skeleton-${index}`} />
                ))
              ) : orderedAgentsList.length === 0 ? (
                <div className="flex flex-col items-center justify-center">
                  {Array.from({ length: 1 }).map((_, index) => (
                    <AgentSkeleton key={`skeleton-${index}`} />
                  ))}
                  <p className="py-6 text-center font-medium text-gray-500">
                    No agents available yet for this category
                  </p>
                </div>
              ) : (
                orderedAgentsList.map((agent, index) => (
                  <div
                    key={index}
                    className="flex w-[186px] flex-shrink-0 cursor-pointer flex-col overflow-hidden rounded-lg bg-white shadow-[0px_4px_8px_0px_#98A2B31A] transition-all sm:w-[290px] sm:border sm:shadow-none"
                    onClick={() => {
                      setActiveAgent(agent.agentKey);
                      navigate(ROUTES.AGENTS_DETAILS(agent.agentSuiteKey!));
                    }}
                  >
                    <div className="bg-peachTwo">
                      <img
                        src={agent.avatar}
                        className="h-[120px] w-full object-contain sm:h-56"
                        alt={agent.agentName}
                        onError={e => {
                          // Fallback to mock logo if agent avatar fails to load
                          const mockAgent =
                            mockAgents.find(
                              mockAgent =>
                                mockAgent.name.toLowerCase() ===
                                agent.agentName.toLowerCase()
                            ) || mockAgents[0];
                          (e.target as HTMLImageElement).src = mockAgent?.image;
                        }}
                      />
                    </div>
                    <div className="flex flex-1 flex-col p-2 sm:mt-0 sm:gap-4 sm:p-4">
                      <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] text-sm font-semibold sm:text-base ">
                        {agent.agentName}
                      </div>
                      <p className="text-[13px] font-medium sm:text-lg sm:font-semibold">
                        {agent.description}
                      </p>
                      <p className="font-inter text-[13px] text-subText sm:text-base">
                        {agent.roleDescription}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Right Arrow */}
          {!isLoading && showRightArrow && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll right"
            >
              <ChevronRight className="h-8 w-8" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
};

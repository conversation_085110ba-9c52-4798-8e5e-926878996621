import { motion } from 'framer-motion';
import React, { useEffect } from 'react';

import { pricingHeader } from '@/assets/images';

const termsOfUse = [
  {
    id: 1,
    title: 'Scope of Services',
    content: [
      'Purpose. Agentous provides consulting, deployment, and configuration of AI Agents suites across business functions such as collections, underwriting, sales operations, and administrative services.',
      'Deliverables. Services may include AI Agents suites (e.g., SetIQ, UnderwriteIQ, SalesWing, AdminWing) comprising AI agents optimized to perform workflows, decisions, and communications.',
      'Updates. Agentous may update Services, pricing sheets, and features from time to time. Continued use constitutes acceptance of such updates.',
    ],
  },
  {
    id: 2,
    title: 'Website and Platform Use',
    content: [
      'Eligibility. By accessing the website or platform, User represents that they are legally able to enter into binding agreements.',
      'Account Access. Some features require account creation. User is responsible for safeguarding credentials and all activity under their account.',
      'Prohibited Use. User may not copy, reverse engineer, decompile, disrupt, or misuse the Services. User may not upload malicious code, post unlawful content, or use the Services for illegal, fraudulent, or abusive purposes.',
      'Availability. Agentous may suspend or limit access for maintenance, upgrades, or misuse.',
    ],
  },
  {
    id: 3,
    title: 'Ownership and License',
    content: [
      'Agentous IP. All rights, title, and interest in the Services—including AI agents, AI Agents suites, models, algorithms, training routines, code, documentation, and related intellectual property—are and remain the sole property of Agentous.',
      ' License. User receives a limited, revocable, non-exclusive, non-transferable, non-sublicensable license to use the Services during an active subscription, solely for internal business purposes.',
      'No Transfer. No ownership rights are transferred to User.',
      'Self-Optimization. AI agents may adapt over time through exposure to User’s workflows. Such improvements remain the property of Agentous.',
      'User Data. User retains ownership of all data provided. Derivative datasets created exclusively from User Data also remain User’s property. Agentous will not use User Data beyond providing Services to that User.',
      'Feedback. Suggestions or feedback provided by User may be used by Agentous without restriction.',
    ],
  },
  {
    id: 4,
    title: 'Fees and Payment',
    content: [
      'Fees. User agrees to pay fees as specified in applicable pricing schedules or order forms.',
      'Payment Terms. Setup and upgrade fees are invoiced upon order execution. Monthly subscription fees begin in the first full month after an AI Agent completes its first acceptable production task. Invoices are due within thirty (30) days.',
      'Late Payment. Payments not received within ten (10) days of the due date accrue interest at 1.5% per month or the maximum lawful rate.',
      'Pricing Adjustments. Agentous may adjust fees with ninety (90) days’ prior notice.',
    ],
  },
  {
    id: 5,
    title: 'Confidentiality',
    content: [
      'Definition. Confidential Information includes all non-public information disclosed by either party that should reasonably be understood as confidential.',
      'Obligations. Each party shall protect and not disclose Confidential Information except as permitted under these Terms.',
      'Exceptions. Confidential Information does not include information that is public, previously known, independently developed, or lawfully obtained.',
    ],
  },
  {
    id: 6,
    title: 'Assumption of Risk and Disclaimers',
    content: [
      'LLM Fallibility. Large language models may generate inaccurate, incomplete, or biased outputs. User assumes all risk of relying on such outputs and must validate results.',
      'No Professional Advice. Outputs are not legal, medical, financial, compliance, or other professional advice. User is solely responsible for decisions and outcomes.',
      'Compliance Responsibility. User is responsible for complying with all laws and regulations applicable to their use of the Services.',
      'Third-Party Services. Agentous is not responsible for third-party tools, APIs, or infrastructure relied on by the Services.',
    ],
  },
  {
    id: 7,
    title: 'Warranties and Disclaimers',
    content: [
      'Authority. Each party warrants that it has authority to enter into these Terms.',
      'As Is / As Available. Except as expressly stated, the Services are provided “as is” and “as available,” without warranties of merchantability, fitness for a particular purpose, accuracy, or non-infringement.',
      'Security. Agentous uses commercially reasonable safeguards but does not guarantee immunity from unauthorized access, loss, or corruption.',
      'No Malicious Code. Agentous will not knowingly deliver malicious code.',
    ],
  },
  {
    id: 8,
    title: 'Indemnification',
    content: [
      'User shall defend, indemnify, and hold harmless Agentous and its affiliates, officers, directors, employees, and agents from all claims, damages, losses, and expenses (including attorneys’ fees) arising out of or related to:',
      '— User Data or instructions,',
      '— User’s use of or reliance on outputs,',
      '— User’s violation of law or third-party rights, or',
      '— User’s breach of these Terms.',
    ],
  },
  {
    id: 9,
    title: 'Limitation of Liability',
    content: [
      'Exclusions. Neither party is liable for indirect, incidental, special, punitive, or consequential damages, including lost profits or data.',
      'Cap. Agentous’s total liability is limited to the fees paid by User in the twelve (12) months preceding the claim.',
    ],
  },
  {
    id: 10,
    title: 'Term, Suspension, and Termination',
    content: [
      'Term. These Terms remain effective for one (1) year from the Effective Date and renew annually unless terminated with sixty (60) days’ written notice.',
      'Termination for Convenience. Either party may terminate for convenience with sixty (60) days’ notice.',
      'Termination for Cause. Either party may terminate immediately for uncured material breach or insolvency.',
      'Suspension. Agentous may suspend access for non-payment, misuse, or security risks.',
      'Effect. Upon termination, all accrued fees become immediately due. Sections on ownership, confidentiality, warranties, indemnification, and limitation of liability survive.',
    ],
  },
  {
    id: 11,
    title: 'Dispute Resolution and Arbitration',
    content: [
      'Good-Faith Negotiation. Before filing any claim, both User and Agentous agree to try to resolve the dispute informally by providing written notice and engaging in good-faith discussions for at least 30 days.',
      'Binding Arbitration. If the dispute is not resolved through negotiation, it shall be resolved by final and binding arbitration, rather than in court, except as provided in Section 11.5. Arbitration will be conducted by JAMS (or the American Arbitration Association if JAMS is unavailable) under its commercial arbitration rules. The arbitration shall take place in Wilmington, Delaware, and may be conducted virtually if both parties agree.',
      'No Jury Trial. By agreeing to arbitration, both User and Agentous waive the right to a trial by jury.',
      'Class Action Waiver. All claims must be brought in an individual capacity, and not as a plaintiff or class member in any purported class, collective, or representative proceeding. The arbitrator may not consolidate claims of more than one person.',
      'Injunctive Relief and IP Protection. Either party may seek injunctive or equitable relief in the Delaware Court of Chancery or the U.S. federal courts located in Delaware for the purpose of protecting intellectual property, confidential information, or preventing unauthorized use of the Services.',
      'Costs of Arbitration. The arbitrator will decide how arbitration costs are allocated, but Agentous will pay any administrative or filing fees that would exceed what User would normally pay in a court proceeding.',
      'Opt-Out Option. User may opt out of this arbitration agreement by sending written <NAME_EMAIL> within 30 days of first accepting these Terms. If User opts out, disputes will be resolved in the state or federal courts located in Delaware.',
    ],
  },
  {
    id: 12,
    title: 'Governing Law',
    content: [
      '.These Terms are governed by the laws of the State of Delaware, without regard to conflict-of-law principles.',
    ],
  },
  {
    id: 13,
    title: 'Miscellaneous',
    content: [
      'Force Majeure. Neither party is liable for delays caused by events beyond reasonable control.',
      'Beta Features. Beta or trial features are provided “as is,” may change, and carry no warranties.',
      'Assignment. User may not assign rights under these Terms without Agentous’s prior written consent.',
      'Entire Agreement. These Terms, together with applicable pricing and policies, constitute the entire agreement.',
      'Amendments. No modifications are valid unless in writing and signed by Agentous.',
      'Non-Solicitation. During the term and for two (2) years after termination, User shall not solicit Agentous employees or contractors without consent.',
      'Severability. If any provision is found unenforceable, the remainder remains in effect.',
      'Notices. Notices to Agentous shall be <NAME_EMAIL> or to Agentous’s headquarters. Notices to User shall be directed to the email or address associated with the account.',
    ],
  },
];

const formatContent = (
  text: string,
  showNumber: boolean,
  id: number,
  idx: number
) => {
  // Check if it's a bullet
  const isBullet = text.trim().startsWith('—');

  // Detect emails and wrap in semibold
  const formattedText = text.replace(
    /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
    "<span class='font-semibold'>$1</span>"
  );

  if (isBullet) {
    return (
      <li
        key={idx}
        className="leading-relaxed text-gray-700"
        dangerouslySetInnerHTML={{ __html: formattedText.replace('—', '') }}
      />
    );
  }

  // Split first sentence at the first period
  const firstPeriodIndex = text.indexOf('.');
  if (firstPeriodIndex > -1) {
    const beforePeriod = text.substring(0, firstPeriodIndex + 1);
    const afterPeriod = text.substring(firstPeriodIndex + 1);

    return (
      <p key={idx} className="leading-relaxed text-gray-700">
        {showNumber && (
          <span className="mr-1 font-medium">
            {id}.{idx + 1}.
          </span>
        )}
        {!beforePeriod.startsWith('.') && (
          <span className="font-semibold">{beforePeriod}</span>
        )}
        <span
          dangerouslySetInnerHTML={{ __html: afterPeriod }}
          className="font-normal"
        />
      </p>
    );
  }

  // No period — everything normal (with number only if needed)
  return (
    <p
      key={idx}
      className="leading-relaxed text-gray-700"
      dangerouslySetInnerHTML={{
        __html: `${
          showNumber
            ? `<span class='font-medium mr-1'>${id}.${idx + 1}.</span>`
            : ''
        }${formattedText}`,
      }}
    />
  );
};

const TermsOfUsePage: React.FC = () => {
  useEffect(() => {
    const id = window.setTimeout(() => {
      try {
        window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
      } catch {
        window.scrollTo(0, 0);
      }
    }, 0);
    return () => window.clearTimeout(id);
  }, []);

  return (
    <div className="mx-auto max-w-4xl py-6">
      <div className="relative h-[198px] overflow-hidden rounded-lg border bg-gray-200 bg-cover bg-center">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url(${pricingHeader})`,
          }}
        />
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-black/20" />
        {/* Content */}
        <div className="relative z-10 flex h-full flex-col justify-center px-8 py-6 md:px-12">
          <div className="flex w-fit items-center justify-center rounded bg-white px-4 py-2.5 text-xl font-bold backdrop-blur-sm md:text-[32px]">
            Terms of Use
          </div>

          <div className="mt-6 font-inter text-sm text-white md:text-lg">
            These Terms establish the legal framework guiding organizational
            access, acceptable use, and service obligations.
          </div>
        </div>
      </div>

      <p className="px-4 py-6 text-center text-sm text-gray-700 md:text-left">
        <strong>Effective Date:</strong> September 7, 2025
      </p>
      <motion.section
        className="px-4 pb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="space-y-10">
          <div className="text-gray-900">
            These Terms of Use (“Terms”) govern access to and use of Agentous
            websites, platforms, software, models, and AI Agents suites
            (collectively, the “Services”). By accessing or using the Services,
            you (“User”) agree to these Terms.
          </div>
          {termsOfUse.map(term => {
            const nonBulletItems = term.content.filter(
              c => !c.trim().startsWith('—')
            );
            const showNumbering = nonBulletItems.length > 1;

            return (
              <motion.div
                key={term.id}
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <h2 className="text-lg font-semibold text-gray-900">
                  {term.id}. {term.title}
                </h2>
                <div className="ml-6 space-y-3">
                  {term.content.some(c => c.trim().startsWith('—')) ? (
                    <ul className="ml-6 list-disc space-y-2">
                      {term.content.map((c, idx) =>
                        formatContent(c, showNumbering, term.id, idx)
                      )}
                    </ul>
                  ) : (
                    term.content.map((c, idx) =>
                      formatContent(c, showNumbering, term.id, idx)
                    )
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
      </motion.section>
    </div>
  );
};

export default TermsOfUsePage;

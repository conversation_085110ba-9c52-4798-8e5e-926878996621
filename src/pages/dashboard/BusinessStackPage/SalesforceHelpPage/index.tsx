import { ArrowLeft, Check, Settings } from 'lucide-react';
import React, { useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import { useConnectionFlow } from '@/hooks/useConnectionFlow';

const SalesforceHelpPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Get modal param from URL or location state to preserve it when navigating back
  const searchParams = new URLSearchParams(location.search);
  const modalParam =
    searchParams.get('modal') ||
    (location.state as { returnWithModal?: string })?.returnWithModal;

  // Initialize connection flow for chat sidebar
  const connectionFlow = useConnectionFlow(() => {
    // No need to update app connection state on this page
  });

  return (
    <DashboardWithChatLayout
      connectionFlow={connectionFlow}
      reloadChatHistoryRef={reloadChatHistoryRef}
    >
      <div className="min-h-full bg-gray-50 p-8">
        <div className="max-w-[674px]">
          {/* Back Button */}
          <button
            onClick={() =>
              navigate(
                modalParam
                  ? `/dashboard/business-stack?modal=${modalParam}`
                  : '/dashboard/business-stack'
              )
            }
            className="mb-6 flex items-center gap-2 text-sm hover:underline sm:text-base"
          >
            <ArrowLeft className="h-5 w-5 text-primary" />
            Agentous Auth/OpenID App
          </button>

          {/* Title */}
          <h1 className="mb-6 text-lg font-semibold text-subText">
            Create The Agentous Internal Connected App (Salesforce)
          </h1>

          {/* Sign in to the correct org */}
          <section className="mb-6 rounded-lg border border-lightPeach bg-lightOrangeTwo p-4">
            <h2 className="mb-4 text-lg font-medium text-subText">
              Sign in to the correct org
            </h2>
            <p className="mb-4 text-base text-subText">
              Log in to the Salesforce org you want Agentous to access (prod or
              staging).
            </p>

            <div className="mb-6">
              <div className="mb-2 block font-semibold text-subText">
                Instance URL (copy this now):{' '}
                <span className="mb-2 text-base font-normal text-subText">
                  From the browser address bar after login copy just the domain,
                  e.g.
                </span>
              </div>

              <div className="mb-4 flex gap-4">
                <div className="flex-1">
                  <p className="mb-2 text-sm font-medium text-subText">Prod:</p>
                  <a
                    href="https://abc.my.salesforce.com"
                    className="rounded-lg border border-grayTwentyEight bg-white p-2 text-sm text-[#FF6636] hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    https://abc.my.salesforce.com
                  </a>
                </div>
                <div className="flex-1">
                  <p className="mb-2 text-sm font-medium text-subText">
                    Sandbox
                  </p>
                  <a
                    href="https://abc--sandbox.my.salesforce.com"
                    className="rounded-lg border border-gray-200 bg-white p-2 text-sm text-[#FF6636] hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    https://abc--sandbox.my.salesforce.com
                  </a>
                </div>
              </div>
            </div>

            <p className="text-sm text-subText">
              This is what goes into the Agentous "Instance URL" field.
            </p>
          </section>

          {/* Open App Manager and create the app */}
          <section className="mb-6 rounded-lg border border-lightPeach bg-lightOrangeTwo p-4">
            <h2 className="mb-4 text-lg font-medium text-subText">
              Open App Manager and create the app
            </h2>

            <div className="space-y-3">
              <div className="flex items-center gap-2 text-base text-subText">
                Click the gear{' '}
                <Settings className="h-3.5 w-3.5 text-[#618FB0]" />→{' '}
                <span className="font-semibold">Setup</span>
              </div>
              <p className="text-base text-subText">
                In Quick Find, type{' '}
                <span className="font-semibold">App Manager</span> → Open{' '}
                <span className="font-semibold">App Manager</span>
              </p>
              <p className="text-base text-subText">
                Click <span className="font-semibold">New Connected App</span>{' '}
                (top-right)
              </p>
            </div>
          </section>

          {/* Fill out the Connected App */}
          <section className="mb-6 rounded-lg border border-lightPeach bg-lightOrangeTwo p-4">
            <h2 className="mb-4 text-lg font-medium text-subText">
              Fill out the Connected App (enable OAuth)
            </h2>

            <div className="space-y-3">
              <p className="text-base text-subText">
                <span className="font-semibold">Connected App Name:</span>{' '}
                Agentous
              </p>
              <p className="text-base text-subText">
                <span className="font-semibold">Contact Email:</span> your email
              </p>
              <p className="flex items-center gap-2 text-base font-semibold text-subText">
                API (Enable OAuth Settings):
                <Check className="h-4 w-4" /> Enable OAuth Settings
              </p>

              <div className="space-y-3">
                <p className="text-base text-subText">
                  <span className="font-semibold">Callback URL:</span>{' '}
                  <a
                    href="https://agentous.ai/oauth/callback/salesforce"
                    className="text-[#FF6636] underline"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    https://agentous.ai/oauth/callback/salesforce
                  </a>
                </p>

                <p className="text-base text-subText">
                  <span className="font-semibold">Selected OAuth Scopes:</span>
                </p>
                <ol className="ml-6 list-decimal space-y-1 text-sm text-subText">
                  <li>Access and manage your data (api)</li>
                  <li>
                    Perform requests on your behalf at any time (refresh_token,
                    offline_access)
                  </li>
                  <li>(Optional) openid, id, email</li>
                </ol>

                <p className="text-base text-subText">
                  <span className="font-semibold">
                    Require Secret for Web Server Flow:
                  </span>{' '}
                  ON (client-secret approach)
                </p>
                <p className="text-base text-subText">
                  (PKCE can be left OFF for this quick path)
                </p>
              </div>

              <p className="text-base font-normal text-subText">
                <span className="font-semibold">
                  Click Save. Wait ~2-10 minutes
                </span>{' '}
                for Salesforce to propagate the new app.
              </p>
            </div>
          </section>

          {/* Get the credentials */}
          <section className="mb-6 rounded-lg border border-lightPeach bg-lightOrangeTwo p-4">
            <h2 className="mb-4 text-lg font-medium text-subText">
              Get the credentials & pre-authorize
            </h2>

            <div className="space-y-3">
              <p className="text-base text-subText">
                Back in <span className="font-semibold">App Manager</span>, find
                the new <span className="font-semibold">Agentous</span> row →
                click the ▼ → <span className="font-semibold">View</span>.
              </p>

              <div className="space-y-3">
                <p className="text-base text-subText">
                  •{' '}
                  <span className="font-semibold">
                    Client ID (Consumer Key):
                  </span>{' '}
                  shown on the detail page.
                </p>
                <p className="text-base text-subText">
                  •{' '}
                  <span className="font-semibold">
                    Client Secret (Consumer Secret):
                  </span>{' '}
                  click <span className="font-semibold">Reveal</span> to copy.
                </p>
              </div>

              <p className="text-base text-subText">
                Then click ▼ → <span className="font-semibold">Manage:</span>
              </p>

              <div className="space-y-3">
                <p className="text-base text-subText">
                  • <span className="font-semibold">OAuth Policies</span> →{' '}
                  <span className="font-semibold">Permitted Users:</span> set to{' '}
                  <span className="font-semibold">
                    Admin approved users are pre-authorized
                  </span>{' '}
                  → <span className="font-semibold">Save</span>.
                </p>

                <p className="text-base text-subText">
                  •{' '}
                  <span className="font-semibold">
                    Grant access via Permission Set
                  </span>{' '}
                  (recommended):
                </p>
                <p className="text-base text-subText">
                  • <span className="font-normal">Setup</span> →{' '}
                  <span className="font-semibold">Permission Sets</span> →
                  create (e.g., Agentous Integration Access ) →{' '}
                  <span className="font-semibold">Assigned Connected Apps</span>{' '}
                  → <span className="font-semibold">Edit</span> → add{' '}
                  <span className="font-semibold">Agentous</span> →{' '}
                  <span className="font-semibold">Save</span> →{' '}
                  <span className="font-semibold">Manage Assignments</span> →
                  assign to your{' '}
                  <span className="font-semibold">integration user</span>.
                </p>
              </div>
            </div>
          </section>

          {/* Where to paste things in Agentous */}
          <section className="mb-6 rounded-lg border border-lightPeach bg-lightOrangeTwo p-4">
            <h2 className="mb-4 text-lg font-semibold text-subText">
              Where to paste things in Agentous
            </h2>

            <div className="space-y-3">
              <p className="text-base text-subText">
                • <span className="font-semibold">Instance URL:</span> the
                domain you copied in Step 1 (e.g.,{' '}
                <a
                  href="https://abc.my.salesforce.com"
                  className="text-primary underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  https://abc.my.salesforce.com
                </a>
                ).
              </p>
              <p className="text-base text-subText">
                • <span className="font-semibold">Client ID:</span> the{' '}
                <span className="font-semibold">Consumer Key</span> from Step 4.
              </p>
              <p className="text-base text-subText">
                •{' '}
                <span className="font-semibold">
                  Client Secret (Consumer Secret):
                </span>{' '}
                click <span className="font-semibold">Reveal</span> to copy.
              </p>

              <p className="text-base text-subText">
                Click <span className="font-semibold">Test & Connect</span> in
                Agentous. If the Connected App has finished propagating, the
                handshake should succeed and Agentous will store the tokens
                securely.
              </p>
            </div>
          </section>
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};

export default SalesforceHelpPage;

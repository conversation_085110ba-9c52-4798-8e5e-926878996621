import { AxiosError } from 'axios';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import { regis } from '@/assets/images';
import { OTPVerification } from '@/components/common/OTPVerification';
import { PasswordChangeForm } from '@/components/common/PasswordChangeForm';
import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import Alert from '@/components/ui/Alert';
import { useTenant } from '@/context/TenantContext';
import {
  useChangePasswordMutation,
  useRequestTokenMutation,
  useValidateTokenMutation,
} from '@/hooks/useUserProfile';

import { ROUTES } from '../../../../constants/routes';

interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const ChangePassword: React.FC = () => {
  const navigate = useNavigate();
  const { setActiveAgent } = useTenant();
  const changePasswordMutation = useChangePasswordMutation();
  const requestTokenMutation = useRequestTokenMutation();
  const validateTokenMutation = useValidateTokenMutation();

  const [step, setStep] = useState<'otp' | 'password'>('otp');
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  const [chatMessage] = useState<string>('');
  const [isResendLoading, setIsResendLoading] = useState(false);
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  const handleOTPSubmit = async (code: string) => {
    setSubmitError(null);

    try {
      // Validate the OTP token
      await validateTokenMutation.mutateAsync(code);

      // Store the validated OTP for later use
      sessionStorage.setItem('validatedOTP', code);

      // Move to password input step
      setStep('password');
    } catch (error) {
      // Extract error message from API response
      let errorMessage = 'Failed to validate OTP';

      if (error instanceof AxiosError && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setSubmitError(errorMessage);
    }
  };

  const handlePasswordSubmit = async (data: ChangePasswordFormData) => {
    setSubmitError(null);

    try {
      // Get stored OTP token from session storage
      const storedOTP = sessionStorage.getItem('validatedOTP');
      if (!storedOTP) {
        throw new Error('OTP validation not found');
      }

      // Change the password with the validated token
      await changePasswordMutation.mutateAsync({
        token: storedOTP,
        oldPassword: data.currentPassword,
        newPassword: data.newPassword,
      });

      setSubmitSuccess('Password changed successfully!');
      sessionStorage.removeItem('passwordChangeData');
      sessionStorage.removeItem('validatedOTP');

      // Navigate back after 2 seconds
      setTimeout(() => {
        navigate('/dashboard/settings/profile');
      }, 2000);
    } catch (error) {
      // Extract error message from API response
      let errorMessage = 'Failed to change password';

      if (error instanceof AxiosError && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setSubmitError(errorMessage);
    }
  };

  const handleResendCode = async () => {
    setSubmitError(null);
    setIsResendLoading(true);

    try {
      await requestTokenMutation.mutateAsync();
    } catch (error) {
      let errorMessage = 'Failed to resend verification code';

      if (error instanceof AxiosError && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setSubmitError(errorMessage);
    } finally {
      setIsResendLoading(false);
    }
  };

  const handleForgotPassword = () => {
    // TODO: Implement forgot password flow
  };

  const isOTPLoading = validateTokenMutation.isPending;
  const isPasswordLoading = changePasswordMutation.isPending;

  return (
    <DashboardWithChatLayout
      reloadChatHistoryRef={reloadChatHistoryRef}
      externalMessage={chatMessage}
    >
      <div className="relative flex-1 overflow-hidden p-4 sm:p-6">
        <div className="flex items-center space-x-1">
          <Link
            to={ROUTES.DASHBOARD_SETTINGS_PROFILE}
            className="cursor-pointer text-sm font-normal text-primary underline sm:text-base lg:text-lg"
          >
            My Profile
          </Link>
          <ChevronRight className="h-4 w-4 text-subText" strokeWidth={3} />
          <h3 className="text-sm font-normal text-subText sm:text-base lg:text-lg">
            Change password
          </h3>
        </div>

        {/* Alert - Relative to this section */}
        {submitError && (
          <div className="mt-4">
            <Alert
              message={submitError}
              type="error"
              clearMode="manual"
              onClose={() => setSubmitError(null)}
              agent={{
                name: 'Regis',
                avatar: regis,
              }}
              showIcon={true}
            />
          </div>
        )}

        {submitSuccess && (
          <div className="mt-4">
            <Alert
              message={submitSuccess}
              type="success"
              clearMode="manual"
              onClose={() => setSubmitSuccess(null)}
              agent={{
                name: 'Regis',
                avatar: regis,
              }}
              showIcon={true}
            />
          </div>
        )}

        <AnimatePresence mode="wait">
          {step === 'otp' ? (
            <motion.div
              key="otp"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="h-full w-full"
            >
              <OTPVerification
                title="Email Confirmation"
                isLoading={isOTPLoading}
                error={null}
                success={submitSuccess}
                onSubmit={handleOTPSubmit}
                onResend={handleResendCode}
                setError={setSubmitError}
                isResendLoading={isResendLoading}
              />
            </motion.div>
          ) : (
            <motion.div
              key="password"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="h-full w-full overflow-y-auto"
            >
              <PasswordChangeForm
                isLoading={isPasswordLoading}
                error={submitError}
                success={submitSuccess}
                onSubmit={handlePasswordSubmit}
                setError={setSubmitError}
                onForgotPassword={handleForgotPassword}
                showCurrentPassword={true}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </DashboardWithChatLayout>
  );
};

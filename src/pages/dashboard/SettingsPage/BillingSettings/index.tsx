import { motion } from 'framer-motion';
import React, { useLayoutEffect, useRef, useState } from 'react';

import { americanExpressIcon, masterCardIcon, visaIcon } from '@/assets/images';

const tabs = [
  { id: 'subscription', label: 'Subscription' },
  { id: 'billing', label: 'Billing settings' },
  { id: 'security', label: 'Security' },
];

export const BillingSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('subscription');
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);

  // Animated tab underline logic
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const [underlineProps, setUnderlineProps] = useState({ left: 0, width: 0 });
  const activeTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  useLayoutEffect(() => {
    if (activeTabIndex !== -1 && tabRefs.current[activeTabIndex]) {
      const el = tabRefs.current[activeTabIndex];
      setUnderlineProps({
        left: el.offsetLeft - 31,
        width: el.offsetWidth,
      });
    }
  }, [activeTab, tabs]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'subscription':
        return (
          <div className="w-[536px] space-y-6 rounded-md bg-white p-4">
            {/* SetIQ Trial Info */}
            <div className="flex flex-col items-start gap-2">
              <div className="flex items-center gap-2">
                <div className="flex h-9 w-9 items-center justify-center rounded-full bg-black text-xs">
                  <img src="/" alt="logo" />
                </div>
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-black md:text-lg xl:text-2xl">
                    SetIQ
                  </h3>
                  <span className="border border-black bg-transparent px-2 py-1 text-xs text-black">
                    Trial
                  </span>
                </div>
              </div>{' '}
              <p className="text-sm text-black md:text-base">
                Collection Services AI Agents Suite
              </p>
            </div>

            {/* Usage Stats */}
            <div className="space-y-2">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-subText">Activities</span>
                  <span className="text-sm font-medium text-subText">
                    0 / 1,000
                  </span>
                </div>
                <div className="h-2 w-full rounded-full bg-[#E8E7E4]">
                  <div
                    className="h-2 rounded-full bg-primary"
                    style={{ width: '0%' }}
                  ></div>
                </div>
                <p className="font-meidum text-sm text-subText">
                  Trial ends on December 08, 2025
                </p>
                {/* Description */}
                <p className="text-sm text-subText md:text-base">
                  Resolve accounts with precision, empathy, speed, and
                  compliance.
                </p>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between gap-3">
                <button className="font-spartan text-sm text-primary underline">
                  Compare AI Agents Plans
                </button>
                <button className="rounded-md bg-peach-5 px-4 py-2 text-sm text-primary transition-colors hover:bg-primary/40">
                  Upgrade
                </button>
              </div>
            </div>
          </div>
        );

      case 'billing':
        return (
          <div className="w-[536px] space-y-6 rounded-md bg-white p-4">
            {/* Payment Information */}
            <h3 className="text-paragraph font-semibold sm:text-lg">
              Payment information
            </h3>
            <p className="text-xs text-black sm:text-[13px]">
              Add a payment method to make purchases and keep your account
              active.
            </p>
            <div className="flex items-center justify-between">
              {/* Payment Method Icons */}
              <div className="flex gap-3">
                <img
                  src={americanExpressIcon}
                  alt="AMEX"
                  className="h-8 w-12 rounded object-contain"
                />
                <img
                  src={masterCardIcon}
                  alt="MC"
                  className="h-8 w-12 rounded object-contain"
                />
                <img
                  src={visaIcon}
                  alt="VISA"
                  className="h-8 w-12 rounded object-contain"
                />
              </div>

              <button className="rounded-md bg-peach-5 px-4 py-2 text-sm text-primary transition-colors hover:bg-primary/10">
                Add payment method
              </button>
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="w-[374px] space-y-6 rounded-md bg-white p-4">
            {/* Two-factor Authentication */}
            <div>
              <h3 className="text-paragraph mb-4 text-sm font-medium">
                Two-factor Authentication
              </h3>

              <div className="mb-6 flex items-center justify-start gap-4">
                <div className="relative">
                  <input
                    type="checkbox"
                    id="two-factor"
                    className="sr-only"
                    checked={twoFactorEnabled}
                    onChange={() => setTwoFactorEnabled(!twoFactorEnabled)}
                  />
                  <label
                    htmlFor="two-factor"
                    className="flex cursor-pointer items-center"
                  >
                    <div
                      className={`h-6 w-12 rounded-full shadow-inner transition-colors ${
                        twoFactorEnabled ? 'bg-primary' : 'bg-gray-300'
                      }`}
                    >
                      <div
                        className={`h-6 w-6 transform rounded-full bg-white shadow transition-transform ${
                          twoFactorEnabled ? 'translate-x-6' : 'translate-x-0'
                        }`}
                      ></div>
                    </div>
                  </label>
                </div>
                <span className="text-[13px] text-black">
                  Enable or disable two factor authentication
                </span>
              </div>
            </div>

            {/* Current Password */}
            <div>
              <h3 className="mb-3 text-sm font-medium text-black">
                Current Password
              </h3>
              <div className="w-full max-w-md rounded-md border border-gray-200 bg-gray-50 p-3">
                <span className="text-gray-400">••••••••••</span>
              </div>
              <button className="mt-2 text-sm text-primary hover:underline">
                Change Password
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-black">Billing and Usage</h2>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="relative flex space-x-8">
          {tabs.map((tab, idx) => (
            <button
              key={tab.id}
              ref={el => (tabRefs.current[idx] = el)}
              onClick={() => setActiveTab(tab.id)}
              className={`px-1 py-2 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-primary'
                  : 'text-gray-500 hover:text-black'
              }`}
            >
              {tab.label}
            </button>
          ))}
          {/* Animated underline */}
          {activeTabIndex !== -1 && (
            <motion.div
              layout
              className="absolute bottom-0 h-0.5 rounded bg-primary"
              style={{ left: underlineProps.left, width: underlineProps.width }}
              transition={{ type: 'spring', stiffness: 400, damping: 30 }}
            />
          )}
        </nav>
      </div>

      {/* Tab Content */}
      <div>{renderTabContent()}</div>
    </div>
  );
};

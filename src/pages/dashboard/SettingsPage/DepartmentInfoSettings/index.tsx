import { yupResolver } from '@hookform/resolvers/yup';
import CountryList from 'country-list-with-dial-code-and-flag';
import { ChevronLeft, Loader2 } from 'lucide-react';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import * as yup from 'yup';

import { Spinner } from '@/components/common/Loader';
import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import {
  Button,
  Input,
  NotificationContainer,
  PhoneInput,
} from '@/components/ui';
import { ROUTES } from '@/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import {
  useCreateDepartmentInfoMutation,
  useGetDepartmentInfo,
  useUpdateDepartmentInfoMutation,
} from '@/hooks/useDepartmentInfo';
import { useNotifications } from '@/hooks/useNotifications';

// Schema validation
const schema = yup
  .object({
    departmentName: yup.string().required('Department name is required'),
    departmentContactName: yup
      .string()
      .required('Department contact name is required'),
    departmentContactEmail: yup
      .string()
      .email('Invalid email')
      .required('Department contact email is required'),
    departmentContactPhone: yup.string().required('Phone number is required'),
    phoneCountryCode: yup.string().default('US'),
  })
  .required();

type DepartmentInfoFormData = yup.InferType<typeof schema>;

const DepartmentInfoSettings: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { notifyWithImage, notifications, dismiss } = useNotifications();

  // Get suite info from location state
  const agentSuiteKey = location.state?.agentSuiteKey;

  // Fetch suite data from API
  const { data: agentSuites = [], isLoading: isLoadingSuites } =
    useGetAIAgentSuites();
  const suite = agentSuites.find(s => s.agentSuiteKey === agentSuiteKey);

  // Get fallback image from mock data
  const suiteFallbackImage = mockAgentsSuite.find(
    mockAgent =>
      mockAgent.id.toLowerCase() === suite?.agentSuiteKey.toLowerCase()
  )?.image;

  // Use API data to get suite details
  const finalSuite = suite;

  // React Hook Form
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<DepartmentInfoFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      departmentName: '',
      departmentContactName: '',
      departmentContactEmail: '',
      departmentContactPhone: '',
      phoneCountryCode: 'US',
    },
  });

  // Watch phone fields for PhoneInput component
  const departmentContactPhone = watch('departmentContactPhone');
  const phoneCountryCode = watch('phoneCountryCode');

  // Queries and Mutations
  const { data: departmentInfoResponse, isLoading: isLoadingDepartmentInfo } =
    useGetDepartmentInfo(agentSuiteKey || '', {
      enabled: !!agentSuiteKey,
    });

  const createMutation = useCreateDepartmentInfoMutation();
  const updateMutation = useUpdateDepartmentInfoMutation();

  const isSubmitting = createMutation.isPending || updateMutation.isPending;

  // Watch all form values
  const currentFormValues = watch();

  // Check if form has changes by comparing with API response data
  const hasFormChanges = React.useMemo(() => {
    if (!departmentInfoResponse?.data) {
      // If no existing data, check if any field has a value
      return (
        !!currentFormValues.departmentName ||
        !!currentFormValues.departmentContactName ||
        !!currentFormValues.departmentContactEmail ||
        !!currentFormValues.departmentContactPhone
      );
    }

    const data = departmentInfoResponse.data;

    // Get dial code from phone number for comparison
    let currentPhoneNumber = '';
    if (
      currentFormValues.departmentContactPhone &&
      currentFormValues.phoneCountryCode
    ) {
      const allCountries = CountryList.getAll();
      const selectedCountry = allCountries.find(
        c => c.code === currentFormValues.phoneCountryCode
      );
      const dialCode = selectedCountry?.dial_code || '+1';
      currentPhoneNumber = `${dialCode}${currentFormValues.departmentContactPhone}`;
    }

    // Compare all form fields with API response
    return (
      currentFormValues.departmentName !== (data.name || '') ||
      currentFormValues.departmentContactName !== (data.contactName || '') ||
      currentFormValues.departmentContactEmail !== (data.contactEmail || '') ||
      currentPhoneNumber !== (data.contactPhone || '')
    );
  }, [currentFormValues, departmentInfoResponse?.data]);

  // Populate form when data is fetched
  useEffect(() => {
    if (departmentInfoResponse?.data) {
      const data = departmentInfoResponse.data;
      setValue('departmentName', data.name || '');
      setValue('departmentContactName', data.contactName || '');
      setValue('departmentContactEmail', data.contactEmail || '');

      // Parse phone number to extract country code if possible
      if (data.contactPhone) {
        const allCountries = CountryList.getAll();
        // Try to find a matching dial code
        const matchingCountry = allCountries.find(country =>
          data.contactPhone.startsWith(country.dial_code)
        );
        if (matchingCountry) {
          setValue('phoneCountryCode', matchingCountry.code);
          setValue(
            'departmentContactPhone',
            data.contactPhone.replace(matchingCountry.dial_code, '').trim()
          );
        } else {
          setValue('departmentContactPhone', data.contactPhone);
        }
      }
    }
  }, [departmentInfoResponse, setValue]);

  const onSubmit = async (data: DepartmentInfoFormData) => {
    if (!agentSuiteKey) return;

    // Get dial code from country code
    const allCountries = CountryList.getAll();
    const selectedCountry = allCountries.find(
      c => c.code === data.phoneCountryCode
    );
    const dialCode = selectedCountry?.dial_code || '+1';
    const fullPhoneNumber = `${dialCode}${data.departmentContactPhone}`;

    const payload = {
      agentSuiteKey,
      name: data.departmentName,
      contactName: data.departmentContactName,
      contactEmail: data.departmentContactEmail,
      contactPhone: fullPhoneNumber,
    };

    try {
      if (departmentInfoResponse?.data?.id) {
        // Update existing
        await updateMutation.mutateAsync(payload);
      } else {
        // Create new
        await createMutation.mutateAsync(payload);
      }

      notifyWithImage(
        'Department information updated successfully!',
        '',
        '',
        'success'
      );

      // Navigate back to the suite page
      navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
    } catch (error: any) {
      notifyWithImage(
        error?.response?.data?.message ||
          'Failed to update department information. Please try again.',
        '',
        ''
      );
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
  };

  // Show loading state while fetching suite data
  if (isLoadingSuites) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 h-8 w-8" />
          <p className="text-lg text-gray-600">Loading suite information...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!finalSuite) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agent suite could not be found.
          </p>
          <button
            onClick={() => navigate(ROUTES.DASHBOARD_AI_AGENTS)}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to AI Agents
          </button>
        </div>
      </div>
    );
  }

  return (
    <DashboardWithChatLayout>
      <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
        {/* Header */}
        <div className="flex items-center gap-4">
          {/* Breadcrumb */}
          <button
            onClick={handleCancel}
            className="flex items-center gap-1 font-semibold text-blackTwo"
          >
            <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" strokeWidth={2} />
            Agent Suite
          </button>
          <span className="text-blackTwo">›</span>
          <span className="text-gray-600">Department Information</span>
        </div>

        {/* Notifications Container */}
        <NotificationContainer
          notifications={notifications}
          onClose={dismiss}
          className="w-full"
          maxNotifications={3}
        />

        {/* Suite Header */}
        <div className="relative h-[198px] overflow-hidden rounded-xl bg-cover bg-center font-spartan">
          {/* Background Image */}
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: finalSuite.avatar
                ? `url(${finalSuite.avatar})`
                : `url(${suiteFallbackImage})`,
            }}
          />
          {/* Dark Overlay */}
          <div className="absolute inset-0 bg-black/20" />
          {/* Content */}
          <div className="relative z-10 flex h-full flex-col justify-center p-6">
            <div className="flex w-fit items-center justify-center rounded-lg bg-white px-4 py-3">
              <h1 className="mt-1 text-[32px] font-bold leading-none">
                {finalSuite.agentSuiteName}
              </h1>
            </div>
            <h2 className="mt-8 w-fit text-[20px] font-semibold text-white">
              {finalSuite.description}
            </h2>
            <div className="font-inter text-lg text-white">
              {finalSuite.roleDescription}
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="w-full max-w-[528px]">
          <h2 className="mb-6 text-lg font-medium text-blackOne">
            Update Your Department Information
          </h2>

          {isLoadingDepartmentInfo ? (
            <div className="flex justify-center py-8">
              <Spinner className="h-8 w-8" />
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Department Name & Contact Name */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label
                    htmlFor="departmentName"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Department Name
                  </label>
                  <Input
                    type="text"
                    id="departmentName"
                    placeholder="Enter department name"
                    {...register('departmentName')}
                    className={`w-full ${errors.departmentName ? 'border-red-500' : ''}`}
                  />
                  {errors.departmentName && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.departmentName.message}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="departmentContactName"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Department Contact Name
                  </label>
                  <Input
                    type="text"
                    id="departmentContactName"
                    placeholder="Enter department contact name"
                    {...register('departmentContactName')}
                    className={`w-full ${errors.departmentContactName ? 'border-red-500' : ''}`}
                  />
                  {errors.departmentContactName && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.departmentContactName.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Contact Email & Phone */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label
                    htmlFor="departmentContactEmail"
                    className="mb-2 block text-xs font-normal text-grayTen"
                  >
                    Department Contact Email
                  </label>
                  <Input
                    type="email"
                    id="departmentContactEmail"
                    placeholder="Enter department contact email"
                    {...register('departmentContactEmail')}
                    className={`w-full ${errors.departmentContactEmail ? 'border-red-500' : ''}`}
                  />
                  {errors.departmentContactEmail && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.departmentContactEmail.message}
                    </p>
                  )}
                </div>
                <PhoneInput
                  label="Department Contact Phone Number"
                  placeholder="Enter phone number"
                  value={departmentContactPhone}
                  countryCode={phoneCountryCode}
                  onChange={(value, code) => {
                    setValue('departmentContactPhone', value);
                    setValue('phoneCountryCode', code);
                  }}
                  error={errors.departmentContactPhone?.message}
                />
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-center gap-4">
                <Button
                  type="button"
                  onClick={handleCancel}
                  className="h-11 w-full"
                  variant="outline"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || !hasFormChanges}
                  className="h-11 w-full text-white"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center gap-2">
                      <Loader2 className="h-4 w-4 text-white" />
                      <span className="text-sm">Saving...</span>
                    </div>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};

export default DepartmentInfoSettings;

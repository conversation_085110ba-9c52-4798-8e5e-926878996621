import { AxiosError } from 'axios';
import clsx from 'clsx';
import { ChevronDown, Loader2, Search } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import Pagination from '@/components/common/Pagination';
import { Button, Input } from '@/components/ui';
import { usePagination } from '@/hooks/usePagination';
import {
  useApproveJoinRequestMutation,
  useRejectJoinRequestMutation,
  useSuiteJoinRequests,
  useUpdateJoinRequestRoleMutation,
} from '@/hooks/useRequestToJoin';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import type { JoinRequest } from '@/services/requestToJoinService';

import ActionDropdown from '../../../../components/common/ActionDropdown';
import DeclineReasonModal from '../../../../components/common/DeclineReasonModal';
import DataTable, { Column } from '../../../../components/ui/tables/DataTable';
import {
  mapApiRoleToUiRole,
  MemberRole,
  SuiteMemberRoleApi,
} from '../../../../types/members';
import {
  generateMemberInitials,
  getRoleColor,
  getRoleDisplayName,
} from '../../../../utils/members';
import UpdateRoleModal from './UpdateRole';

const REQUEST_STATUS_COLORS = {
  PENDING: 'bg-amber-100 text-amber-700 hover:bg-amber-200 border-amber-300',
  APPROVED:
    'bg-emerald-100 text-emerald-700 hover:bg-emerald-200 border-emerald-300',
  REJECTED: 'bg-red-100 text-red-700 hover:bg-red-200 border-red-300',
};

interface RequestsTabProps {
  agentSuiteKey: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onShowAlert: (message: string, type: 'error' | 'success' | 'warning') => void;
}

export const RequestsTab: React.FC<RequestsTabProps> = ({
  agentSuiteKey,
  searchQuery,
  onSearchChange,
  onShowAlert,
}) => {
  const { page, setPage } = usePagination(1, 'requestsPage');
  const [pageSize] = useState<number>(10);
  const [statusFilter] = useState<string | undefined>('PENDING');

  const [isUpdateRoleModalOpen, setIsUpdateRoleModalOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<{
    id: string;
    name: string;
    role: MemberRole;
  } | null>(null);
  const [isDeclineModalOpen, setIsDeclineModalOpen] = useState(false);
  const [requestToDecline, setRequestToDecline] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [approveActionState, setApproveActionState] = useState<{
    status: 'idle' | 'pending' | 'success';
    requestId?: string;
  }>({ status: 'idle' });
  const [declineActionState, setDeclineActionState] = useState<{
    status: 'idle' | 'pending' | 'success';
    requestId?: string;
  }>({ status: 'idle' });
  const approveResetTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  );
  const declineResetTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  );

  const { data: userData } = useGetUserProfile();

  const suiteMembership = userData?.userInfo?.tenant?.claimedAgentSuites?.find(
    suite => suite?.suite?.agentSuiteKey === agentSuiteKey
  );

  const suiteMemberRecord = suiteMembership?.members?.find(
    member => member?.user?.userId === userData?.userInfo?.userId
  );

  const isUserMember = Boolean(suiteMemberRecord);
  const canViewRequests =
    isUserMember &&
    suiteMemberRecord?.memberRoles?.some(role => role === 'MANAGER');

  // Fetch join requests for this suite
  const { data: joinRequestsData, isLoading: isLoadingRequests } =
    useSuiteJoinRequests(
      agentSuiteKey,
      { page: page - 1, pageSize, status: statusFilter },
      !!agentSuiteKey && canViewRequests
    );

  const approveRequestMutation = useApproveJoinRequestMutation();
  const rejectRequestMutation = useRejectJoinRequestMutation();
  const updateRequestRoleMutation = useUpdateJoinRequestRoleMutation();

  const requests = joinRequestsData?.requests || [];
  const totalRequests = joinRequestsData?.total || 0;

  const handleChangeRole = async (requestId: string, newRole: MemberRole) => {
    try {
      await updateRequestRoleMutation.mutateAsync({
        requestId,
        payload: { requestedRole: newRole },
        agentSuiteKey,
      });
      onShowAlert('Role updated successfully', 'success');
      setIsUpdateRoleModalOpen(false);
      setSelectedRequest(null);
    } catch (error) {
      const axiosError = error as AxiosError<{ message?: string }>;
      const errorMessage =
        axiosError.response?.data?.message || 'Failed to update role';
      onShowAlert(errorMessage, 'error');
      setIsUpdateRoleModalOpen(false);
      setSelectedRequest(null);
    }
  };

  const scheduleApproveReset = () => {
    if (approveResetTimeoutRef.current) {
      clearTimeout(approveResetTimeoutRef.current as unknown as number);
    }
    approveResetTimeoutRef.current = setTimeout(() => {
      approveRequestMutation.reset?.();
      setApproveActionState({ status: 'idle' });
    }, 3000);
  };

  const scheduleDeclineReset = () => {
    if (declineResetTimeoutRef.current) {
      clearTimeout(declineResetTimeoutRef.current as unknown as number);
    }
    declineResetTimeoutRef.current = setTimeout(() => {
      rejectRequestMutation.reset?.();
      setDeclineActionState({ status: 'idle' });
    }, 3000);
  };

  const handleAcceptRequest = async (requestId: string) => {
    try {
      setApproveActionState({ status: 'pending', requestId });
      await approveRequestMutation.mutateAsync({
        requestId,
        agentSuiteKey,
      });
      setApproveActionState({ status: 'success', requestId });
      scheduleApproveReset();
      onShowAlert('Request accepted successfully', 'success');
    } catch (error) {
      const axiosError = error as AxiosError<{ message?: string }>;
      setApproveActionState({ status: 'idle' });
      const errorMessage =
        axiosError.response?.data?.message || 'Failed to accept request';
      onShowAlert(errorMessage, 'error');
    }
  };

  const handleDeclineRequestClick = (
    requestId: string,
    requesterName: string
  ) => {
    setRequestToDecline({ id: requestId, name: requesterName });
    setIsDeclineModalOpen(true);
  };

  const handleDeclineRequest = async (rejectionReason: string) => {
    if (!requestToDecline) return;

    try {
      setDeclineActionState({
        status: 'pending',
        requestId: requestToDecline.id,
      });
      await rejectRequestMutation.mutateAsync({
        requestId: requestToDecline.id,
        payload: { rejectionReason },
        agentSuiteKey,
      });
      setDeclineActionState({
        status: 'success',
        requestId: requestToDecline.id,
      });
      scheduleDeclineReset();
      setIsDeclineModalOpen(false);
      setRequestToDecline(null);
      onShowAlert('Request declined successfully', 'success');
    } catch (error) {
      const axiosError = error as AxiosError<{ message?: string }>;
      setDeclineActionState({ status: 'idle' });
      const errorMessage =
        axiosError.response?.data?.message || 'Failed to decline request';
      onShowAlert(errorMessage, 'error');
      setIsDeclineModalOpen(false);
      setRequestToDecline(null);
    }
  };

  useEffect(() => {
    return () => {
      if (approveResetTimeoutRef.current) {
        clearTimeout(approveResetTimeoutRef.current);
      }
      if (declineResetTimeoutRef.current) {
        clearTimeout(declineResetTimeoutRef.current);
      }
    };
  }, []);

  const getApproveActionLabel = (requestId: string) => {
    if (
      approveActionState.status === 'pending' &&
      approveActionState.requestId === requestId
    ) {
      return 'Approving...';
    }
    if (
      approveActionState.status === 'success' &&
      approveActionState.requestId === requestId
    ) {
      return 'Request Approved';
    }
    return 'Accept Request';
  };

  const getDeclineActionLabel = (requestId: string) => {
    if (
      declineActionState.status === 'pending' &&
      declineActionState.requestId === requestId
    ) {
      return 'Declining...';
    }
    if (
      declineActionState.status === 'success' &&
      declineActionState.requestId === requestId
    ) {
      return 'Request Declined';
    }
    return 'Decline Request';
  };

  const isApprovePending = (requestId: string) =>
    approveActionState.status === 'pending' &&
    approveActionState.requestId === requestId;

  const isDeclinePending = (requestId: string) =>
    declineActionState.status === 'pending' &&
    declineActionState.requestId === requestId;

  const approveActionVariant = (requestId: string) =>
    approveActionState.status === 'success' &&
    approveActionState.requestId === requestId
      ? ('success' as const)
      : ('default' as const);

  const declineActionVariant = (requestId: string) =>
    declineActionState.status === 'success' &&
    declineActionState.requestId === requestId
      ? ('success' as const)
      : ('danger' as const);

  const requestColumns: Column<JoinRequest>[] = [
    {
      key: 'requesterName',
      label: 'Name',
      render: (_, request) => (
        <div className="flex items-center">
          <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
            {request?.requesterName
              ? generateMemberInitials(request.requesterName as string)
              : 'U'}
          </div>
          <div>
            <div className="font-medium text-blackOne">
              {request?.requesterName ? request.requesterName : 'User'}
            </div>
            <div className="text-sm text-subText">
              {request?.requesterEmail}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'requestedRole',
      label: 'Role',
      render: (_, request) => (
        <span
          className={`flex h-[34px] w-28 items-center justify-center rounded-lg px-4 text-xs font-medium ${
            getRoleColor(request.requestedRole) || 'bg-gray-30 text-white'
          }`}
        >
          {getRoleDisplayName(request.requestedRole) || 'N/A'}
        </span>
      ),
    },
    {
      key: 'id',
      label: 'Requests',
      render: (_, request) => {
        const actions = [
          // {
          //   label: 'Change Role',
          //   icon: null,
          //   onClick: () => {
          //     setSelectedRequest({
          //       id: request.id,
          //       name: request.requesterName,
          //       role: mapApiRoleToUiRole(
          //         (request.status as SuiteMemberRoleApi) || 'MEMBER'
          //       ),
          //     });
          //     setIsUpdateRoleModalOpen(true);
          //   },
          //   variant: 'default' as const,
          // },
          {
            label: getApproveActionLabel(request.id),
            icon: isApprovePending(request.id) ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : null,
            onClick: () => handleAcceptRequest(request.id),
            isDisabled: isApprovePending(request.id),
            isLoading: isApprovePending(request.id),
            variant: approveActionVariant(request.id),
            closeOnClick: false,
          },
          {
            label: getDeclineActionLabel(request.id),
            icon: isDeclinePending(request.id) ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : null,
            onClick: () =>
              handleDeclineRequestClick(request.id, request.requesterName),
            isDisabled: isDeclinePending(request.id),
            isLoading: isDeclinePending(request.id),
            variant: declineActionVariant(request.id),
            closeOnClick: false,
          },
        ];

        const canManageMembers = !!canViewRequests;

        return canManageMembers ? (
          <ActionDropdown
            actions={actions}
            customTriggerIcon={
              <Button
                variant="outline"
                className="flex h-10 w-32 items-center gap-2 rounded-lg border border-grayTen"
              >
                Actions{' '}
                <ChevronDown className="h-5 w-5 text-grayTen" strokeWidth={3} />
              </Button>
            }
          />
        ) : null;
      },
    },
  ];

  if (!isUserMember) {
    return (
      <div className="rounded-lg border border-grayThirteen bg-white px-4 py-6 text-sm text-grayTen">
        You need to be a member of this suite to see join requests.
      </div>
    );
  }

  if (!canViewRequests) {
    return (
      <div className="rounded-lg border border-grayThirteen bg-white px-4 py-6 text-sm text-grayTen">
        Only suite managers can view the Requests tab.
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Search */}
        <div className="flex items-center gap-3">
          <Input
            type="text"
            placeholder="Search by name or email"
            className="h-10 w-[300px] rounded-[10px] border border-grayThirteen py-2 pl-4 pr-10 text-sm placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 h-4 w-4" />}
            value={searchQuery}
            onChange={e => onSearchChange(e.target.value)}
          />
        </div>

        {/* Desktop Table */}
        <div className="hidden flex-col gap-4 overflow-x-auto sm:flex">
          <DataTable<JoinRequest & Record<string, unknown>>
            data={(requests || []) as (JoinRequest & Record<string, unknown>)[]}
            columns={
              requestColumns as unknown as Column<
                JoinRequest & Record<string, unknown>
              >[]
            }
            loading={isLoadingRequests}
            emptyMessage="No requests found"
            rowColoring={true}
            rowColoringType="odd"
            getRowId={(request: JoinRequest & Record<string, unknown>) =>
              request.id
            }
          />
          {!isLoadingRequests && requests && requests?.length > 0 && (
            <Pagination
              currentPage={page}
              totalPages={Math.ceil(totalRequests / pageSize)}
              onPageChange={page => setPage(page)}
            />
          )}
        </div>

        {/* Mobile Card View */}
        <div className="-mx-4 flex flex-col sm:hidden">
          {isLoadingRequests ? (
            Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className={clsx('px-6 py-4', index % 2 === 0 && 'bg-[#FFF1EB]')}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-gray-200" />
                    <div className="space-y-2">
                      <div className="h-4 w-32 rounded bg-gray-200" />
                      <div className="h-3 w-40 rounded bg-gray-200" />
                    </div>
                  </div>
                  <div className="h-6 w-6 rounded bg-gray-200" />
                </div>
                <div className="mt-4 space-y-2">
                  <div className="h-3 w-20 rounded bg-gray-200" />
                  <div className="h-3 w-24 rounded bg-gray-200" />
                </div>
              </div>
            ))
          ) : requests && requests.length > 0 ? (
            <>
              {requests.map((request, index) => {
                const actions = [
                  {
                    label: 'Change Role',
                    icon: null,
                    onClick: () => {
                      setSelectedRequest({
                        id: request.id,
                        name: request.requesterName,
                        role: mapApiRoleToUiRole(
                          (request.status as SuiteMemberRoleApi) || 'MEMBER'
                        ),
                      });
                      setIsUpdateRoleModalOpen(true);
                    },
                    variant: 'default' as const,
                  },
                  {
                    label: getApproveActionLabel(request.id),
                    icon: isApprovePending(request.id) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : null,
                    onClick: () => handleAcceptRequest(request.id),
                    isDisabled: isApprovePending(request.id),
                    isLoading: isApprovePending(request.id),
                    variant: approveActionVariant(request.id),
                    closeOnClick: false,
                  },
                  {
                    label: getDeclineActionLabel(request.id),
                    icon: isDeclinePending(request.id) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : null,
                    onClick: () => handleDeclineRequest(request.id),
                    isDisabled: isDeclinePending(request.id),
                    isLoading: isDeclinePending(request.id),
                    variant: declineActionVariant(request.id),
                    closeOnClick: false,
                  },
                ];

                return (
                  <div
                    key={request.id}
                    className={clsx('p-4', index % 2 === 0 && 'bg-[#FFF1EB]')}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
                          {request?.requesterName
                            ? generateMemberInitials(
                                request.requesterName as string
                              )
                            : 'U'}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="truncate font-medium text-blackOne">
                            {request?.requesterName || 'User'}
                          </div>
                          <div className="truncate text-sm text-subText">
                            {request?.requesterEmail}
                          </div>
                        </div>
                      </div>
                      {canViewRequests && <ActionDropdown actions={actions} />}
                    </div>

                    <div className="mt-4 flex flex-col gap-3">
                      <div className="flex items-center justify-between gap-2">
                        <span className="text-xs text-subText">Status</span>
                        <span
                          className={clsx(
                            'inline-flex items-center justify-center rounded-lg px-3 py-1.5 text-xs font-medium',
                            REQUEST_STATUS_COLORS[request.status] ||
                              'bg-gray-100 text-gray-800'
                          )}
                        >
                          {request.status}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}

              <div className="px-6 py-4">
                <Pagination
                  currentPage={page}
                  totalPages={Math.ceil(totalRequests / pageSize)}
                  onPageChange={page => setPage(page)}
                />
              </div>
            </>
          ) : (
            <div className="px-6 py-8 text-center text-sm text-subText">
              No requests found
            </div>
          )}
        </div>
      </div>

      {/* Update Role Modal */}
      <UpdateRoleModal
        isOpen={isUpdateRoleModalOpen}
        onClose={() => {
          setIsUpdateRoleModalOpen(false);
          setSelectedRequest(null);
        }}
        onUpdateRole={(newRole: MemberRole) => {
          handleChangeRole(selectedRequest?.id || '', newRole);
        }}
        userDetails={selectedRequest}
        loading={updateRequestRoleMutation.isPending}
      />

      {/* Decline Reason Modal */}
      <DeclineReasonModal
        isOpen={isDeclineModalOpen}
        onClose={() => {
          setIsDeclineModalOpen(false);
          setRequestToDecline(null);
        }}
        onConfirm={handleDeclineRequest}
        requesterName={requestToDecline?.name}
      />
    </>
  );
};

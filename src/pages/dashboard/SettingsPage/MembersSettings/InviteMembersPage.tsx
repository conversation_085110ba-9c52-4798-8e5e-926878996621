import { yupResolver } from '@hookform/resolvers/yup';
import { ChevronRight, Shield } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import * as yup from 'yup';

import { DashboardWithChatLayout } from '@/components/layout/DashboardWithChatLayout';
import { Input, NotificationContainer } from '@/components/ui';
import AgentsDropdown, { DropdownOption } from '@/components/ui/AgentsDropdown';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useInviteSuiteMemberMutation } from '@/hooks/useMembers';
import { useNotifications } from '@/hooks/useNotifications';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import {
  mapUiRoleToApiRole,
  MemberRole,
  ROLE_DESCRIPTIONS,
} from '../../../../types/members';

interface InviteMemberFormData {
  firstName: string;
  lastName: string;
  email: string;
  role: MemberRole;
  agentSuiteKey: string;
}

const inviteMemberSchema = yup.object().shape({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup
    .string()
    .email('Enter a valid email')
    .required('Email is required'),
  role: yup
    .string()
    .required('Role is required') as yup.StringSchema<MemberRole>,
  agentSuiteKey: yup.string().required('Agent Suite key is required'),
});

const InviteMembersPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setActiveAgent } = useTenant();
  const [selectedRole, setSelectedRole] = useState<MemberRole>('MEMBER');
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] =
    useState<boolean>(false);
  const [currentSuite, setCurrentSuite] = useState<DropdownOption | undefined>(
    undefined
  );

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid },
    setValue,
  } = useForm<InviteMemberFormData>({
    resolver: yupResolver(inviteMemberSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      role: 'MEMBER',
      agentSuiteKey: '',
    },
    mode: 'onChange',
  });
  const { data: userData, isLoading: isLoadingUser } = useGetUserProfile();

  const inviteMutation = useInviteSuiteMemberMutation();

  const { notify, notifications, dismiss } = useNotifications();

  const handleSuiteChange = (
    suite: DropdownOption,
    onChange?: (value: string) => void
  ) => {
    setCurrentSuite(suite);
    setIsSuiteDropdownOpen(false);
    // Call the form field onChange to update the form value
    if (onChange) {
      onChange(suite.id);
    }
  };

  const onSubmit = async (data: InviteMemberFormData) => {
    // Validate all required fields
    if (
      !data.firstName?.trim() ||
      !data.lastName?.trim() ||
      !data.email?.trim()
    ) {
      // showAlert('Please fill in all required fields.', 'error');
      notify('Please fill in all required fields.', 'error');
      return;
    }

    try {
      await inviteMutation.mutateAsync({
        agentSuiteKey: data.agentSuiteKey,
        firstname: data.firstName.trim(),
        lastname: data.lastName.trim(),
        email: data.email.trim(),
        role: mapUiRoleToApiRole(selectedRole),
      });
      // showAlert(`Invitation sent successfully to ${data.email}`, 'success');
      // Clear form after successful submission
      notify(`Invitation sent successfully to ${data.email}`, 'success');
      reset();
      setSelectedRole('MEMBER');
      // Reset suite selection to first available suite where user can invite
      if (suitesWhereUserCanInvite.length > 0) {
        const firstSuite = suitesWhereUserCanInvite[0];
        const defaultSuite = {
          id: firstSuite.suite.agentSuiteKey,
          name: firstSuite.suite.agentSuiteName,
          icon: firstSuite.suite.avatar,
        };
        setCurrentSuite(defaultSuite);
        setValue('agentSuiteKey', defaultSuite.id);
      }
      // Navigate back with preserved search params
      // setTimeout(() => {
      //   const returnUrl = returnParams
      //     ? `${ROUTES.DASHBOARD_SETTINGS_MEMBERS}?${returnParams}`
      //     : ROUTES.DASHBOARD_SETTINGS_MEMBERS;
      //   navigate(returnUrl);
      // }, 1500);
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        'Failed to send invitation. Please try again.';
      // showAlert(errorMessage, 'error');
      notify(errorMessage, 'error');
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  // Filter suites to only show those where user is a member with MANAGER or LEAD role
  const suitesWhereUserCanInvite = useMemo(() => {
    return (
      userData?.userInfo?.tenant?.claimedAgentSuites?.filter(claimedSuite => {
        // Check if user is a member of this suite
        const userMembership = claimedSuite.members.find(
          member => member?.user?.userId === userData?.userInfo?.userId
        );

        if (!userMembership) return false;

        // Check if user has MANAGER or LEAD role in this suite
        const userRole = userMembership.memberRoles?.[0];
        return userRole === 'MANAGER' || userRole === 'LEAD';
      }) || []
    );
  }, [
    userData?.userInfo?.tenant?.claimedAgentSuites,
    userData?.userInfo?.userId,
  ]);

  // Check if user can manage members in at least one suite
  const canManageMembers = suitesWhereUserCanInvite.length > 0;
  // Redirect if user doesn't have permission
  useEffect(() => {
    if (!isLoadingUser && userData && !canManageMembers) {
      // Show alert and redirect to members page
      navigate(ROUTES.DASHBOARD_SETTINGS_MEMBERS, {
        replace: true,
        state: {
          error:
            'You do not have permission to invite members. Only Managers and Leads can invite new members.',
        },
      });
    }
  }, [isLoadingUser, userData, canManageMembers, navigate]);

  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  // Set first suite as default when userData loads (only suites where user can invite)
  useEffect(() => {
    if (suitesWhereUserCanInvite.length > 0 && !currentSuite) {
      // Check if navigation state has a specific suite to pre-select
      const navigationState = location.state as {
        agentSuiteKey?: string;
      } | null;
      const agentSuiteKey = navigationState?.agentSuiteKey;

      let defaultSuite;
      if (agentSuiteKey) {
        // Try to find the suite from navigation state
        const suiteFromNav = suitesWhereUserCanInvite.find(
          s => s.suite.agentSuiteKey === agentSuiteKey
        );
        if (suiteFromNav) {
          defaultSuite = {
            id: suiteFromNav.suite.agentSuiteKey,
            name: suiteFromNav.suite.agentSuiteName,
            icon: suiteFromNav.suite.avatar,
          };
        }
      }

      // If no suite from navigation or not found, use first available
      if (!defaultSuite) {
        const firstSuite = suitesWhereUserCanInvite[0];
        defaultSuite = {
          id: firstSuite.suite.agentSuiteKey,
          name: firstSuite.suite.agentSuiteName,
          icon: firstSuite.suite.avatar,
        };
      }

      setCurrentSuite(defaultSuite);
      setValue('agentSuiteKey', defaultSuite.id);
    }
  }, [suitesWhereUserCanInvite, currentSuite, setValue, location.state]);

  // Show loading state while checking permissions
  if (isLoadingUser) {
    return (
      <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary/20 border-t-primary"></div>
            <p className="text-subText">Loading...</p>
          </div>
        </div>
      </DashboardWithChatLayout>
    );
  }

  // Show unauthorized access message if user doesn't have permission
  if (!isLoadingUser && userData && !canManageMembers) {
    return (
      <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
        <div className="flex h-64 flex-col items-center justify-center text-center">
          <Shield className="mb-4 h-16 w-16 text-gray-400" />
          <h2 className="mb-2 text-xl font-semibold text-blackOne">
            Access Restricted
          </h2>
          <p className="mb-4 max-w-md text-subText">
            You do not have permission to invite members. Only Managers and
            Leads can invite new members.
          </p>
          <button
            onClick={() => navigate(ROUTES.DASHBOARD_SETTINGS_MEMBERS)}
            className="rounded-md bg-primary px-4 py-2 text-white transition-colors hover:bg-primary/90"
          >
            Back to Members
          </button>
        </div>
      </DashboardWithChatLayout>
    );
  }

  // Only show suites where user has MANAGER or LEAD role
  const agentSuites = suitesWhereUserCanInvite.map(suite => ({
    id: suite.suite.agentSuiteKey,
    name: suite.suite.agentSuiteName,
    icon: suite.suite.avatar,
  }));

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <div className="px-4 pb-8 sm:px-8 sm:py-6">
        <div className="relative overflow-x-hidden">
          {/* Alert */}
          {/* {alertMessage && (
            <Alert
              message={alertMessage}
              type={alertType}
              onClose={() => setAlertMessage('')}
              agent={{
                name: 'Regis',
                avatar: regis,
              }}
              onBack={() => {
                navigate(-1);
              }}
              clearMode="manual"
              showIcon={true}
              triggerKey={alertTrigger}
            />
          )} */}

          {/* Notifications Container */}
          <div className="mb-4 max-w-[732px]">
            <NotificationContainer
              notifications={notifications}
              onClose={dismiss}
              className="w-full"
              maxNotifications={3}
            />
          </div>

          <div className="flex max-w-[483px] flex-col gap-6 sm:gap-12">
            {/* Header */}
            <div className="flex flex-col gap-4">
              <h1 className="text-lg font-bold text-subText">Members</h1>
              <div className="flex items-center space-x-1">
                <Link
                  to={
                    location.pathname.includes(ROUTES.DASHBOARD_SETTINGS)
                      ? ROUTES.DASHBOARD_SETTINGS_MEMBERS
                      : ROUTES.DASHBOARD_MEMBERS
                  }
                  className="cursor-pointer text-sm font-medium text-[#FF6636] underline"
                >
                  Members
                </Link>
                <ChevronRight
                  className="h-4 w-4 text-subText"
                  strokeWidth={3}
                />
                <h3 className="text-sm font-semibold text-subText">
                  Invite Members
                </h3>
              </div>
              {/* Description */}
              <p className="text-sm text-subText">
                Invite members to collaborate in your organization's Agent
                Suites. (Access limited to users with your email domain)
              </p>
            </div>

            {/* Form */}
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="space-y-4 sm:space-y-6"
            >
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="First name"
                    placeholder="First name"
                    error={errors.firstName?.message}
                    fullWidth
                  />
                )}
              />

              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Last name"
                    placeholder="Last name"
                    error={errors.lastName?.message}
                    fullWidth
                  />
                )}
              />

              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Email address"
                    placeholder="Email address"
                    error={errors.email?.message}
                    fullWidth
                  />
                )}
              />

              {/* Agent Suite Selection */}
              <Controller
                name="agentSuiteKey"
                control={control}
                render={({ field }) => (
                  <div className="relative space-y-2">
                    <label className="block text-sm font-medium text-blackOne">
                      Agent Suite
                    </label>
                    <AgentsDropdown
                      isOpen={isSuiteDropdownOpen}
                      onToggle={() =>
                        setIsSuiteDropdownOpen(!isSuiteDropdownOpen)
                      }
                      currentItem={currentSuite}
                      options={
                        agentSuites?.map(suite => ({
                          id: suite.id,
                          name: suite.name,
                          icon: suite.icon,
                        })) || []
                      }
                      onItemSelect={suite =>
                        handleSuiteChange(suite, field.onChange)
                      }
                      placeholder="Choose an Agent Suite..."
                      className="!w-full border border-[#D0D0D0] focus:border-[#D0D0D0]"
                      noOptionsMessage="No other suites available"
                    />
                    {errors.agentSuiteKey && (
                      <p className="text-sm text-red-600">
                        {errors.agentSuiteKey.message}
                      </p>
                    )}
                  </div>
                )}
              />

              {/* Role Selection */}
              <Controller
                name="role"
                control={control}
                render={({ field }) => (
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-blackOne">
                      Assign Role
                    </label>
                    <div className="space-y-3">
                      <label className="flex cursor-pointer items-center space-x-3">
                        <input
                          type="radio"
                          name="role"
                          value="MANAGER"
                          checked={selectedRole === 'MANAGER'}
                          onChange={e => {
                            const role = e.target.value as MemberRole;
                            setSelectedRole(role);
                            field.onChange(role);
                          }}
                          className="mt-1 h-4 w-4 border-[#D0D0D0] bg-white text-primary focus:ring-primary"
                        />
                        <div className="text-sm text-subText sm:text-base">
                          <span className="font-semibold">
                            {ROLE_DESCRIPTIONS.MANAGER.title}
                          </span>
                          {ROLE_DESCRIPTIONS.MANAGER.description}
                        </div>
                      </label>

                      <label className="flex cursor-pointer items-center space-x-3">
                        <input
                          type="radio"
                          name="role"
                          value="LEAD"
                          checked={selectedRole === 'LEAD'}
                          onChange={e => {
                            const role = e.target.value as MemberRole;
                            setSelectedRole(role);
                            field.onChange(role);
                          }}
                          className="h-4 w-4 border-[#D0D0D0] bg-white text-primary focus:ring-primary"
                        />
                        <div className="text-sm text-subText sm:text-base">
                          <span className="font-semibold">
                            {ROLE_DESCRIPTIONS.LEAD.title}
                          </span>
                          {ROLE_DESCRIPTIONS.LEAD.description}
                        </div>
                      </label>

                      <label className="flex cursor-pointer items-center space-x-3">
                        <input
                          type="radio"
                          name="role"
                          value="MEMBER"
                          checked={selectedRole === 'MEMBER'}
                          onChange={e => {
                            const role = e.target.value as MemberRole;
                            setSelectedRole(role);
                            field.onChange(role);
                          }}
                          className="h-4 w-4 border-[#D0D0D0] bg-white text-primary focus:ring-primary"
                        />
                        <div className="text-sm text-subText sm:text-base">
                          <span className="font-semibold">
                            {ROLE_DESCRIPTIONS.MEMBER.title}
                          </span>
                          {ROLE_DESCRIPTIONS.MEMBER.description}
                        </div>
                      </label>
                    </div>
                    {errors.role && (
                      <p className="text-sm text-red-600">
                        {errors.role.message}
                      </p>
                    )}
                  </div>
                )}
              />

              {/* Actions */}
              <div className="mt-4 flex w-full items-center justify-start gap-4">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="flex w-full items-center justify-center rounded-lg bg-[#F5F5F5] px-4 py-2.5 text-sm font-medium text-subText transition-colors hover:bg-[#E5E5E5] sm:w-fit"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex w-full items-center justify-center gap-2 rounded-lg bg-primary px-4 py-2.5 text-sm font-medium text-white transition-colors hover:bg-primary/90 disabled:opacity-50 sm:w-fit"
                  disabled={inviteMutation.isPending || !isValid}
                >
                  {inviteMutation.isPending ? (
                    <>
                      <div className="border-white/20 h-4 w-4 animate-spin rounded-full border-2 border-t-white"></div>
                      Adding...
                    </>
                  ) : (
                    'Add member'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};

export default InviteMembersPage;

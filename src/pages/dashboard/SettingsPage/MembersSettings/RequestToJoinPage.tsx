import { yup<PERSON>esolver } from '@hookform/resolvers/yup';
import { AxiosError } from 'axios';
import { ChevronRight } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import * as yup from 'yup';

import { Icons } from '@/assets/icons/DashboardIcons';
import { DashboardWithChatLayout } from '@/components/layout/DashboardWithChatLayout';
import { Button, useNotifications } from '@/components/ui';
import AgentsDropdown, { DropdownOption } from '@/components/ui/AgentsDropdown';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useCreateJoinRequestMutation } from '@/hooks/useRequestToJoin';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { MemberRole, ROLE_DESCRIPTIONS } from '@/types/members';

interface RequestToJoinFormData {
  agentSuiteKey: string;
  message: string;
  requestedRole: MemberRole;
}

const requestToJoinSchema = yup.object().shape({
  agentSuiteKey: yup.string().required('Please select a suite.'),
  message: yup
    .string()
    .required('Please enter a message explaining why you want to join.')
    .min(10, 'Message must be at least 10 characters long.')
    .trim(),
  requestedRole: yup
    .string()
    .oneOf(['MANAGER', 'LEAD', 'MEMBER'], 'Invalid role selected')
    .required('Please select a role.') as yup.StringSchema<MemberRole>,
});

interface RequestToJoinPageProps {}

const RequestToJoinPage: React.FC<RequestToJoinPageProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setActiveAgent } = useTenant();
  const { notify } = useNotifications();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Get data from navigation state
  const { returnRoute, agentSuiteKey: suiteKeyFromState } =
    location.state || {};

  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);

  const { data: userData } = useGetUserProfile();
  const createJoinRequestMutation = useCreateJoinRequestMutation();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<RequestToJoinFormData>({
    resolver: yupResolver(requestToJoinSchema),
    defaultValues: {
      agentSuiteKey: suiteKeyFromState || '',
      message: '',
      requestedRole: 'MANAGER',
    },
    mode: 'onChange',
  });

  const watchedRole = watch('requestedRole');
  const watchedSuiteKey = watch('agentSuiteKey');

  // Set default suite when userData loads
  useEffect(() => {
    if (userData?.userInfo?.tenant?.claimedAgentSuites && !watchedSuiteKey) {
      const suites = userData.userInfo.tenant.claimedAgentSuites;
      let defaultSuiteKey: string | undefined = undefined;

      // First priority: try to find suite from navigation state
      if (suiteKeyFromState) {
        const suiteFromState = suites.find(
          suite => suite.suite.agentSuiteKey === suiteKeyFromState
        );
        if (suiteFromState) {
          defaultSuiteKey = suiteFromState.suite.agentSuiteKey;
        }
      }

      // Second priority: if only one suite exists, use that as default
      if (!defaultSuiteKey && suites.length === 1) {
        defaultSuiteKey = suites[0].suite.agentSuiteKey;
      }

      // Third priority: use first available suite
      if (!defaultSuiteKey && suites.length > 0) {
        defaultSuiteKey = suites[0].suite.agentSuiteKey;
      }

      // Set the default suite key in the form
      if (defaultSuiteKey) {
        setValue('agentSuiteKey', defaultSuiteKey);
      }
    }
  }, [userData, watchedSuiteKey, setValue, suiteKeyFromState]);

  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  const agentSuites = userData?.userInfo?.tenant?.claimedAgentSuites?.map(
    suite => ({
      id: suite.suite.agentSuiteKey,
      name: suite.suite.agentSuiteName,
      icon: suite.suite.avatar,
    })
  );

  // Derive currentSuite from form value
  const currentSuite = agentSuites?.find(suite => suite.id === watchedSuiteKey);

  const handleSuiteChange = (
    suite: DropdownOption,
    onChange?: (value: string) => void
  ) => {
    setIsSuiteDropdownOpen(false);
    // Update form value
    if (onChange) {
      onChange(suite.id);
    }
  };

  const getApiErrorMessage = (error: unknown) => {
    if (error instanceof AxiosError) {
      const apiResponse = error.response?.data as {
        message?: string;
        data?: { details?: string };
      };

      if (apiResponse?.message) {
        return apiResponse.data?.details
          ? `${apiResponse.message}`
          : apiResponse.message;
      }
    }

    if (error instanceof Error && error.message) {
      return error.message;
    }

    return 'Failed to send request. Please try again.';
  };

  const onSubmit = async (data: RequestToJoinFormData) => {
    try {
      await createJoinRequestMutation.mutateAsync({
        agentSuiteKey: data.agentSuiteKey,
        message: data.message,
        requestedRole: data.requestedRole,
      });

      notify(
        'Request sent successfully! You will be notified once approved.',
        'success'
      );

      // Navigate back after a short delay
      setTimeout(() => {
        navigate(returnRoute || ROUTES.DASHBOARD_BASE);
      }, 2000);
    } catch (error) {
      notify(getApiErrorMessage(error), 'error');
    }
  };
  console.log(location.state);
  const handleCancel = () => {
    navigate(returnRoute || ROUTES.DASHBOARD_BASE);
  };

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <div className="px-4 py-6 sm:px-8">
        <div className="relative overflow-x-hidden">
          <div className="ml-1 flex w-full max-w-[483px] flex-col gap-6">
            {/* Header */}
            <div className="flex flex-col gap-4">
              <div className="flex items-center space-x-1">
                <Link
                  to={
                    location.pathname.includes(ROUTES.DASHBOARD_SETTINGS)
                      ? ROUTES.DASHBOARD_SETTINGS_MEMBERS
                      : ROUTES.DASHBOARD_MEMBERS
                  }
                  className="text-sm font-medium text-primary"
                >
                  Members
                </Link>
                <ChevronRight
                  className="h-4 w-4 text-subText"
                  strokeWidth={3}
                />
                <h3 className="text-sm font-semibold text-subText">
                  Request To Join
                </h3>
              </div>
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-1">
                  <Icons.Users className="text-primary" />
                  <h3 className="cursor-pointer text-sm font-semibold text-blackOne">
                    Members
                  </h3>
                </div>
                <Controller
                  name="agentSuiteKey"
                  control={control}
                  render={({ field }) => (
                    <AgentsDropdown
                      isOpen={isSuiteDropdownOpen}
                      onToggle={() =>
                        setIsSuiteDropdownOpen(!isSuiteDropdownOpen)
                      }
                      currentItem={currentSuite}
                      options={
                        agentSuites?.map(suite => ({
                          id: suite.id,
                          name: suite.name,
                          icon: suite.icon,
                        })) || []
                      }
                      onItemSelect={suite =>
                        handleSuiteChange(suite, field.onChange)
                      }
                      placeholder="Choose an Agent Suite..."
                      noOptionsMessage="No other suites available"
                    />
                  )}
                />
              </div>
              {/* Description */}
              <p className="text-sm text-subText">
                Request access to collaborate in an organization's Agent Suites.
                (Approval required from the organization's admin.)
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Message Input */}
              <Controller
                name="message"
                control={control}
                render={({ field }) => (
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-blackOne">
                      Message *
                    </label>
                    <textarea
                      {...field}
                      placeholder="Explain why you want to join this suite..."
                      className={`h-32 w-full rounded-lg border p-3 text-sm text-blackOne outline-none placeholder:text-grayTen focus:border-0 focus:outline-none focus:ring-2 focus:ring-primary ${
                        errors.message
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-grayThirteen'
                      }`}
                    />
                    {errors.message && (
                      <p className="text-sm text-red-600">
                        {errors.message.message}
                      </p>
                    )}
                  </div>
                )}
              />

              {/* Role Selection */}
              <Controller
                name="requestedRole"
                control={control}
                render={({ field }) => (
                  <div className="space-y-4">
                    <label className="block text-sm font-medium text-blackOne">
                      Requested Role
                    </label>
                    <div className="space-y-3">
                      <label className="flex cursor-pointer items-center space-x-3">
                        <input
                          type="radio"
                          name="role"
                          value="MANAGER"
                          checked={watchedRole === 'MANAGER'}
                          onChange={() => field.onChange('MANAGER')}
                          className="mt-1 h-4 w-4 border-[#D0D0D0] bg-white text-primary focus:ring-primary"
                        />
                        <div className="text-sm text-subText sm:text-base">
                          <span className="font-semibold">
                            {ROLE_DESCRIPTIONS.MANAGER.title}
                          </span>
                          {ROLE_DESCRIPTIONS.MANAGER.description}
                        </div>
                      </label>

                      <label className="flex cursor-pointer items-center space-x-3">
                        <input
                          type="radio"
                          name="role"
                          value="LEAD"
                          checked={watchedRole === 'LEAD'}
                          onChange={() => field.onChange('LEAD')}
                          className="h-4 w-4 border-[#D0D0D0] bg-white text-primary focus:ring-primary"
                        />
                        <div className="text-sm text-subText sm:text-base">
                          <span className="font-semibold">
                            {ROLE_DESCRIPTIONS.LEAD.title}
                          </span>
                          {ROLE_DESCRIPTIONS.LEAD.description}
                        </div>
                      </label>

                      <label className="flex cursor-pointer items-center space-x-3">
                        <input
                          type="radio"
                          name="role"
                          value="MEMBER"
                          checked={watchedRole === 'MEMBER'}
                          onChange={() => field.onChange('MEMBER')}
                          className="h-4 w-4 border-[#D0D0D0] bg-white text-primary focus:ring-primary"
                        />
                        <div className="text-sm text-subText sm:text-base">
                          <span className="font-semibold">
                            {ROLE_DESCRIPTIONS.MEMBER.title}
                          </span>
                          {ROLE_DESCRIPTIONS.MEMBER.description}
                        </div>
                      </label>
                    </div>
                    {errors.requestedRole && (
                      <p className="text-sm text-red-600">
                        {errors.requestedRole.message}
                      </p>
                    )}
                  </div>
                )}
              />

              {/* Actions */}
              <div className="flex w-full items-center justify-center space-x-4">
                <Button
                  type="button"
                  onClick={handleCancel}
                  className="h-[42px] w-full"
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="h-[42px] w-full"
                  disabled={isSubmitting || createJoinRequestMutation.isPending}
                >
                  {isSubmitting || createJoinRequestMutation.isPending ? (
                    <>
                      <div className="border-white/20 h-4 w-4 animate-spin rounded-full border-2 border-t-white"></div>
                      Sending Request...
                    </>
                  ) : (
                    <>Send Request</>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};

export default RequestToJoinPage;

import { <PERSON>ader2, MoveHorizontal } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Icons } from '../../../../assets/icons/DashboardIcons';
import AnimatedModal from '../../../../components/common/AnimatedModal';
import UserAvatar from '../../../../components/ui/UserAvatar';
import { MemberRole, ROLE_DESCRIPTIONS } from '../../../../types/members';

interface UpdateRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateRole: (newRole: MemberRole) => void;
  userDetails: {
    id: string;
    name: string;
    role: MemberRole;
  } | null;
  loading?: boolean;
}

const UpdateRoleModal: React.FC<UpdateRoleModalProps> = ({
  isOpen,
  onClose,
  onUpdateRole,
  userDetails,
  loading = false,
}) => {
  const [selectedRole, setSelectedRole] = useState<MemberRole>('MEMBER');
  const [initialRole, setInitialRole] = useState<MemberRole | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    if (userDetails && isOpen) {
      // Reset state when opening modal with a different user or when first opening
      const shouldReset = !currentUserId || currentUserId !== userDetails.id;

      if (shouldReset) {
        // Use the role directly (should already be uppercase MemberRole)
        const role = userDetails.role || 'MEMBER';
        setSelectedRole(role);
        setInitialRole(role);
        setCurrentUserId(userDetails.id);
      }
    }
  }, [userDetails, isOpen, currentUserId]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userDetails) return;

    // Compare with the initial role to prevent no-op updates
    if (selectedRole === initialRole) {
      onClose();
      return;
    }

    onUpdateRole(selectedRole);
  };

  const handleClose = () => {
    if (userDetails) {
      const role = userDetails.role || 'MEMBER';
      setSelectedRole(role);
    }
    setInitialRole(null); // Reset initial role for next time modal opens
    setCurrentUserId(null); // Reset current user ID for next time modal opens
    onClose();
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!userDetails}
      onClose={handleClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <form onSubmit={handleSubmit} className="flex flex-col">
        <div className="flex flex-col space-y-6 p-8">
          {/* Header with Avatar and Arrow */}
          <div className="flex items-center justify-center space-x-6">
            {/* User Avatar */}
            <UserAvatar
              fullName={userDetails?.name || 'User'}
              // profileImage={userDetails?. || undefined}
              size="lg"
              isLabel={false}
              border={true}
              useRandomColor={false}
              defaultColor="#121212"
              borderColor="#FF3E00"
              textColor="#fff"
              borderWidth={2}
            />

            {/* Arrow */}
            <MoveHorizontal className="h-6 w-6 text-subText" />

            {/* Role Icon */}
            <div className="flex h-[52px] w-[52px] flex-shrink-0 items-center justify-center rounded-lg bg-yellowOne">
              <Icons.UpdateMe className="h-9 w-9" />
            </div>
          </div>

          {/* Title */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900">
              Update {userDetails?.name}'s Role
            </h3>
          </div>

          {/* Role Selection */}
          <div className="space-y-4">
            {(['MANAGER', 'LEAD', 'MEMBER'] as MemberRole[]).map(role => (
              <label
                key={role}
                className="flex cursor-pointer items-center space-x-3"
              >
                <input
                  type="radio"
                  name="role"
                  value={role}
                  checked={selectedRole === role}
                  onChange={e => setSelectedRole(e.target.value as MemberRole)}
                  className="h-4 w-4 text-primary focus:ring-primary"
                  disabled={loading}
                />
                <div className="text-sm text-subText">
                  <span className="font-semibold">
                    {ROLE_DESCRIPTIONS[role].title}
                  </span>
                  {ROLE_DESCRIPTIONS[role].description}
                </div>
              </label>
            ))}
          </div>
        </div>
        {/* Actions */}
        <div className="flex w-full items-center justify-center space-x-4 border-t p-6">
          <button
            type="button"
            onClick={handleClose}
            className="flex-1 rounded-lg border border-gray-300 bg-white px-6 py-2.5 text-base text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="flex flex-1 items-center justify-center gap-2 rounded-lg bg-primary px-6 py-2.5 text-base text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50"
            disabled={loading || selectedRole === initialRole}
          >
            {loading ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin" />
                Updating...
              </>
            ) : (
              'Update'
            )}
          </button>
        </div>
      </form>
    </AnimatedModal>
  );
};

export default UpdateRoleModal;

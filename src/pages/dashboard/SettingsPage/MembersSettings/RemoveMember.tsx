import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui';

import { Icons } from '../../../../assets/icons/DashboardIcons';
import AnimatedModal from '../../../../components/common/AnimatedModal';
import { MemberRole } from '../../../../types/members';

interface RemoveMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRemoveMember: (memberId: string) => void;
  member: {
    id: string;
    name: string;
    role: MemberRole;
  } | null;
  loading?: boolean;
}

const RemoveMemberModal: React.FC<RemoveMemberModalProps> = ({
  isOpen,
  onClose,
  onRemoveMember,
  member,
  loading = false,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!member) return;
    onRemoveMember(member.id);
  };

  return (
    <AnimatedModal
      isOpen={isOpen && !!member}
      onClose={onClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <div className="relative">
        {/* Header */}

        {/* Header Close Button - Top Right */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 flex h-6 w-6 items-center justify-center text-gray-400 hover:text-gray-600"
        >
          <X className="h-5 w-5" />
        </button>

        {/* Content */}
        <div className="mx-auto flex w-full max-w-[385px] flex-col items-start justify-center gap-4 py-8">
          {/* Header */}
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg border border-primary bg-[#FFECE3]  ">
              <Icons.Users className="h-5 w-5 text-primary" />
            </div>
            <h2 className="text-lg font-semibold text-blackOne">
              Manage Members
            </h2>
          </div>
          {/* Subtitle */}
          <h3 className=" text-base font-medium text-blackOne">
            Delete Selected Members' Role
          </h3>

          {/* Warning Box */}
          <div className="flex w-[385px] items-center gap-3 rounded-lg border border-[#FBA320] bg-[#FFFAF7] p-4">
            <TriangleAlert
              className="h-6 w-6 shrink-0 text-white"
              fill="#FBA320"
            />
            <p className="text-sm text-blackOne sm:text-base">
              Deleting selected members will remove their access permanently.
              This action cannot be undone.
            </p>
          </div>
        </div>
        {/* Actions */}
        <form
          onSubmit={handleSubmit}
          className="flex justify-center gap-3 border-t p-6"
        >
          <Button
            type="button"
            onClick={onClose}
            variant="outline"
            className="h-10 w-[180.5px] border border-gray-300 bg-white px-6 py-2 text-blackOne hover:bg-gray-50"
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="h-10 w-[180.5px] bg-delete px-6 py-2 text-white hover:bg-red-600"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </form>
      </div>
    </AnimatedModal>
  );
};

export default RemoveMemberModal;

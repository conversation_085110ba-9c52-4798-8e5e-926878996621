import { yupResolver } from '@hookform/resolvers/yup';
import CountryList from 'country-list-with-dial-code-and-flag';
import { ChevronLeft, Loader2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import Select from 'react-select';
import * as yup from 'yup';

import { Spinner } from '@/components/common/Loader';
import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import {
  Button,
  Input,
  NotificationContainer,
  PaginatedSelect,
  PhoneInput,
} from '@/components/ui';
import { ROUTES } from '@/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import {
  useCreateCompanyInfoMutation,
  useGetCompanyInfo,
  useUpdateCompanyInfoMutation,
} from '@/hooks/useCompanyInfo';
import { useFetchCountriesOptions } from '@/hooks/useLocations';
import { useNotifications } from '@/hooks/useNotifications';
import { usePrivateRequest } from '@/lib/axios/usePrivateRequest';
import locationsService from '@/services/locationsService';
import { CountryWithStates, LocationOption } from '@/types/locations';
import { BASE_URL } from '@/utils/apiUrls';
import { getDefaultSelectStyles } from '@/utils/reactSelectStyles';

// Schema validation
const schema = yup
  .object({
    companyName: yup.string().required('Company name is required'),
    companyEmail: yup
      .string()
      .email('Invalid email')
      .required('Company email is required'),
    streetAddress1: yup.string().required('Street address is required'),
    streetAddress2: yup.string().default(''),
    country: yup.string().required('Country is required'),
    state: yup.string().required('State is required'),
    city: yup.string().required('City is required'),
    zipCode: yup.string().required('Zip code is required'),
    phoneNumber: yup.string().required('Phone number is required'),
    phoneCountryCode: yup.string().default('US'),
  })
  .required();

type CompanyInfoFormData = yup.InferType<typeof schema>;

const CompanyInfoSettings: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { notifyWithImage, notifications, dismiss } = useNotifications();

  // Get suite info from location state
  const agentSuiteKey = location.state?.agentSuiteKey;

  // Fetch suite data from API
  const { data: agentSuites = [], isLoading: isLoadingSuites } =
    useGetAIAgentSuites();
  const suite = agentSuites.find(s => s.agentSuiteKey === agentSuiteKey);

  // Get fallback image from mock data
  const suiteFallbackImage = mockAgentsSuite.find(
    mockAgent =>
      mockAgent.id.toLowerCase() === suite?.agentSuiteKey.toLowerCase()
  )?.image;

  // Use API data to get suite details
  const finalSuite = suite;

  // React Hook Form
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm<CompanyInfoFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      companyName: '',
      companyEmail: '',
      streetAddress1: '',
      streetAddress2: '',
      country: '',
      state: '',
      city: '',
      zipCode: '',
      phoneNumber: '',
      phoneCountryCode: 'US',
    },
  });

  // State for storing country and state labels
  const [countryOption, setCountryOption] = useState<LocationOption | null>(
    null
  );
  // Removed stateOption - using watch('state') directly for Select value

  // Watch phone fields for PhoneInput component
  const phoneNumber = watch('phoneNumber');
  const phoneCountryCode = watch('phoneCountryCode');

  // Hook for fetching countries
  const fetchCountriesOptions = useFetchCountriesOptions();
  const axiosInstance = usePrivateRequest(BASE_URL);

  // State to store the selected country data (to access its states)
  const [selectedCountryData, setSelectedCountryData] =
    useState<CountryWithStates | null>(null);

  // Convert states array to react-select options format
  const stateOptions = React.useMemo(() => {
    if (!selectedCountryData?.states) return [];
    return selectedCountryData.states.map(stateName => ({
      value: stateName,
      label: stateName,
    }));
  }, [selectedCountryData]);

  // Queries and Mutations
  const { data: companyInfoResponse } = useGetCompanyInfo(agentSuiteKey || '', {
    enabled: !!agentSuiteKey,
  });

  const createMutation = useCreateCompanyInfoMutation();
  const updateMutation = useUpdateCompanyInfoMutation();

  const isSubmitting = createMutation.isLoading || updateMutation.isLoading;

  // Watch all form values
  const currentFormValues = watch();

  // Check if form has changes by comparing with API response data
  const hasFormChanges = React.useMemo(() => {
    if (!companyInfoResponse?.data) {
      // If no existing data, check if any field has a value
      return (
        !!currentFormValues.companyName ||
        !!currentFormValues.companyEmail ||
        !!currentFormValues.streetAddress1 ||
        !!currentFormValues.streetAddress2 ||
        !!currentFormValues.country ||
        !!currentFormValues.state ||
        !!currentFormValues.city ||
        !!currentFormValues.zipCode ||
        !!currentFormValues.phoneNumber
      );
    }

    const data = companyInfoResponse.data;

    // Get dial code from phone number for comparison
    let currentPhoneNumber = '';
    if (currentFormValues.phoneNumber && currentFormValues.phoneCountryCode) {
      const allCountries = CountryList.getAll();
      const selectedCountry = allCountries.find(
        c => c.code === currentFormValues.phoneCountryCode
      );
      const dialCode = selectedCountry?.dial_code || '+1';
      currentPhoneNumber = `${dialCode}${currentFormValues.phoneNumber}`;
    }

    // Compare all form fields with API response
    // Note: country is stored as name in form, but API might return code or name
    return (
      currentFormValues.companyName !== (data.name || '') ||
      currentFormValues.companyEmail !== (data.email || '') ||
      currentFormValues.streetAddress1 !== (data.address1 || '') ||
      currentFormValues.streetAddress2 !== (data.address2 || '') ||
      currentFormValues.country !== (data.country || '') ||
      currentFormValues.state !== (data.stateOrProvince || '') ||
      currentFormValues.city !== (data.city || '') ||
      currentFormValues.zipCode !== (data.zipOrPostalCode || '') ||
      currentPhoneNumber !== (data.phone || '')
    );
  }, [currentFormValues, companyInfoResponse?.data]);

  // Populate form when data is fetched
  useEffect(() => {
    if (companyInfoResponse?.data) {
      const data = companyInfoResponse.data;
      setValue('companyName', data.name || '');
      setValue('companyEmail', data.email || '');
      setValue('streetAddress1', data.address1 || '');
      setValue('streetAddress2', data.address2 || '');
      // Set country and state values
      // Backend returns country name, we need to find the country by name to get its code and states
      if (data.country) {
        // Store country name in form
        setValue('country', data.country);

        // Fetch country data to get states - search by name
        const fetchCountryData = async () => {
          if (axiosInstance.current) {
            try {
              const response = await locationsService.getCountriesWithStates(
                axiosInstance.current,
                {
                  search: data.country, // Search by name
                  page: 0,
                  pageSize: 100,
                }
              );
              const countries = response.data?.countries || [];
              const country = countries.find(
                (c: CountryWithStates) => c.name === data.country
              );

              if (country) {
                setCountryOption({
                  value: country.iso2, // Use code for the option value
                  label: country.name,
                  countryCode: country.iso2,
                });
                setSelectedCountryData(country);
              } else {
                // Fallback if country not found
                setCountryOption({
                  value: data.country,
                  label: data.country,
                  countryCode: data.country,
                });
              }
            } catch (error) {
              // If fetch fails, still set the country name
              setCountryOption({
                value: data.country,
                label: data.country,
                countryCode: data.country,
              });
            }
          }
        };

        fetchCountryData();
      }
      if (data.stateOrProvince) {
        setValue('state', data.stateOrProvince);
        // Set state option for display - state is stored as name, not code
        // State value is set via setValue above
      }
      setValue('city', data.city || '');
      setValue('zipCode', data.zipOrPostalCode || '');

      // Parse phone number to extract country code if possible
      if (data.phone) {
        const allCountries = CountryList.getAll();
        // Try to find a matching dial code
        const matchingCountry = allCountries.find(country =>
          data.phone.startsWith(country.dial_code)
        );
        if (matchingCountry) {
          setValue('phoneCountryCode', matchingCountry.code);
          setValue(
            'phoneNumber',
            data.phone.replace(matchingCountry.dial_code, '').trim()
          );
        } else {
          setValue('phoneNumber', data.phone);
        }
      }
    }
  }, [companyInfoResponse, setValue, axiosInstance]);

  const onSubmit = async (data: CompanyInfoFormData) => {
    if (!agentSuiteKey) return;

    // Get dial code from country code
    const allCountries = CountryList.getAll();
    const selectedCountry = allCountries.find(
      c => c.code === data.phoneCountryCode
    );
    const dialCode = selectedCountry?.dial_code || '+1';
    const fullPhoneNumber = `${dialCode}${data.phoneNumber}`;

    // Get country name from selectedCountryData if available, otherwise use form value
    const countryName = selectedCountryData?.name || data.country;

    const payload = {
      agentSuiteKey,
      name: data.companyName,
      email: data.companyEmail,
      address1: data.streetAddress1,
      address2: data.streetAddress2 || '',
      country: countryName, // Send country name, not code
      stateOrProvince: data.state,
      city: data.city,
      zipOrPostalCode: data.zipCode,
      phone: fullPhoneNumber,
    };

    try {
      if (companyInfoResponse?.data?.id) {
        // Update existing
        await updateMutation.mutateAsync(payload);
      } else {
        // Create new
        await createMutation.mutateAsync(payload);
      }

      notifyWithImage(
        'Company information updated successfully!',
        '',
        '',
        'success'
      );

      // Navigate back to the suite page
      navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
    } catch (error: unknown) {
      const errorMessage =
        (error as { response?: { data?: { message?: string } } })?.response
          ?.data?.message ||
        'Failed to update company information. Please try again.';
      notifyWithImage(errorMessage, '', '');
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.DASHBOARD_AGENT_SUITE(agentSuiteKey || 'default'));
  };

  // Show loading state while fetching suite data
  if (isLoadingSuites) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 h-8 w-8" />
          <p className="text-lg text-gray-600">Loading suite information...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!finalSuite) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agent suite could not be found.
          </p>
          <button
            onClick={() => navigate(ROUTES.DASHBOARD_AI_AGENTS)}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            Back to AI Agents
          </button>
        </div>
      </div>
    );
  }

  return (
    <DashboardWithChatLayout>
      <div className="flex w-full max-w-[850px] flex-col gap-4 p-8">
        {/* Header */}
        <div className="flex items-center gap-4">
          {/* Breadcrumb */}
          <button
            onClick={handleCancel}
            className="flex items-center gap-1 font-semibold text-blackTwo"
          >
            <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" strokeWidth={2} />
            Agent Suite
          </button>
          <span className="text-blackTwo">›</span>
          <span className="text-gray-600">Company Information</span>
        </div>

        {/* Notifications Container */}
        <NotificationContainer
          notifications={notifications}
          onClose={dismiss}
          className="w-full"
          maxNotifications={3}
        />

        {/* Suite Header */}
        <div className="relative h-[198px] overflow-hidden rounded-xl bg-cover bg-center font-spartan">
          {/* Background Image */}
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: finalSuite.avatar
                ? `url(${finalSuite.avatar})`
                : `url(${suiteFallbackImage})`,
            }}
          />
          {/* Dark Overlay */}
          <div className="absolute inset-0 bg-black/20" />
          {/* Content */}
          <div className="relative z-10 flex h-full flex-col justify-center p-6">
            <div className="flex w-fit items-center justify-center rounded-lg bg-white px-4 py-3">
              <h1 className="mt-1 text-[32px] font-bold leading-none">
                {finalSuite.agentSuiteName}
              </h1>
            </div>
            <h2 className="mt-8 w-fit text-[20px] font-semibold text-white">
              {finalSuite.description}
            </h2>
            <div className="font-inter text-lg text-white">
              {finalSuite.roleDescription}
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="w-full max-w-[528px]">
          <h2 className="mb-6 text-lg font-medium text-blackOne">
            Update Your Company Information
          </h2>

          {/* {isLoadingCompanyInfo ? (
            <div className="flex justify-center py-8">
              <Spinner className="h-8 w-8" />
            </div>
          ) : ( */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Company Name & Email */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="flex flex-col gap-2">
                <label
                  htmlFor="companyName"
                  className="text-xs font-normal text-grayTen"
                >
                  Company Name
                </label>
                <Controller
                  name="companyName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="text"
                      id="companyName"
                      {...field}
                      className="w-full"
                      error={
                        errors.companyName
                          ? errors.companyName.message
                          : undefined
                      }
                    />
                  )}
                />
              </div>
              <div className="flex flex-col gap-2">
                <label
                  htmlFor="companyEmail"
                  className="text-xs font-normal text-grayTen"
                >
                  Company Email
                </label>
                <Controller
                  name="companyEmail"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="email"
                      id="companyEmail"
                      {...field}
                      className="w-full"
                      error={
                        errors.companyEmail
                          ? (errors.companyEmail.message as string)
                          : undefined
                      }
                    />
                  )}
                />
              </div>
            </div>

            {/* Company Address */}
            <div>
              <h3 className="mb-4 text-xs font-normal text-grayTen">
                Company Address
              </h3>

              <div className="space-y-4">
                <Input
                  type="text"
                  id="streetAddress1"
                  label="Street Address 1"
                  {...register('streetAddress1')}
                  placeholder="Enter street address 1"
                  className="w-full"
                  error={
                    errors.streetAddress1
                      ? errors.streetAddress1.message
                      : undefined
                  }
                />

                <Input
                  type="text"
                  id="streetAddress2"
                  label="Street Address 2"
                  {...register('streetAddress2')}
                  placeholder="Enter street address 2"
                  className="w-full"
                  error={
                    errors.streetAddress2
                      ? errors.streetAddress2.message
                      : undefined
                  }
                />
                <PaginatedSelect
                  label="Country"
                  name="country"
                  placeholder="Search for a country..."
                  value={countryOption}
                  onChange={async (option: LocationOption | null) => {
                    setCountryOption(option);
                    if (option) {
                      // Fetch the country data to get its states array and name
                      if (axiosInstance.current) {
                        try {
                          const response =
                            await locationsService.getCountriesWithStates(
                              axiosInstance.current,
                              {
                                search: option.value, // Search by code (iso2)
                                page: 0,
                                pageSize: 100,
                              }
                            );
                          const countries = response.data?.countries || [];
                          const country = countries.find(
                            (c: CountryWithStates) => c.iso2 === option.value
                          );

                          if (country) {
                            // Store country NAME in form, not code
                            setValue('country', country.name);
                            setSelectedCountryData(country);
                            // Clear state when country changes
                            setValue('state', '');
                          } else {
                            setSelectedCountryData(null);
                            setValue('country', '');
                            setValue('state', '');
                          }
                        } catch (error) {
                          setSelectedCountryData(null);
                          setValue('country', '');
                          setValue('state', '');
                        }
                      }
                    } else {
                      setValue('country', '');
                      setValue('state', '');
                      setSelectedCountryData(null);
                    }
                  }}
                  fetchOptions={fetchCountriesOptions}
                  error={errors.country ? errors.country.message : undefined}
                />

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <label
                      htmlFor="state"
                      className="mb-2 block text-xs font-normal text-grayTen"
                    >
                      State/Province
                    </label>
                    <Select
                      id="state"
                      name="state"
                      placeholder={
                        selectedCountryData && stateOptions.length > 0
                          ? 'Select a state...'
                          : 'Select a country first'
                      }
                      value={
                        watch('state')
                          ? {
                              value: watch('state'),
                              label: watch('state'),
                            }
                          : null
                      }
                      onChange={option => {
                        if (option) {
                          setValue('state', option.value);
                        } else {
                          setValue('state', '');
                        }
                      }}
                      options={stateOptions}
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      isDisabled={
                        !selectedCountryData || stateOptions.length === 0
                      }
                      isSearchable
                      isClearable
                      styles={getDefaultSelectStyles()}
                      className={`!disabled:bg-white !disabled:opacity-50 h-12 w-full ${errors.state ? 'border-red-500' : ''}`}
                      classNamePrefix="react-select"
                    />
                    {errors.state && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.state.message}
                      </p>
                    )}
                  </div>
                  <Input
                    type="text"
                    id="city"
                    label="City"
                    {...register('city')}
                    placeholder="Enter city"
                    className="w-full"
                    error={errors.city ? errors.city.message : undefined}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Input
                    type="text"
                    id="zipCode"
                    label="Zip/Postal Code"
                    {...register('zipCode')}
                    placeholder="Enter zip/postal code"
                    className="w-full"
                    error={errors.zipCode ? errors.zipCode.message : undefined}
                  />
                  <PhoneInput
                    label="Phone Number"
                    placeholder="Enter phone number"
                    value={phoneNumber}
                    countryCode={phoneCountryCode}
                    onChange={(value, code) => {
                      setValue('phoneNumber', value);
                      setValue('phoneCountryCode', code);
                    }}
                    error={errors.phoneNumber?.message}
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex w-full items-center justify-center gap-4">
              <Button
                type="button"
                className="h-11 w-full"
                variant="outline"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || !hasFormChanges}
                className="h-11 w-full text-white"
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin text-white" />
                )}
                Save Changes
              </Button>
            </div>
          </form>
          {/* )} */}
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};

export default CompanyInfoSettings;

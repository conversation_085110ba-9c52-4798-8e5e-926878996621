import React, { useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import Pagination from '@/components/common/Pagination';
import { TablePageContainer } from '@/components/layout/DashboardWithChatLayout';
import DataTable, { Column } from '@/components/ui/tables/DataTable';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useTimezone } from '@/context/TimezoneContext';
import { useAssignmentLogsList } from '@/hooks/useAssignmentLog';
import { useDebounce } from '@/hooks/useDebounce';
import { usePagination } from '@/hooks/usePagination';
import type { AssignmentLog, AssignmentLogStatus } from '@/types/assignmentLog';
import { useAnalyticsParams } from '@/utils/urlParams';

interface AssignmentLogTableRow {
  id: string;
  assignedDate: string;
  assignedTo: string;
  startDate: string;
  dueDate: string;
  status: string;
  overdue: string;
  completionDate: string;
}

const AssignmentLogsPage: React.FC = () => {
  const { filters } = useAnalyticsParams();
  const { tenantId } = useTenant();
  const { formatUserTimestamp } = useTimezone();
  const navigate = useNavigate();
  const location = useLocation();

  // Get search term from URL params
  const searchParams = new URLSearchParams(location.search);
  const searchTerm = searchParams.get('search') || '';
  // Debounce search term
  const debouncedSearchTerm = useDebounce(searchTerm, 700);
  // Pagination state
  const { page: currentPage, setPage: setCurrentPage } = usePagination();
  const pageSize = 10;

  // Build filter using URL params
  const urlParams = new URLSearchParams(location.search);
  const statusFilter =
    (urlParams.get('status') as AssignmentLogStatus) || undefined;
  const assignedDateFrom = urlParams.get('assignedDateFrom') || undefined;
  const assignedDateTo = urlParams.get('assignedDateTo') || undefined;
  const startDateFrom = urlParams.get('startDateFrom') || undefined;
  const startDateTo = urlParams.get('startDateTo') || undefined;

  // Memoize filter object to prevent infinite re-renders
  const filter = useMemo(
    () => ({
      search: debouncedSearchTerm,
      createdBy: filters.agent || '',
      tenantId: tenantId || '',
      status: statusFilter,
      assignedDateFrom: assignedDateFrom,
      assignedDateTo: assignedDateTo,
      startDateFrom: startDateFrom,
      startDateTo: startDateTo,
      page: currentPage - 1, // Convert to 0-based indexing
      pageSize: pageSize,
    }),
    [
      debouncedSearchTerm,
      filters.agent,
      tenantId,
      statusFilter,
      assignedDateFrom,
      assignedDateTo,
      startDateFrom,
      startDateTo,
      currentPage,
      pageSize,
    ]
  );

  const { data: assignmentLogsResponse, isLoading } = useAssignmentLogsList(
    filter,
    !!tenantId
  );

  const assignmentLogs = assignmentLogsResponse?.data || {
    assignments: [],
    total: 0,
  };
  const totalCount = assignmentLogs.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const convertToTableData = (
    logs: AssignmentLog[]
  ): AssignmentLogTableRow[] => {
    return logs?.map(log => ({
      id: log.id,
      assignedDate: log.assignedDate
        ? formatUserTimestamp(log.assignedDate, 'date')
        : '--',
      assignedTo: log.assignedTo
        ? log.assignedTo
            ?.split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ')
        : log.assignedTo,
      startDate: log.startDate
        ? formatUserTimestamp(log.startDate, 'date')
        : '--',
      dueDate: log.dueDate ? formatUserTimestamp(log.dueDate, 'date') : '--',
      status: log.status,
      overdue: log.dueDate
        ? (() => {
            const msPerDay = 1000 * 60 * 60 * 24;
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const dueDate = new Date(log.dueDate);
            dueDate.setHours(0, 0, 0, 0);
            const diff = Math.ceil(
              (today.getTime() - dueDate.getTime()) / msPerDay
            );
            return diff > 0 ? `${diff} day${diff === 1 ? '' : 's'}` : '--';
          })()
        : '--',
      completionDate: '--',
    }));
  };

  const tableData = convertToTableData(assignmentLogs.assignments || []);

  const columns: Column<AssignmentLogTableRow>[] = [
    { key: 'assignedDate', label: 'Assigned Date', sortable: true },
    { key: 'assignedTo', label: 'Assigned To', sortable: true },
    { key: 'startDate', label: 'Start Date', sortable: true },
    { key: 'dueDate', label: 'Due Date', sortable: true },
    { key: 'status', label: 'Status', sortable: true },
    { key: 'overdue', label: 'Overdue', sortable: true },
    { key: 'completionDate', label: 'Completion Date', sortable: true },
  ];

  const handleRowClick = (row: AssignmentLogTableRow) => {
    navigate(ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOG_DETAILS(row.id));
  };

  return (
    <>
      <TablePageContainer>
        <DataTable
          data={tableData}
          columns={columns}
          onRowClick={handleRowClick}
          loading={isLoading}
          emptyMessage={
            searchTerm
              ? 'No assignment logs found matching your search'
              : 'No assignment logs found'
          }
          rowColoring={true}
          rowColoringType="odd"
          className="w-full"
        />
      </TablePageContainer>
      {/* Pagination */}
      {totalPages > 0 && !isLoading && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </>
  );
};

export default AssignmentLogsPage;

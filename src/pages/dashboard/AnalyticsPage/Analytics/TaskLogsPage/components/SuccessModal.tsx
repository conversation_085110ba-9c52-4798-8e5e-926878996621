import React from 'react';

interface SuccessModalProps {
  isOpen: boolean;
  title?: string;
  message?: string;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  title = 'Success!',
  message = 'Task log has been deleted successfully',
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex animate-fade-in items-center justify-center bg-black/30 p-4 backdrop-blur-sm">
      <div className="mx-4 w-full max-w-md animate-scale-in rounded-2xl bg-white p-12 text-center shadow-2xl">
        <div className="mb-8">
          <div className="bg-green-50 mx-auto mb-6 flex h-24 w-24 animate-pulse items-center justify-center rounded-full">
            <svg
              className="text-green-500 h-12 w-12"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h3 className="mb-4 text-3xl font-bold text-gray-900">{title}</h3>
          <p className="text-lg text-gray-600">{message}</p>
          <p className="mt-3 text-sm text-gray-500">
            Redirecting you back to the list...
          </p>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;

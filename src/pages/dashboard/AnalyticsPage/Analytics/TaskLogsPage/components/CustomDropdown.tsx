import clsx from 'clsx';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

export interface DropdownOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
}

interface CustomDropdownProps {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  buttonClassName?: string;
  renderButtonContent: (selectedOption?: DropdownOption) => React.ReactNode;
  dropdownClassName?: string;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  value,
  onChange,
  buttonClassName,
  renderButtonContent,
  dropdownClassName,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const selectedOption = options.find(opt => opt.value === value);

  const handleSelect = (option: DropdownOption) => {
    onChange(option.value);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={clsx(
          'inline-flex items-center justify-between gap-2 rounded-full px-4 py-2 text-xs font-medium',
          buttonClassName
        )}
      >
        {renderButtonContent(selectedOption)}
        <ChevronDown className="h-4 w-4 text-white" />
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className={clsx(
              'absolute z-10 mt-2 w-full rounded-xl bg-white',
              dropdownClassName
            )}
            style={{ boxShadow: '0px 5px 40px 0px #0000001A' }}
          >
            <ul className="max-h-60 overflow-auto py-1">
              {options.map(option => (
                <li
                  key={option.value}
                  onClick={() => handleSelect(option)}
                  className="flex cursor-pointer items-center gap-2 px-4 py-2 text-sm text-subText hover:bg-gray-100"
                >
                  {option.icon}
                  <span>{option.label}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CustomDropdown;

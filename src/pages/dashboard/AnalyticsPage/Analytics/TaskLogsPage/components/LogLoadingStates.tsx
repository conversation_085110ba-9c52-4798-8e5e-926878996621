import { AlertTriangle } from 'lucide-react';
import React from 'react';

// Loading Skeleton Component
export const LogDetailsSkeleton: React.FC = () => {
  return (
    <div className="flex flex-1 flex-col gap-8 overflow-y-auto px-8 pb-20">
      {/* Header Skeleton */}
      <div className="w-full border-b border-gray-200 py-4">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-6 w-24 animate-pulse rounded bg-gray-200" />
          </div>
          <div className="flex items-center gap-3">
            <div className="h-10 w-20 animate-pulse rounded-full bg-gray-200" />
            <div className="h-10 w-20 animate-pulse rounded-full bg-gray-200" />
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="w-full flex-1">
        <div className="w-full max-w-full">
          {/* Top Section Skeleton */}
          <div className="mb-8 grid w-full grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse rounded-lg bg-gray-200 p-4">
                <div className="mb-2 h-4 w-20 rounded bg-gray-200" />
                <div className="h-6 w-16 rounded bg-gray-300" />
              </div>
            ))}
          </div>

          {/* Task Title Skeleton */}
          <div className="mb-8 w-full">
            <div className="mb-3 h-4 w-20 animate-pulse rounded bg-gray-200" />
            <div className="h-8 w-full animate-pulse rounded-lg bg-[]" />
          </div>

          {/* Description Skeleton */}
          <div className="mb-8 w-full">
            <div className="mb-3 h-4 w-20 animate-pulse rounded bg-gray-200" />
            <div className="animate-pulse rounded-lg bg-[] p-4">
              <div className="space-y-3">
                <div className="h-4 w-full rounded bg-gray-300" />
                <div className="h-4 w-full rounded bg-gray-300" />
                <div className="h-4 w-3/4 rounded bg-gray-300" />
                <div className="h-4 w-full rounded bg-gray-300" />
                <div className="h-4 w-2/3 rounded bg-gray-300" />
              </div>
            </div>
          </div>

          {/* Status and Priority Skeleton */}
          <div className="mb-8 grid w-full grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <div key={i}>
                <div className="mb-3 h-4 w-16 animate-pulse rounded bg-gray-200" />
                <div className="h-10 w-24 animate-pulse rounded-lg bg-gray-200" />
              </div>
            ))}
          </div>

          {/* Additional Fields Skeleton */}
          <div className="w-full space-y-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse rounded-lg bg-gray-200 p-4">
                <div className="h-6 w-32 rounded bg-gray-300" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Error State Component
export const LogDetailsError: React.FC<{
  message: string;
  onRetry?: () => void;
  onBack?: () => void;
}> = ({ message, onRetry, onBack }) => {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="mx-auto w-full p-6 text-center">
        <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
          <AlertTriangle className="h-8 w-8 text-red-600" />
        </div>

        <h1 className="mb-4 text-2xl font-semibold text-gray-900">
          Unable to Load Task Log
        </h1>

        <p className="mb-8 text-gray-600">{message}</p>

        <div className="flex justify-center gap-3">
          {onBack && (
            <button
              onClick={onBack}
              className="rounded-lg border border-gray-300 px-6 py-2 text-gray-700 transition-colors hover:bg-gray-50"
            >
              Go Back
            </button>
          )}

          {onRetry && (
            <button
              onClick={onRetry}
              className="rounded-lg bg-orange-500 px-6 py-2 text-white transition-colors hover:bg-orange-600"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

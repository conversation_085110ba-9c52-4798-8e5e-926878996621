import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { useTimezone } from '@/context/TimezoneContext';
import { cn } from '@/lib/twMerge/cn';
import { DailyInsight } from '@/types/dashboard';

interface InsightAccordionProps {
  insight: DailyInsight;
  isLast?: boolean;
}

export const InsightAccordion: React.FC<InsightAccordionProps> = ({
  insight,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { formatUserTimestamp } = useTimezone();

  const formattedDate = formatUserTimestamp(insight.createdAt, 'full');

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'rounded-xl bg-white transition-all'
        // isExpanded ? 'border-primary' : 'border-gray-200'
      )}
    >
      {/* Header - Always Visible */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={cn(
          'flex w-full justify-start gap-2.5 px-2 py-4 text-left transition-colors md:gap-4 md:p-4',
          isExpanded ? 'items-center' : 'items-start'
        )}
      >
        {/* Circular Arrow Icon */}
        <div className="flex h-5 w-5 shrink-0 items-center justify-center rounded-full bg-[#F3F6FF] md:h-9 md:w-9">
          <Icons.ChipExtractionRound className="h-5 w-5 text-blackOne" />
        </div>
        <div className="flex w-full flex-col items-start gap-2">
          {/* Date and Time */}
          <span className="text-sm font-medium text-[#44475B]">
            {formattedDate}
          </span>
          {/* Collapsed Preview - Show when not expanded */}
          {!isExpanded && (
            <p className="line-clamp-2 text-sm text-[#4A4E69]">
              {insight.observation}
            </p>
          )}
        </div>

        {/* Expand/Collapse Icon */}
        <motion.div
          animate={{ rotate: isExpanded ? 180 : 0 }}
          transition={{ duration: 0.4 }}
        >
          <ChevronDown className="h-5 w-5 text-gray-600" />
        </motion.div>
      </button>

      {/* Expanded Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden px-4 pb-4"
          >
            <div className="flex flex-row items-start gap-4">
              <div className="size-9 shrink-0" />
              <div className="space-y-4">
                {/* Observation */}
                <div className="flex flex-col items-start gap-2 rounded-xl bg-[#FFF0EB] p-4">
                  <h4 className="text-sm font-semibold text-red-600">
                    Observation
                  </h4>
                  <p className="text-sm leading-relaxed text-[#4A4E69]">
                    {insight.observation}
                  </p>
                </div>

                {/* Impact */}
                <div className="flex flex-col items-start gap-2 rounded-xl bg-[#FFF0EB] p-4">
                  <h4 className="text-sm font-semibold text-red-600">Impact</h4>
                  <p className="text-sm leading-relaxed text-[#4A4E69]">
                    {insight.impact}
                  </p>
                </div>

                {/* Recommendation */}
                <div className="flex flex-col items-start gap-2 rounded-xl bg-[#FFF0EB] p-4">
                  <h4 className="text-sm font-semibold text-red-600">
                    Recommendation
                  </h4>
                  <p className="text-sm leading-relaxed text-[#4A4E69]">
                    {insight.recommendation}
                  </p>
                </div>

                {/* Action Taken - Optional */}
                {insight.actionTaken && (
                  <div className="flex flex-col items-start gap-2 rounded-xl bg-[#FFF0EB] p-4">
                    <h4 className="text-sm font-semibold text-red-600">
                      Action Taken
                    </h4>
                    <p className="text-sm leading-relaxed text-[#4A4E69]">
                      {insight.actionTaken}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { insightsEmptyStateBg } from '@/assets/images';
import { DailyInsight } from '@/types/dashboard';

import { InsightAccordion } from './InsightAccordion';
import { InsightSkeleton } from './InsightSkeleton';

interface DailyInsightCommentaryProps {
  insights?: DailyInsight[];
  isLoading?: boolean;
  isError?: boolean;
}

const DailyInsightCommentary: React.FC<DailyInsightCommentaryProps> = ({
  insights,
  isLoading,
  isError,
}) => {
  if (isLoading) {
    return <InsightSkeleton count={3} />;
  }

  if (isError) {
    return (
      <div className="py-8 text-center">
        <p className="text-red-500">
          Failed to load insights. Please try again.
        </p>
      </div>
    );
  }

  if (!insights || insights?.length === 0) {
    return (
      <div
        className="relative flex h-[171px] w-full items-start justify-start overflow-hidden rounded-lg p-5 sm:h-[295px] sm:items-center sm:justify-center sm:p-16"
        style={{
          backgroundImage: `url(${insightsEmptyStateBg})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        {/* Dark overlay */}
        <div
          className="absolute inset-0"
          style={{
            backgroundColor: '#000000B2',
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex flex-col items-start justify-center gap-6 sm:items-center">
          {/* Icon with warning background */}
          <div className="flex h-9 w-9 items-center justify-center rounded-lg bg-warning sm:h-16 sm:w-16 sm:rounded-2xl">
            <Icons.ChipExtractionRound className="h-6 w-6 text-white sm:h-8 sm:w-8" />
          </div>

          {/* Text content */}
          <div className="flex flex-col items-start gap-3 sm:items-center">
            <h3 className="text-center text-base font-semibold leading-[100%] text-white">
              No insights available today.
            </h3>
            <p className="text-left text-sm font-normal leading-[24px] text-white sm:text-center sm:text-base">
              As you activate your first agent tasks, this
              <br />
              space will come alive with real-time insights.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {insights?.map((insight, index) => (
        <InsightAccordion
          key={insight.id}
          insight={insight}
          isLast={index === insights.length - 1}
        />
      ))}
    </div>
  );
};

export default DailyInsightCommentary;

import React from 'react';

interface InsightSkeletonProps {
  count?: number;
}

export const InsightSkeleton: React.FC<InsightSkeletonProps> = ({
  count = 3,
}) => {
  return (
    <div className="flex flex-col gap-4">
      {[...Array(count)].map((_, index) => (
        <div
          key={index}
          className="animate-pulse rounded-xl border border-gray-200 bg-white p-4"
        >
          <div className="flex items-start justify-between gap-4">
            <div className="flex items-start gap-3">
              {/* Circular Icon Skeleton */}
              <div className="h-9 w-9 shrink-0 rounded-full bg-gray-200" />

              <div className="flex flex-col gap-2">
                {/* Date/Time Skeleton */}
                <div className="h-4 w-32 rounded bg-gray-200" />

                {/* Description Skeleton - 2 lines */}
                <div className="space-y-2">
                  <div className="h-3 w-[300px] rounded bg-gray-200" />
                  <div className="h-3 w-[250px] rounded bg-gray-200" />
                </div>
              </div>
            </div>

            {/* Chevron Icon Skeleton */}
            <div className="h-5 w-5 shrink-0 rounded bg-gray-200" />
          </div>
        </div>
      ))}
    </div>
  );
};

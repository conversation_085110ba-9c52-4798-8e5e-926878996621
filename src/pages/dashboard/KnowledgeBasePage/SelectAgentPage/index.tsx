import React from 'react';
import { useNavigate } from 'react-router-dom';

import { knowledgeBaseBg } from '@/assets/images';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import AgentSelectionLayout from '../../../../components/layout/AgentSelectionLayout';
import { ROUTES } from '../../../../constants/routes';

const KnowledgeBaseSelectAgentPage: React.FC = () => {
  const navigate = useNavigate();
  const { data: userData } = useGetUserProfile();

  // Check if user has claimed the specific agents suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return userData?.userInfo?.tenant?.claimedAgentSuites?.some(
      claimedSuite => claimedSuite.suite.agentSuiteKey === suiteKey
    );
  };

  // Note: Removed automatic redirect to allow users to stay on select-agent page
  // Users with claimed suites should still be able to access the select-agent page
  /*
  if (
    !isLoading &&
    userData?.userInfo?.tenant?.claimedAgentSuites?.length &&
    userData.userInfo.tenant.claimedAgentSuites.length > 0
  ) {
    return <Navigate to={ROUTES.DASHBOARD_KNOWLEDGE_BASE} replace />;
  }
  */

  return (
    <AgentSelectionLayout
      title="Adaptive Process Library"
      description="Equip each agent with shared policies and tailored addendums, ensuring accuracy, consistency, and speed."
      bgImage={knowledgeBaseBg}
      pageType="knowledge-base"
      onAgentSuiteClick={suite => {
        if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
          navigate(
            ROUTES.DASHBOARD_KNOWLEDGE_BASE_ACTIVATE_SUITE(suite.agentSuiteKey)
          );
        } else {
          navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE, {
            state: { suite },
          });
        }
      }}
    />
  );
};

export default KnowledgeBaseSelectAgentPage;

import clsx from 'clsx';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { alertLine, cloudSyncComplete, gotoDoc, upload } from '@/assets/icons';
import { kbCurlLines } from '@/assets/images';
import { NotificationContainer } from '@/components/ui';
import AgentsDropdown from '@/components/ui/AgentsDropdown';
import { kbIcons } from '@/data/constants';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useNotifications } from '@/hooks/useNotifications';
import { useOnClickOutside } from '@/hooks/useOnClickOutside';

import { useTenant } from '../../../../context/TenantContext';
import { useGetUserProfile } from '../../../../hooks/useUserProfile';
import {
  KnowledgeBaseDocument as ServiceKnowledgeBaseDocument,
  KnowledgeBaseResponse,
  useAgentKnowledgeBase<PERSON><PERSON>,
  useSuiteKnowledgeBaseApi,
} from '../../../../services/knowledgeBaseService';
import { AgentSuite, UserBasicInfoPayload } from '../../../../types/user';
import KnowledgeBaseFileUploadModal from './KnowledgeBaseFileUploadModal';
import { UploadLevel } from './LevelSelector';

type KnowledgeBaseDocument = ServiceKnowledgeBaseDocument & {
  fileRef?: string;
  url?: string;
  size?: string;
  createdAt?: string;
  required?: boolean;
};

type UploadedDocumentInfo = KnowledgeBaseDocument & {
  fileTag: string;
};

interface KnowledgeBaseUploadsProps {
  selectedLevel: UploadLevel;
  currentSuite?: AgentSuite;
  onLevelSelect: (level: UploadLevel) => void;
  onDocumentSelect: (document: KnowledgeBaseDocument, index?: number) => void;
  onBack: () => void;
  onDocumentUpload?: (
    documentKey: string,
    documentName: string,
    files: File[]
  ) => Promise<void>;
  onDocumentReplace?: (
    documentKey: string,
    documentName: string,
    files: File[]
  ) => Promise<void>;
  onRefreshDocuments?: () => void;
}

const KnowledgeBaseUploads: React.FC<KnowledgeBaseUploadsProps> = ({
  selectedLevel,
  currentSuite,
  onLevelSelect,
  onDocumentSelect,
  // onBack,
  onDocumentUpload,
  onDocumentReplace,
  onRefreshDocuments,
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const kbTabs = isMobile
    ? { suite: 'Suite', agent: 'Agent' }
    : { suite: 'Suite Knowledge Base', agent: 'Agent Knowledge Base' };

  const [activeTab, setActiveTab] = useState<UploadLevel>(selectedLevel);
  const [selectedSuite, setSelectedSuite] = useState<string>(
    currentSuite?.agentSuiteKey || ''
  );
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] = useState(false);
  const [isAgentDropdownOpen, setIsAgentDropdownOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] =
    useState<KnowledgeBaseDocument | null>(null);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<
    Record<string, UploadedDocumentInfo>
  >({});
  const [uploadingDocuments, setUploadingDocuments] = useState<{
    [key: string]: boolean;
  }>({});

  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const { activeAgent, setActiveAgent } = useTenant();
  const { notifications, dismiss } = useNotifications();

  // Initialize API hooks
  const suiteKnowledgeBaseApi = useSuiteKnowledgeBaseApi();
  const agentKnowledgeBaseApi = useAgentKnowledgeBaseApi();

  // Get suite options from user data
  const suiteOptions = useMemo(
    () =>
      userData?.userInfo?.tenant?.claimedAgentSuites?.map(suite => ({
        id: suite.suite.agentSuiteKey,
        name: suite.suite.agentSuiteName,
        icon: suite.suite.avatar,
        fileData: suite.suite.fileData,
      })) || [],
    [userData]
  );

  // Get agents only from the currently selected suite
  const currentSuiteAgents = useMemo(
    () =>
      userData?.userInfo?.tenant?.claimedAgentSuites
        ?.find(suite => suite.suite.agentSuiteKey === selectedSuite)
        ?.suite.availableAgents.slice()
        .sort((a, b) =>
          (a.agentName || '').localeCompare(b.agentName || '', undefined, {
            sensitivity: 'base',
          })
        )
        .map(agent => ({
          ...agent,
          suiteKey: selectedSuite,
        })) || [],
    [userData, selectedSuite]
  );

  // Fetch existing documents from API
  const fetchExistingDocuments = useCallback(async () => {
    if (!activeTab) return;

    setIsLoadingDocuments(true);
    try {
      let response: KnowledgeBaseResponse;

      if (activeTab === 'suite' && selectedSuite) {
        response =
          await suiteKnowledgeBaseApi.checkSuiteKnowledgeBase(selectedSuite);
      } else if (activeTab === 'agent' && selectedAgent) {
        response =
          await agentKnowledgeBaseApi.checkAgentKnowledgeBase(selectedAgent);
      } else {
        return;
      }

      if (response.status && response.data?.knowledgeBaseFiles) {
        const documentsMap: Record<string, UploadedDocumentInfo> = {};
        response.data.knowledgeBaseFiles.forEach(
          (file: UploadedDocumentInfo) => {
            documentsMap[file.fileTag] = file;
          }
        );
        setUploadedDocuments(documentsMap);
      } else {
        setUploadedDocuments({});
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error fetching existing documents:', error);
      setUploadedDocuments({});
    } finally {
      setIsLoadingDocuments(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, selectedSuite, selectedAgent]);

  // Get agent documents based on selected agent
  const getAgentDocuments = (): KnowledgeBaseDocument[] => {
    if (!selectedAgent) return [];

    const agent = currentSuiteAgents.find(a => a.agentKey === selectedAgent);
    if (!agent?.fileData) return [];

    return agent.fileData.map(file => ({
      iconUrl: file.iconUrl,
      name: file.name,
      key: file.key,
      description: file.description,
      hasFile: !!uploadedDocuments[file.key],
      fileName: uploadedDocuments[file.key]?.name,
      fileRef: uploadedDocuments[file.key]?.fileRef,
      url: uploadedDocuments[file.key]?.url,
      size:
        typeof uploadedDocuments[file.key]?.size === 'number'
          ? String(uploadedDocuments[file.key]?.size)
          : (uploadedDocuments[file.key]?.size ?? ''),
      createdAt: uploadedDocuments[file.key]?.createdAt,
      required: file.required || false,
    }));
  };

  // Get suite documents with uploaded status
  const getSuiteDocuments = (): KnowledgeBaseDocument[] => {
    if (!selectedSuite) return [];

    const suite = suiteOptions.find(s => s.id === selectedSuite);
    if (!suite?.fileData) return [];

    return suite.fileData.map(file => ({
      iconUrl: file.iconUrl,
      name: file.name,
      key: file.key,
      description: file.description,
      hasFile: !!uploadedDocuments[file.key],
      fileName: uploadedDocuments[file.key]?.name,
      fileRef: uploadedDocuments[file.key]?.fileRef,
      url: uploadedDocuments[file.key]?.url,
      size:
        typeof uploadedDocuments[file.key]?.size === 'number'
          ? String(uploadedDocuments[file.key]?.size)
          : (uploadedDocuments[file.key]?.size ?? ''),
      createdAt: uploadedDocuments[file.key]?.createdAt,
      required: file.required || false,
    }));
  };

  // Ensure suite is initialized when suiteOptions become available
  useEffect(() => {
    if (suiteOptions.length > 0 && !selectedSuite) {
      // If no suite selected yet, default to first suite
      setSelectedSuite(suiteOptions[0].id);
    }
  }, [suiteOptions, selectedSuite]);

  // Ensure an agent is always selected when suite changes or agents are available
  useEffect(() => {
    if (currentSuiteAgents.length > 0 && activeTab === 'agent') {
      // Check if current selected agent exists in the current suite
      const agentExistsInSuite = currentSuiteAgents.some(
        agent => agent.agentKey === selectedAgent
      );

      // If no agent is selected or the selected agent doesn't exist in current suite
      if (!selectedAgent || !agentExistsInSuite) {
        // Prefer activeAgent if it exists in current suite, otherwise use first agent
        const agentToSelect =
          currentSuiteAgents.find(agent => agent.agentKey === activeAgent)
            ?.agentKey || currentSuiteAgents[0].agentKey;
        setSelectedAgent(agentToSelect);
        setActiveAgent(agentToSelect);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentSuiteAgents, selectedSuite, activeTab]);

  // Fetch documents when component mounts or selections change
  useEffect(() => {
    if (activeTab === 'suite' && selectedSuite) {
      fetchExistingDocuments();
    } else if (activeTab === 'agent' && selectedAgent) {
      fetchExistingDocuments();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, selectedSuite, selectedAgent]);

  const suiteDropdownRef = useRef<HTMLDivElement>(null);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(suiteDropdownRef, () => setIsSuiteDropdownOpen(false));
  useOnClickOutside(agentDropdownRef, () => setIsAgentDropdownOpen(false));

  const handleTabLevelChange = (level: UploadLevel) => {
    onLevelSelect(level);
    setActiveTab(level);
  };

  // Handle agent selection change
  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    setIsAgentDropdownOpen(false);
  };

  const handleDocumentCardClick = (
    doc: KnowledgeBaseDocument,
    index?: number
  ) => {
    onDocumentSelect(doc, index);
  };

  const handleUploadClick = (doc: KnowledgeBaseDocument) => {
    setSelectedDocument(doc);
    setUploadModalOpen(true);
  };

  const handleFilesUpload = async (files: File[]) => {
    if (!selectedDocument) return;

    // Set loading state for this document
    setUploadingDocuments(prev => ({
      ...prev,
      [selectedDocument.key]: true,
    }));

    try {
      // Close modal immediately to show loading state
      setUploadModalOpen(false);

      // Await the upload/replace operation
      if (selectedDocument?.hasFile && selectedDocument.fileRef) {
        if (onDocumentReplace && selectedDocument.fileRef) {
          await onDocumentReplace(
            selectedDocument.fileRef,
            selectedDocument.name,
            files
          );
        }
      } else if (onDocumentUpload && selectedDocument) {
        await onDocumentUpload(
          selectedDocument.key,
          selectedDocument.name,
          files
        );
      }

      // Refresh documents after successful upload
      await fetchExistingDocuments();
      onRefreshDocuments?.();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Upload failed:', error);
    } finally {
      // Clear loading state for this document
      setUploadingDocuments(prev => ({
        ...prev,
        [selectedDocument.key]: false,
      }));
    }
  };

  // Empty state component
  const EmptyState = ({ level }: { level: 'suite' | 'agent' }) => {
    const currentSuiteName =
      suiteOptions.find(s => s.id === selectedSuite)?.name || 'suite';
    const currentAgentName =
      currentSuiteAgents.find(a => a.agentKey === selectedAgent)?.agentName ||
      'agent';

    return (
      <div className="flex h-[360px] w-full flex-col items-center justify-center rounded-xl border border-dashed border-grayTwentyThree bg-gray-50 px-8 py-12 md:w-[648px]">
        <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-lightPeach">
          <svg
            className="h-8 w-8 text-primary"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 className="mb-2 font-spartan text-lg font-semibold text-blackOne">
          No {level === 'suite' ? 'Suite' : 'Agent'} Documents Available
        </h3>
        <p className="text-center text-sm text-subText">
          {level === 'suite'
            ? `There are no documents configured for the ${currentSuiteName} knowledge base yet.`
            : `There are no documents configured for ${currentAgentName} knowledge base yet.`}
        </p>
      </div>
    );
  };

  const renderDocumentCard = (doc: KnowledgeBaseDocument, index: number) => (
    <div
      key={doc.key}
      onClick={() => handleDocumentCardClick(doc, index)}
      className="relative flex w-full cursor-pointer flex-col rounded-xl border border-grayTwentyThree px-4 py-6 transition-shadow hover:shadow-md sm:w-[312px]"
      style={{ minHeight: '360px' }}
    >
      <div
        className="rounded-2xl bg-banner p-4"
        style={{
          backgroundImage: `url(${kbCurlLines})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          minHeight: '124px',
        }}
      >
        <img
          className="h-8 w-8"
          src={doc.iconUrl}
          alt={doc.name}
          onError={e => {
            (e.target as HTMLImageElement).src =
              kbIcons[index % kbIcons.length];
          }}
        />

        <h3 className="mt-4 font-spartan text-base font-semibold text-white">
          {doc.name}
        </h3>
      </div>
      <div className="mt-4 text-sm text-subText">{doc.description}</div>
      <div className="mt-4 flex items-center gap-3 text-sm text-subText">
        Status:
        <div className="flex items-center gap-2 rounded bg-[#EEF7FE] px-4 py-1">
          <img src={doc.hasFile ? cloudSyncComplete : alertLine} alt="status" />
          <span>{doc.hasFile ? 'Completed' : 'Missing'}</span>
        </div>
      </div>
      <div className="mt-auto flex w-full items-center justify-center">
        {doc.hasFile ? (
          <div className="flex w-full flex-col gap-2 sm:w-[280px]">
            <button
              onClick={() => {
                handleDocumentCardClick(doc, index);
              }}
              className="upload-button flex h-10 w-full items-center justify-center gap-2 rounded-full border border-primary px-3.5 py-1.5 text-sm font-medium text-primary transition-colors hover:border-primary hover:bg-lightOrangeTwo"
            >
              <img src={gotoDoc} alt="View Document" />
              View Document
            </button>
          </div>
        ) : (
          <button
            onClick={e => {
              e.stopPropagation();
              handleUploadClick(doc);
            }}
            disabled={uploadingDocuments[doc.key]}
            className={`upload-button flex h-10 w-full items-center justify-center gap-2 rounded-full px-3.5 py-1.5 text-sm font-medium transition-colors sm:w-[280px] ${
              uploadingDocuments[doc.key]
                ? 'cursor-not-allowed bg-gray-400 text-gray-600'
                : 'bg-primary text-white hover:bg-orange-15'
            }`}
          >
            {uploadingDocuments[doc.key] ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                Uploading...
              </>
            ) : (
              <>
                <img src={upload} alt="Upload Document" />
                Upload Document
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6 p-4 sm:p-0">
      {/* Notifications Container */}
      <NotificationContainer
        notifications={notifications}
        onClose={dismiss}
        className="w-full md:w-[648px]"
        maxNotifications={3}
      />

      {/* Tab Selectors with Dropdowns */}
      <div className="flex items-center justify-between md:w-[648px]">
        <div className="flex w-fit space-x-1 border-b border-gray-200">
          {(Object.keys(kbTabs) as UploadLevel[]).map(tab => (
            <button
              key={tab}
              onClick={() => handleTabLevelChange(tab)}
              className={clsx(
                'px-4 py-2 text-sm font-medium transition-colors',
                activeTab === tab
                  ? 'border-b-2 border-primary text-primary'
                  : 'text-gray-600 hover:text-blackOne'
              )}
            >
              {kbTabs[tab]}
            </button>
          ))}
        </div>

        {/* Dropdowns */}
        <div className="flex gap-4">
          {activeTab === 'suite' && (
            <div className="relative" ref={suiteDropdownRef}>
              <AgentsDropdown
                isOpen={isSuiteDropdownOpen}
                onToggle={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
                currentItem={suiteOptions.find(s => s.id === selectedSuite)}
                options={suiteOptions}
                onItemSelect={suite => {
                  setSelectedSuite(suite.id);
                  setIsSuiteDropdownOpen(false);
                }}
                placeholder="Suite"
                noOptionsMessage="No other suites available"
              />
            </div>
          )}

          {activeTab === 'agent' && (
            <div className="relative" ref={agentDropdownRef}>
              <AgentsDropdown
                isOpen={isAgentDropdownOpen}
                onToggle={() => setIsAgentDropdownOpen(!isAgentDropdownOpen)}
                currentItem={currentSuiteAgents
                  .map(a => ({
                    id: a.agentKey,
                    name: a.agentName,
                    icon: a.avatar,
                  }))
                  .find(a => a.id === selectedAgent)}
                options={currentSuiteAgents.map(a => ({
                  id: a.agentKey,
                  name: a.agentName,
                  icon: a.avatar,
                }))}
                onItemSelect={agent => handleAgentChange(agent.id)}
                placeholder="Agent"
                noOptionsMessage="No other agents available"
              />
            </div>
          )}
        </div>
      </div>

      {/* Document Grid */}
      {isLoadingDocuments ? (
        <div className="flex flex-col items-start justify-center gap-4">
          <div className="h-4 w-40 animate-pulse rounded-full bg-gray-200" />
          <hr className="h-px border-grayTwentyEight" />
          <div className="grid w-fit grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Skeleton loading placeholders */}
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="flex min-h-[360px] w-full min-w-[350px] flex-1 animate-pulse flex-col rounded-2xl border border-grayTwentyThree p-6 sm:w-[312px]"
              >
                <div className="flex w-full items-start gap-2">
                  <div className="h-8 w-8 rounded bg-gray-200"></div>
                  <div className="h-6 w-32 rounded bg-gray-200"></div>
                </div>
                <div className="mt-2.5 w-full space-y-2">
                  <div className="h-4 w-full rounded bg-gray-200"></div>
                  <div className="h-4 w-3/4 rounded bg-gray-200"></div>
                </div>
                <div className="mt-auto h-9 w-full rounded-[30px] bg-gray-200 sm:w-[280px]"></div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        (() => {
          const documents =
            activeTab === 'suite' ? getSuiteDocuments() : getAgentDocuments();
          const requiredDocs = documents.filter(doc => doc.required);
          const optionalDocs = documents.filter(doc => !doc.required);

          if (documents.length === 0) {
            return <EmptyState level={activeTab} />;
          }

          return (
            <div className="space-y-4 sm:space-y-8">
              {/* Required Uploads Section */}
              {requiredDocs.length > 0 && (
                <div className="space-y-4">
                  <h3 className="font-inter text-base font-medium text-grayTen sm:text-xl">
                    Required Uploads
                  </h3>
                  <hr className="h-px border-grayTwentyEight" />
                  <div className="grid w-fit grid-cols-1 gap-6 lg:grid-cols-2">
                    {requiredDocs.map((doc, index) =>
                      renderDocumentCard(doc, index)
                    )}
                  </div>
                </div>
              )}

              {/* Optional Uploads Section */}
              {optionalDocs.length > 0 && (
                <div className="space-y-4">
                  <h3 className="font-inter text-base font-medium text-grayTen sm:text-xl">
                    Optional Uploads
                  </h3>
                  <hr className="h-px border-grayTwentyEight" />
                  <div className="grid w-fit grid-cols-1 gap-6 lg:grid-cols-2">
                    {optionalDocs.map((doc, index) =>
                      renderDocumentCard(doc, index)
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })()
      )}

      {/* Upload Modal */}
      <KnowledgeBaseFileUploadModal
        multiple={false}
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        title={selectedDocument?.name || 'Upload Document'}
        onFilesUpload={handleFilesUpload}
        currentAgent={
          selectedLevel === 'agent'
            ? currentSuiteAgents
                .map(a => ({
                  id: a.agentKey,
                  name: a.agentName,
                  icon: a.avatar,
                }))
                .find(a => a.id === selectedAgent)
            : suiteOptions.find(s => s.id === selectedSuite)
        }
      />
    </div>
  );
};

export default KnowledgeBaseUploads;

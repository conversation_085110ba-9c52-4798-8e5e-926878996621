import { motion } from 'framer-motion';
import { BookOpen } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import AppContainer from '@/components/common/AppContainer';
import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import { useNotifications } from '@/components/ui/notifications';
import { useTenant } from '@/context/TenantContext';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import {
  KnowledgeBaseDeleteRequest,
  KnowledgeBaseReplaceRequest,
  KnowledgeBaseUploadRequest,
  useAgentKnowledgeBaseApi,
  useSuiteKnowledgeBaseApi,
} from '../../../services/knowledgeBaseService';
import { AgentSuite, UserBasicInfoPayload } from '../../../types/user';
import DocumentDetails from './components/DocumentDetails';
import KnowledgeBaseUploads from './components/KnowledgeBaseUploads';
import LevelSelector, { UploadLevel } from './components/LevelSelector';

export type KnowledgeBaseStep =
  | 'level-selector'
  | 'uploads'
  | 'document-details';

interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  fileRef?: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
  url?: string;
  size?: string;
  createdAt?: string;
  pageCount?: number;
  required?: boolean;
}

interface KnowledgeBaseState {
  step: KnowledgeBaseStep;
  selectedLevel?: UploadLevel;
  selectedDocument?: KnowledgeBaseDocument;
  documentIndex?: number;
}

const KnowledgeBasePage: React.FC = () => {
  const { activeAgent, claimedSuites } = useTenant();
  const { clearNotifications } = useNotifications();
  const location = useLocation();
  const locationStateSuite = location.state as AgentSuite;
  // Get current suite
  const currentSuite: AgentSuite | undefined =
    locationStateSuite || claimedSuites[0]?.suite;

  const [chatMessage, setChatMessage] = useState<string>('');
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // API hooks
  const suiteKnowledgeBaseApi = useSuiteKnowledgeBaseApi();
  const agentKnowledgeBaseApi = useAgentKnowledgeBaseApi();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();

  // Get all agents from all suites
  const allAgents =
    userData?.userInfo?.tenant?.claimedAgentSuites?.flatMap(suite =>
      suite.suite.availableAgents.map(agent => ({
        ...agent,
        suiteKey: suite.suite.agentSuiteKey,
      }))
    ) || [];

  // Component state
  const [state, setState] = useState<KnowledgeBaseState>({
    step: 'level-selector',
  });
  const [isLoading, setIsLoading] = useState(false);

  // Trigger reload of chat history when agent changes
  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error('Error occured while changing agent:', error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  // State update helper
  const updateState = (updates: Partial<KnowledgeBaseState>) => {
    // clear all notifications when changing steps
    clearNotifications();
    setState(prev => ({ ...prev, ...updates }));
  };

  // Navigation handlers
  const handleLevelSelect = (level: UploadLevel) => {
    updateState({ selectedLevel: level });
  };

  const handleNext = () => {
    if (state.step === 'level-selector') {
      updateState({ step: 'uploads' });
    }
  };

  const handleBack = () => {
    if (state.step === 'uploads') {
      updateState({ step: 'level-selector' });
    } else if (state.step === 'document-details') {
      updateState({ step: 'uploads' });
    }
  };

  const handleDocumentSelect = (document: KnowledgeBaseDocument, index = 0) => {
    updateState({
      step: 'document-details',
      selectedDocument: document,
      documentIndex: index,
    });
  };

  const handleRefreshDocuments = () => {
    // additional refresh logic
  };

  // Document action handlers with API integration
  const handleDocumentUpload = async (
    documentKey: string,
    documentName: string,
    files: File[]
  ) => {
    if (!files.length || !state.selectedLevel) return;

    setIsLoading(true);
    try {
      setChatMessage('');
      const uploadRequest: KnowledgeBaseUploadRequest = {
        filePart: files[0],
        fileTag: documentKey,
      };

      let response: any;
      if (state.selectedLevel === 'suite' && currentSuite) {
        response = await suiteKnowledgeBaseApi.uploadSuiteKnowledgeBase(
          currentSuite.agentSuiteKey,
          uploadRequest
        );
      } else if (state.selectedLevel === 'agent') {
        response =
          await agentKnowledgeBaseApi.uploadAgentKnowledgeBase(uploadRequest);
      }

      if (response.status === true) {
        // Update document state to reflect upload
        if (state.selectedDocument) {
          setState(prev => ({
            ...prev,
            selectedDocument: {
              ...prev.selectedDocument!,
              hasFile: true,
              fileName: files[0].name,
              uploadedAt: new Date().toISOString(),
            },
          }));
        }
      } else {
        console.error('Failed to upload document');
      }
    } catch (error) {
      console.error('Upload document error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentReplace = async (
    documentRef: string,
    documentName: string,
    files: File[]
  ) => {
    if (!files.length || !state.selectedLevel) return;

    setIsLoading(true);
    try {
      const uploadRequest: KnowledgeBaseReplaceRequest = {
        filePart: files[0],
        fileRef: documentRef,
      };

      let response: any;
      if (state.selectedLevel === 'suite' && currentSuite) {
        response = await suiteKnowledgeBaseApi.replaceSuiteKnowledgeBase(
          currentSuite.agentSuiteKey,
          uploadRequest
        );
      } else if (state.selectedLevel === 'agent') {
        response =
          await agentKnowledgeBaseApi.replaceAgentKnowledgeBase(uploadRequest);
      }

      if (response.status === true) {
        // Update document state to reflect replacement
        if (state.selectedDocument) {
          setState(prev => ({
            ...prev,
            selectedDocument: {
              ...prev.selectedDocument!,
              hasFile: true,
              fileName: files[0].name,
              uploadedAt: new Date().toISOString(),
            },
          }));
        }
      } else {
        console.error('Failed to replace document');
      }
    } catch (error) {
      console.error('Replace document error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentDelete = async (documentKey: string) => {
    if (!state.selectedLevel) return;

    setIsLoading(true);
    try {
      const deleteRequest: KnowledgeBaseDeleteRequest = {
        fileTag: documentKey,
      };

      let response: any;
      if (state.selectedLevel === 'suite' && currentSuite) {
        response = await suiteKnowledgeBaseApi.deleteSuiteKnowledgeBase(
          currentSuite.agentSuiteKey,
          deleteRequest
        );
      } else if (state.selectedLevel === 'agent') {
        response =
          await agentKnowledgeBaseApi.deleteAgentKnowledgeBase(deleteRequest);
      }

      if (response.status === true) {
        // Update document state to reflect deletion
        if (state.selectedDocument) {
          setState(prev => ({
            ...prev,
            selectedDocument: {
              ...prev.selectedDocument!,
              hasFile: false,
              fileName: undefined,
              uploadedAt: undefined,
            },
          }));
        }
      } else {
        console.error('Failed to delete document');
      }
    } catch (error) {
      console.error('Delete document error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Render current step
  const renderStep = () => {
    switch (state.step) {
      case 'level-selector':
        return (
          <LevelSelector
            currentSuite={currentSuite}
            selectedLevel={state.selectedLevel}
            onLevelSelect={handleLevelSelect}
            onNext={handleNext}
          />
        );
      case 'uploads':
        return (
          <KnowledgeBaseUploads
            selectedLevel={state.selectedLevel!}
            currentSuite={currentSuite}
            onLevelSelect={handleLevelSelect}
            onDocumentSelect={handleDocumentSelect}
            onBack={handleBack}
            onDocumentUpload={handleDocumentUpload}
            onDocumentReplace={handleDocumentReplace}
            onRefreshDocuments={handleRefreshDocuments}
          />
        );
      case 'document-details':
        return state.selectedDocument ? (
          <DocumentDetails
            document={state.selectedDocument}
            onBack={handleBack}
            onDelete={handleDocumentDelete}
            onUpload={handleDocumentUpload}
            onReplace={handleDocumentReplace}
            isLoading={isLoading}
            index={state.documentIndex}
            selectedAgent={activeAgent}
            selectedSuite={currentSuite}
            selectedLevel={state.selectedLevel!}
            allAgents={allAgents.map(a => ({
              id: a.agentKey,
              name: a.agentName,
              icon: a.avatar,
            }))}
          />
        ) : null;
      default:
        return null;
    }
  };

  // TODO: Optimize and add back after updating the component to use the new design @joshua 2025-08-29
  // if (!hasClaimedTenants) {
  //   return (
  //     <Navigate to={ROUTES.DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT} replace />
  //   );
  // }

  // Main Knowledge Base page for users with claimed tenants
  return (
    <DashboardWithChatLayout
      reloadChatHistoryRef={reloadChatHistoryRef}
      externalMessage={chatMessage}
    >
      <AppContainer
        isPadding={false}
        className="space-y-6 p-0 sm:p-8 lg:space-y-8"
      >
        {/* Header */}
        <div className="mb-0 flex items-center justify-between px-4 pt-4 sm:mb-8 sm:px-0 sm:pt-0">
          <div className="flex items-center">
            <BookOpen className="mr-3 h-5 w-5 text-primary md:h-6 md:w-6" />
            <h1 className="text-lg font-semibold text-[#403F3E] sm:text-2xl">
              Knowledge Base
            </h1>
          </div>
        </div>

        {/* Step Content */}
        <motion.div
          key={state.step}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStep()}
        </motion.div>
      </AppContainer>
    </DashboardWithChatLayout>
  );
};

export default KnowledgeBasePage;

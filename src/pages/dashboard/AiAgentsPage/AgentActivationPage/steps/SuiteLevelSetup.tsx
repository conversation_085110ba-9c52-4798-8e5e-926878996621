import clsx from 'clsx';
import { ArrowLeftIcon, ArrowRight, ChevronDown } from 'lucide-react';
import React, { useState } from 'react';

import { AgentSuite } from '../../../../../data/constants';
import DocumentUploadCard from '../components/DocumentUploadCard';
import FileUploadModal from '../components/FileUploadModal';

interface SuiteLevelSetupProps {
  selectedSuite: string | null;
  uploadedDocuments: Record<string, File[]>;
  onDocumentsUpdate: (docs: Record<string, File[]>) => void;
  onNext: () => void;
  onBack: () => void;
  currentSuite?: AgentSuite | null;
}

const SuiteLevelSetup: React.FC<SuiteLevelSetupProps> = ({
  uploadedDocuments,
  onDocumentsUpdate,
  onNext,
  onBack,
  currentSuite,
}) => {
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [activeUploadCategory, setActiveUploadCategory] = useState<string>('');
  const [isSuiteDropdownOpen, setIsSuiteDropdownOpen] =
    useState<boolean>(false);

  // Mock suite options - replace with actual data
  const suiteOptions = [
    { id: 'setiq', name: 'SetIQ', icon: currentSuite?.image },
    { id: 'other', name: 'Other Suite', icon: null },
  ];

  const uploadCategories = [
    {
      id: 'organization',
      title: 'About Your Organization',
      description: 'Overview of who you are, what you do, and your mission.',
      icon: (
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12.5 2C6.97717 2 2.5 6.47713 2.5 12C2.5 17.5228 6.97717 22 12.5 22C18.0229 22 22.5 17.5228 22.5 12C22.5 6.47713 18.0229 2 12.5 2ZM12.5 20C8.08881 20 4.50002 16.4112 4.50002 12C4.50002 7.58877 8.08877 4.00002 12.5 4.00002C16.9113 4.00002 20.5 7.58877 20.5 12C20.5 16.4112 16.9113 20 12.5 20ZM13.7522 8C13.7522 8.72506 13.2243 9.25002 12.5102 9.25002C11.7671 9.25002 11.2522 8.72502 11.2522 7.98612C11.2522 7.27597 11.7811 6.75003 12.5102 6.75003C13.2243 6.75003 13.7522 7.27597 13.7522 8ZM11.5022 11H13.5022V17H11.5022V11Z"
            fill="#FF3E00"
          />
        </svg>
      ),
    },
    {
      id: 'processes',
      title: 'Department Processes',
      description:
        "Guidelines for how your department's business function operates across roles.",
      icon: (
        <svg
          width="19"
          height="21"
          viewBox="0 0 19 21"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12.501 3.00084C12.5013 3.62149 12.3092 4.22697 11.9509 4.73382C11.5927 5.24068 11.0862 5.62396 10.501 5.83084V8.00084H13.501C14.2967 8.00084 15.0597 8.31691 15.6223 8.87952C16.1849 9.44213 16.501 10.2052 16.501 11.0008V12.1708C17.1685 12.4067 17.7311 12.871 18.0894 13.4816C18.4476 14.0923 18.5785 14.8099 18.4588 15.5076C18.3391 16.2054 17.9766 16.8384 17.4354 17.2947C16.8941 17.751 16.209 18.0013 15.501 18.0013C14.7931 18.0013 14.1079 17.751 13.5666 17.2947C13.0254 16.8384 12.6629 16.2054 12.5432 15.5076C12.4235 14.8099 12.5544 14.0923 12.9126 13.4816C13.2709 12.871 13.8335 12.4067 14.501 12.1708V11.0008C14.501 10.7356 14.3956 10.4813 14.2081 10.2937C14.0206 10.1062 13.7662 10.0008 13.501 10.0008H5.50101C5.23579 10.0008 4.98144 10.1062 4.7939 10.2937C4.60636 10.4813 4.50101 10.7356 4.50101 11.0008V12.1708C5.1685 12.4067 5.7311 12.871 6.08937 13.4816C6.44763 14.0923 6.57849 14.8099 6.45882 15.5076C6.33914 16.2054 5.97664 16.8384 5.43538 17.2947C4.89412 17.751 4.20896 18.0013 3.50101 18.0013C2.79305 18.0013 2.10789 17.751 1.56663 17.2947C1.02538 16.8384 0.66287 16.2054 0.543195 15.5076C0.42352 14.8099 0.55438 14.0923 0.912646 13.4816C1.27091 12.871 1.83351 12.4067 2.50101 12.1708V11.0008C2.50101 10.2052 2.81708 9.44213 3.37969 8.87952C3.9423 8.31691 4.70536 8.00084 5.50101 8.00084H8.50101V5.83084C7.98216 5.64768 7.52365 5.32539 7.17561 4.89921C6.82758 4.47303 6.60341 3.95937 6.52761 3.41438C6.45182 2.8694 6.52732 2.31406 6.74586 1.80909C6.96439 1.30412 7.31756 0.868944 7.76674 0.551149C8.21592 0.233354 8.74382 0.0451692 9.29274 0.00716706C9.84166 -0.030835 10.3905 0.0828087 10.8791 0.335674C11.3678 0.58854 11.7776 0.970893 12.0636 1.44093C12.3497 1.91098 12.501 2.45061 12.501 3.00084ZM9.50101 2.00084C9.23579 2.00084 8.98144 2.1062 8.7939 2.29374C8.60636 2.48127 8.50101 2.73563 8.50101 3.00084C8.50101 3.26606 8.60636 3.52041 8.7939 3.70795C8.98144 3.89549 9.23579 4.00084 9.50101 4.00084C9.76622 4.00084 10.0206 3.89549 10.2081 3.70795C10.3956 3.52041 10.501 3.26606 10.501 3.00084C10.501 2.73563 10.3956 2.48127 10.2081 2.29374C10.0206 2.1062 9.76622 2.00084 9.50101 2.00084ZM3.50101 14.0008C3.23579 14.0008 2.98144 14.1062 2.7939 14.2937C2.60636 14.4813 2.50101 14.7356 2.50101 15.0008C2.50101 15.2661 2.60636 15.5204 2.7939 15.7079C2.98144 15.8955 3.23579 16.0008 3.50101 16.0008C3.76622 16.0008 4.02058 15.8955 4.20811 15.7079C4.39565 15.5204 4.50101 15.2661 4.50101 15.0008C4.50101 14.7356 4.39565 14.4813 4.20811 14.2937C4.02058 14.1062 3.76622 14.0008 3.50101 14.0008ZM15.501 14.0008C15.2358 14.0008 14.9814 14.1062 14.7939 14.2937C14.6064 14.4813 14.501 14.7356 14.501 15.0008C14.501 15.2661 14.6064 15.5204 14.7939 15.7079C14.9814 15.8955 15.2358 16.0008 15.501 16.0008C15.7662 16.0008 16.0206 15.8955 16.2081 15.7079C16.3956 15.5204 16.501 15.2661 16.501 15.0008C16.501 14.7356 16.3956 14.4813 16.2081 14.2937C16.0206 14.1062 15.7662 14.0008 15.501 14.0008Z"
            fill="#FF3E00"
          />
        </svg>
      ),
    },
    {
      id: 'compliance',
      title: 'Communication Compliance Guidelines',
      description:
        'Policies and standards for written and verbal communications.',
      icon: (
        <svg
          width="21"
          height="18"
          viewBox="0 0 21 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.5 13.5H13.25C16.702 13.5 19.5 10.702 19.5 7.25C19.5 3.798 16.702 1 13.25 1H7.75C4.298 1 1.5 3.798 1.5 7.25C1.5 11.2075 4.1085 13.627 6.962 15.113C8.3795 15.8515 9.807 16.328 10.8865 16.6205C11.1082 16.6805 11.3127 16.7327 11.5 16.777V13.5ZM12.5 18C12.5 18 12.122 17.945 11.5 17.804C8.618 17.15 0.5 14.6525 0.5 7.25C0.5 3.246 3.746 0 7.75 0H13.25C17.254 0 20.5 3.246 20.5 7.25C20.5 11.254 17.254 14.5 13.25 14.5H12.5V18Z"
            fill="#FF3E00"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.5 8C10.6326 8 10.7598 7.94732 10.8536 7.85355C10.9473 7.75979 11 7.63261 11 7.5C11 7.36739 10.9473 7.24021 10.8536 7.14645C10.7598 7.05268 10.6326 7 10.5 7C10.3674 7 10.2402 7.05268 10.1464 7.14645C10.0527 7.24021 10 7.36739 10 7.5C10 7.63261 10.0527 7.75979 10.1464 7.85355C10.2402 7.94732 10.3674 8 10.5 8ZM10.5 9C10.8978 9 11.2794 8.84196 11.5607 8.56066C11.842 8.27936 12 7.89782 12 7.5C12 7.10218 11.842 6.72064 11.5607 6.43934C11.2794 6.15804 10.8978 6 10.5 6C10.1022 6 9.72064 6.15804 9.43934 6.43934C9.15804 6.72064 9 7.10218 9 7.5C9 7.89782 9.15804 8.27936 9.43934 8.56066C9.72064 8.84196 10.1022 9 10.5 9ZM14.5 8C14.6326 8 14.7598 7.94732 14.8536 7.85355C14.9473 7.75979 15 7.63261 15 7.5C15 7.36739 14.9473 7.24021 14.8536 7.14645C14.7598 7.05268 14.6326 7 14.5 7C14.3674 7 14.2402 7.05268 14.1464 7.14645C14.0527 7.24021 14 7.36739 14 7.5C14 7.63261 14.0527 7.75979 14.1464 7.85355C14.2402 7.94732 14.3674 8 14.5 8ZM14.5 9C14.8978 9 15.2794 8.84196 15.5607 8.56066C15.842 8.27936 16 7.89782 16 7.5C16 7.10218 15.842 6.72064 15.5607 6.43934C15.2794 6.15804 14.8978 6 14.5 6C14.1022 6 13.7206 6.15804 13.4393 6.43934C13.158 6.72064 13 7.10218 13 7.5C13 7.89782 13.158 8.27936 13.4393 8.56066C13.7206 8.84196 14.1022 9 14.5 9ZM6.5 8C6.63261 8 6.75979 7.94732 6.85355 7.85355C6.94732 7.75979 7 7.63261 7 7.5C7 7.36739 6.94732 7.24021 6.85355 7.14645C6.75979 7.05268 6.63261 7 6.5 7C6.36739 7 6.24021 7.05268 6.14645 7.14645C6.05268 7.24021 6 7.36739 6 7.5C6 7.63261 6.05268 7.75979 6.14645 7.85355C6.24021 7.94732 6.36739 8 6.5 8ZM6.5 9C6.89782 9 7.27936 8.84196 7.56066 8.56066C7.84196 8.27936 8 7.89782 8 7.5C8 7.10218 7.84196 6.72064 7.56066 6.43934C7.27936 6.15804 6.89782 6 6.5 6C6.10218 6 5.72064 6.15804 5.43934 6.43934C5.15804 6.72064 5 7.10218 5 7.5C5 7.89782 5.15804 8.27936 5.43934 8.56066C5.72064 8.84196 6.10218 9 6.5 9Z"
            fill="#FF3E00"
          />
        </svg>
      ),
    },
  ];

  const handleUploadClick = (categoryId: string) => {
    setActiveUploadCategory(categoryId);
    setUploadModalOpen(true);
  };

  const handleFilesUpload = (files: File[]) => {
    const updatedDocs = {
      ...uploadedDocuments,
      [activeUploadCategory]: [
        ...(uploadedDocuments[activeUploadCategory] || []),
        ...files,
      ],
    };
    onDocumentsUpdate(updatedDocs);
    setUploadModalOpen(false);
  };

  const handleFileRemove = (categoryId: string, fileIndex: number) => {
    const updatedDocs = {
      ...uploadedDocuments,
      [categoryId]:
        uploadedDocuments[categoryId]?.filter(
          (_, index) => index !== fileIndex
        ) || [],
    };
    onDocumentsUpdate(updatedDocs);
  };

  return (
    <div className="mx-auto flex w-full max-w-[640px] flex-col gap-6 rounded-xl bg-white p-6">
      {/* Header */}
      <div className="w-full rounded-xl bg-[#363D8808] p-4 ">
        <div className="mx-auto flex w-full max-w-[510px] items-center justify-between gap-4">
          <h2 className="text-lg font-semibold text-blackOne">
            AI Agents Suite Level
          </h2>

          {/* Suite Selector Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsSuiteDropdownOpen(!isSuiteDropdownOpen)}
              className="flex h-[48px] w-fit items-center gap-3 rounded-lg bg-white px-4 py-2 hover:border focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded bg-[#F9FAFB]">
                {currentSuite?.image ? (
                  <img
                    src={currentSuite.image}
                    alt="Suite Icon"
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-4 w-4 rounded bg-gray-300" />
                )}
              </div>
              <span className="text-sm font-semibold text-blackOne">
                {currentSuite?.name || 'SetIQ'}
              </span>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </button>

            {/* Dropdown Menu */}
            {isSuiteDropdownOpen && (
              <div className="absolute right-0 top-full z-50 mt-1 w-64 rounded-xl border border-gray-200 bg-white shadow-[0px_8px_16px_-2px_#1B212C1F]">
                <div className="py-1">
                  {suiteOptions.map(suite => (
                    <button
                      key={suite.id}
                      onClick={() => {
                        // Handle suite selection here
                        setIsSuiteDropdownOpen(false);
                      }}
                      className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm font-medium transition-colors hover:bg-gray-50"
                    >
                      <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded bg-[#F9FAFB]">
                        {suite.icon ? (
                          <img
                            src={suite.icon}
                            alt="Suite Icon"
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-4 w-4 rounded bg-gray-300" />
                        )}
                      </div>
                      {suite.name}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Upload Categories */}
      <div className="flex flex-col items-center justify-center gap-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* First two cards */}
          {uploadCategories.slice(0, 2).map(category => (
            <DocumentUploadCard
              key={category.id}
              title={category.title}
              description={category.description}
              icon={category.icon}
              uploadedFiles={uploadedDocuments[category.id] || []}
              onUploadClick={() => handleUploadClick(category.id)}
              onFileRemove={fileIndex =>
                handleFileRemove(category.id, fileIndex)
              }
            />
          ))}
        </div>

        {/* Third card centered on second row */}
        <div className="grid w-full grid-cols-1 gap-4 md:grid-cols-2">
          <div className="col-span-2 w-fit place-content-center place-self-center">
            <DocumentUploadCard
              title={uploadCategories[2].title}
              description={uploadCategories[2].description}
              icon={uploadCategories[2].icon}
              uploadedFiles={uploadedDocuments[uploadCategories[2].id] || []}
              onUploadClick={() => handleUploadClick(uploadCategories[2].id)}
              onFileRemove={fileIndex =>
                handleFileRemove(uploadCategories[2].id, fileIndex)
              }
            />
          </div>
        </div>
      </div>
      <div className="h-[1px] w-full bg-primary" />
      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="flex items-center gap-2 text-sm text-blackOne"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back
        </button>

        <button
          onClick={onNext}
          className={clsx(
            'bg-light-orangeTwo flex items-center gap-2 rounded-lg border border-primary px-6 py-2 font-medium text-blackOne transition-colors disabled:opacity-50'
          )}
        >
          Proceed
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>

      {/* File Upload Modal */}
      {uploadModalOpen && (
        <FileUploadModal
          isOpen={uploadModalOpen}
          onClose={() => setUploadModalOpen(false)}
          onFilesUpload={handleFilesUpload}
          title="Import your file"
        />
      )}
    </div>
  );
};

export default SuiteLevelSetup;

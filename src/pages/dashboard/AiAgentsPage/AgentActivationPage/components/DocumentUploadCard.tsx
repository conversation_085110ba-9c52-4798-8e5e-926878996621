import clsx from 'clsx';
import { Upload } from 'lucide-react';
import { PDFDocument } from 'pdf-lib';
import React, { useEffect, useState } from 'react';

interface DocumentUploadCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  uploadedFiles: File[];
  onUploadClick: () => void;
  onFileRemove: (fileIndex: number) => void;
}

const DocumentUploadCard: React.FC<DocumentUploadCardProps> = ({
  title,
  description,
  icon,
  uploadedFiles,
  onUploadClick,
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string): React.ReactNode => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#DC2626"
              stroke="#DC2626"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#DC2626"
              stroke="#DC2626"
              strokeWidth="1.5"
            />
            <path
              d="M9 13h6M9 17h6M9 9h1"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case 'doc':
      case 'docx':
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#2563EB"
              stroke="#2563EB"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#2563EB"
              stroke="#2563EB"
              strokeWidth="1.5"
            />
            <path
              d="M9 13h6M9 17h6M9 9h1"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case 'xls':
      case 'xlsx':
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#059669"
              stroke="#059669"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#059669"
              stroke="#059669"
              strokeWidth="1.5"
            />
            <path
              d="M9 13h6M9 17h6M9 9h1"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case 'ppt':
      case 'pptx':
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#DC2626"
              stroke="#DC2626"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#DC2626"
              stroke="#DC2626"
              strokeWidth="1.5"
            />
            <path
              d="M9 13h6M9 17h6M9 9h1"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case 'jpg':
      case 'jpeg':
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#7C3AED"
              stroke="#7C3AED"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#7C3AED"
              stroke="#7C3AED"
              strokeWidth="1.5"
            />
            <circle cx="10" cy="13" r="2" fill="white" />
            <path
              d="M20 17l-5-5L13 17"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case 'png':
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#7C3AED"
              stroke="#7C3AED"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#7C3AED"
              stroke="#7C3AED"
              strokeWidth="1.5"
            />
            <circle cx="10" cy="13" r="2" fill="white" />
            <path
              d="M20 17l-5-5L13 17"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case 'ai':
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#FF6B35"
              stroke="#FF6B35"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#FF6B35"
              stroke="#FF6B35"
              strokeWidth="1.5"
            />
            <path
              d="M9 13h6M9 17h6M9 9h1"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      default:
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
              fill="#6B7280"
              stroke="#6B7280"
              strokeWidth="1.5"
            />
            <polyline
              points="14,2 14,8 20,8"
              fill="#6B7280"
              stroke="#6B7280"
              strokeWidth="1.5"
            />
            <path
              d="M9 13h6M9 17h6M9 9h1"
              fill="none"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
    }
  };

  const getFileType = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'ppt':
      case 'pptx':
        return 'ppt';
      case 'xls':
      case 'xlsx':
        return 'xls';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'img';
      case 'ai':
        return 'ai';
      default:
        return 'file';
    }
  };

  const countPdfPages = async (file: File): Promise<number> => {
    if (getFileType(file.name) !== 'pdf') {
      return 0;
    }

    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdfDoc = await PDFDocument.load(arrayBuffer);
      return pdfDoc.getPageCount();
    } catch (error) {
      console.error('Error counting PDF pages:', error);
      // Fallback estimation based on file size
      const sizeInMB = file.size / (1024 * 1024);
      return Math.max(1, Math.round(sizeInMB * 10));
    }
  };

  const getFileInfo = async (file: File): Promise<string> => {
    const fileType = getFileType(file.name);
    const size = formatFileSize(file.size);

    if (fileType === 'pdf') {
      const pages = await countPdfPages(file);
      return `${pages} pages • ${size} • ${fileType}`;
    }

    return `${size} • ${fileType}`;
  };

  // Component to display file info with async loading
  const FileInfoDisplay: React.FC<{ file: File }> = ({ file }) => {
    const [fileInfo, setFileInfo] = useState<string>('');
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      const loadFileInfo = async () => {
        setLoading(true);
        const info = await getFileInfo(file);
        setFileInfo(info);
        setLoading(false);
      };
      loadFileInfo();
    }, [file]);

    if (loading) {
      return <span className="text-xs text-[#6B7280]">Loading...</span>;
    }

    return <span className="text-xs text-[#6B7280]">{fileInfo}</span>;
  };

  return (
    <div className="flex w-full flex-col items-center justify-between gap-4 rounded-xl border border-blackOne bg-[#363D8808] p-4 sm:w-[250px]">
      {/* Header */}
      <div className="flex items-center gap-4">
        <div className="flex h-[32px] w-[36px] flex-shrink-0 flex-col items-center justify-center rounded-xl border border-primary">
          {icon}
        </div>

        <h3 className="font-spartan text-lg font-semibold text-[#0F0006]">
          {title}
        </h3>
      </div>

      <p className="mt-auto text-sm leading-5 text-blackOne sm:text-base">
        {description}
      </p>
      <div className="mt-auto flex w-full flex-col gap-4">
        {/* Upload Button */}
        <button
          onClick={onUploadClick}
          className={clsx(
            'flex w-full items-center justify-center gap-2 rounded-full border border-blackOne bg-transparent px-4 py-3 text-sm font-medium text-blackOne transition-all hover:border-primary hover:bg-primary hover:text-white'
          )}
        >
          <Upload className="h-4 w-4" />
          <span className="text-sm font-medium">
            {uploadedFiles.length > 0 ? 'Replace Document' : 'Upload Document'}
          </span>
        </button>
        {/* Uploaded Files */}
        {uploadedFiles.length > 0 && (
          <div className="space-y-3">
            {uploadedFiles.map((file, index) => (
              <div key={index}>
                <div className="flex items-center justify-between rounded-xl bg-[#EAEAEA] p-2">
                  <div className="flex items-center gap-3">
                    <span className="text-base">{getFileIcon(file.name)}</span>
                    <div className="min-w-0 flex-1">
                      <p className="line-clamp-1 truncate text-xs font-medium text-[#46484B]">
                        {file.name}
                      </p>
                    </div>
                  </div>
                </div>
                <FileInfoDisplay file={file} />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentUploadCard;

import { AnimatePresence, motion } from 'framer-motion';
import { ChevronLeft } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { AgentCard } from '@/components/common/AgentCard';
import { AgentSuiteCard } from '@/components/common/AgentSuiteCard';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import { NotificationContainer } from '@/components/ui';
import AgentsDropdown, { DropdownOption } from '@/components/ui/AgentsDropdown';
import { useTenant } from '@/context/TenantContext';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useNotifications } from '@/hooks/useNotifications';
import { cn } from '@/lib/twMerge/cn';

import { Icons } from '../../../assets/icons/DashboardIcons';
import AgentSuiteSkeletonLoader from '../../../components/ui/AgentSuiteSkeleton';
import { ROUTES } from '../../../constants/routes';
import { useAuth } from '../../../context/AuthContext';
import { useGetAIAgentsData } from '../../../hooks/useAgents';
import { useHeartbeat } from '../../../hooks/useHeartbeat';
import { AIAgent } from '../../../types/agents';

type TabType = 'Agents Suites' | 'AI Agents';

const AIAgentsPage: React.FC = () => {
  const {
    claimedSuites,
    isUserLoading: isLoadingUserProfile,
    isAuthenticated,
  } = useAuth();
  const { activeAgent, setActiveAgent } = useTenant();
  const location = useLocation();
  const locationState = location.state as {
    selectedAgent?: string;
    userMessage?: string;
  } | null;

  const [activeTab, setActiveTab] = useState<TabType>('Agents Suites');

  // Use React Query hooks for data fetching
  const { agents, agentSuites, isLoadingAgents, isLoadingSuites, error } =
    useGetAIAgentsData();

  // Initialize notification system
  const { notifications, dismiss } = useNotifications();

  // Heartbeat functionality
  const {
    heartbeatState,
    fetchHeartbeats,
    initializeHeartbeat,
    pauseHeartbeat,
    getHeartbeatStatus,
  } = useHeartbeat();

  useEffect(() => {
    const switchAgent = async () => {
      if (locationState?.selectedAgent === 'regis') {
        try {
          await setActiveAgent(locationState.selectedAgent);
          // console.log(
          //   `Successfully switched to agent: ${locationState.selectedAgent}`
          // );
        } catch (error) {
          console.error('Failed to switch to regis agent:', error);
        }
      }
    };

    switchAgent();
  }, [locationState?.selectedAgent, setActiveAgent]);

  // Fetch heartbeat data when component mounts - only if user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchHeartbeats();
    }
  }, [fetchHeartbeats, isAuthenticated]);

  // Handle heartbeat actions
  const handleHeartbeatAction = async (
    action: 'initialize' | 'pause',
    agentKey: string
  ): Promise<void> => {
    try {
      if (action === 'initialize') {
        await initializeHeartbeat(agentKey);
      } else {
        await pauseHeartbeat(agentKey);
      }
    } catch (error) {
      // Error handling is done in the useHeartbeat hook
      console.error('Heartbeat action failed:', error);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const isMobile = useMediaQuery('(max-width: 768px)');

  const [isChatDrawerOpen, setIsChatDrawerOpen] = useState(false);
  const [currentSuiteKey, setCurrentSuiteKey] = useState<string | null>(null);
  const [currentAgentKey, setCurrentAgentKey] = useState<string | null>(null);
  const [isAgentSwitcherOpen, setIsAgentSwitcherOpen] = useState(false);

  const currentSuite =
    agentSuites?.find(suite => suite.agentSuiteKey === currentSuiteKey) || null;

  const suiteAgents: AIAgent[] = currentSuite
    ? agents.filter(agent => agent.agentSuiteKey === currentSuite.agentSuiteKey)
    : [];

  const currentAgent: AIAgent | undefined =
    agents.find(agent => agent.agentKey === currentAgentKey) || undefined;

  const agentOptions: DropdownOption[] = suiteAgents.map(agent => ({
    id: agent.agentKey,
    name: agent.agentName,
    icon: agent.avatar,
  }));

  const currentAgentOption: DropdownOption | undefined = currentAgent
    ? {
        id: currentAgent.agentKey,
        name: currentAgent.agentName,
        icon: currentAgent.avatar,
      }
    : undefined;

  const handleAgentSelect = async (agent: AIAgent) => {
    try {
      await setActiveAgent(agent.agentKey);
      // console.log(`Successfully switched to agent: ${agentKey}`);
    } catch (error) {
      console.error('Failed to switch agent:', error);
      return;
    }

    // On mobile, open the full-screen chat drawer for the selected agent
    if (isMobile) {
      setCurrentSuiteKey(agent.agentSuiteKey ?? null);
      setCurrentAgentKey(agent.agentKey);
      setIsChatDrawerOpen(true);
    }
  };

  const handleAgentSwitch = async (option: DropdownOption) => {
    const agent = suiteAgents.find(a => a.agentKey === option.id);
    if (!agent) return;
    await handleAgentSelect(agent);
  };

  // Close drawer automatically when switching to desktop layout
  useEffect(() => {
    if (!isMobile && isChatDrawerOpen) {
      setIsChatDrawerOpen(false);
    }
  }, [isMobile, isChatDrawerOpen]);

  const tabs: TabType[] = ['Agents Suites', 'AI Agents'];

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <div className="flex h-full w-full flex-col p-4 sm:p-8">
        {/* Header */}
        <div className="mb-8 w-full max-w-[606px] ">
          <div className="mb-6 hidden items-center gap-4 sm:flex">
            <Icons.Agent className="h-6 w-6 text-primary" />

            <h1 className="text-blackTree text-xl font-bold">Agents Hub</h1>
          </div>

          {/* Tabs */}
          <div className="flex space-x-2 border-b border-gray-200 sm:space-x-1">
            {tabs.map(tab => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={cn(
                  'px-2 py-2 font-spartan text-sm font-medium transition-colors sm:px-4',
                  activeTab === tab
                    ? 'border-b-2 border-primary text-primary'
                    : 'text-gray-600 hover:text-blackOne'
                )}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div
          className={cn(
            'w-full pb-8',
            activeTab === 'Agents Suites' ? 'max-w-[606px]' : 'max-w-[732px]'
          )}
        >
          {/* Notifications Container */}
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="z-50 mb-8"
            maxNotifications={3}
          />

          {error && (
            <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-700">
              <p>Error loading data: {error.message}</p>
            </div>
          )}

          {activeTab === 'Agents Suites' &&
            (isLoadingSuites ? (
              <AgentSuiteSkeletonLoader count={2} />
            ) : (
              <div className="grid w-full grid-cols-2 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-2">
                {agentSuites.map(suite => {
                  const isMatchingSuite = claimedSuites?.some(
                    claimedSuite =>
                      claimedSuite.suite.agentSuiteKey === suite.agentSuiteKey
                  );
                  return (
                    <AgentSuiteCard
                      key={suite.agentSuiteKey}
                      suite={suite}
                      link={ROUTES.DASHBOARD_AGENT_SUITE(suite.agentSuiteKey)}
                      isSuiteClaimed={isMatchingSuite || false}
                      showClaimButton={!isLoadingUserProfile}
                    />
                  );
                })}
              </div>
            ))}

          {activeTab === 'AI Agents' &&
            (isLoadingAgents ? (
              <AgentSuiteSkeletonLoader count={2} />
            ) : (
              <div className="grid w-full flex-1 grid-cols-1 gap-6 md:grid-cols-2">
                {agents.map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    agent={agent}
                    isActiveAgent={activeAgent === agent.agentKey}
                    link={ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(
                      agent.agentKey
                    )}
                    onAgentSelect={() => handleAgentSelect(agent)}
                    showHeartbeatControl
                    heartbeatStatus={getHeartbeatStatus(agent.agentKey)}
                    onHeartbeatAction={handleHeartbeatAction}
                    isHeartbeatLoading={
                      heartbeatState.loadingAgent === agent.agentKey
                    }
                  />
                ))}
              </div>
            ))}
        </div>
      </div>

      {/* Mobile Chat Drawer */}
      <AnimatePresence>
        {isMobile && isChatDrawerOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex flex-col bg-white md:hidden"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between border-b border-gray-200 p-3">
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => setIsChatDrawerOpen(false)}
                  className="flex items-center justify-center rounded-full text-primary"
                  aria-label="Close chat"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>

                <div className="flex flex-col items-start">
                  <span className="text-sm font-bold uppercase tracking-wide text-blackOne">
                    Agent Suite
                  </span>
                  <span className="hidden text-sm font-semibold text-blackOne">
                    {currentSuite?.agentSuiteName || 'Agent Suite'}
                  </span>
                </div>
              </div>
              <div className="flex items-center">
                {agentOptions.length > 0 && (
                  <AgentsDropdown
                    isOpen={isAgentSwitcherOpen}
                    onToggle={() =>
                      setIsAgentSwitcherOpen(previous => !previous)
                    }
                    currentItem={currentAgentOption}
                    options={agentOptions}
                    onItemSelect={option => {
                      setIsAgentSwitcherOpen(false);
                      void handleAgentSwitch(option);
                    }}
                    placeholder="Select agent"
                    noOptionsMessage="No agents available"
                  />
                )}
              </div>
            </div>

            {/* Chat Content */}
            <div className="flex min-h-0 flex-1 flex-col">
              <EnhancedChatSidebar
                reloadChatHistoryRef={reloadChatHistoryRef}
                className="h-full w-full px-0 py-0 md:hidden"
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </DashboardWithChatLayout>
  );
};

export default AIAgentsPage;

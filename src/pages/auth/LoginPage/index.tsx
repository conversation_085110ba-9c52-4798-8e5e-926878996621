import { useKeycloak } from '@react-keycloak/web';
import { useEffect } from 'react';

import { MainLoaderSkeleton } from '@/components/hocs/suspense/withSuspense';

export const PivotlLoginPage = () => {
  const { keycloak, initialized } = useKeycloak();

  useEffect(() => {
    if (initialized && keycloak) {
      if (keycloak.authenticated) {
        // If already authenticated, redirect to dashboard
        window.location.href = '/dashboard';
      } else {
        // If not authenticated, redirect to Keycloak login
        const redirectUri = window.location.origin + '/dashboard';
        keycloak.login({
          redirectUri,
        });
      }
    }
  }, [initialized, keycloak]);

  // Show loading spinner while redirecting
  return <MainLoaderSkeleton />;
};

export default PivotlLoginPage;

import { yupResolver } from '@hookform/resolvers/yup';
import { AxiosError } from 'axios';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import * as yup from 'yup';

import { Button, Input } from '@/components/ui';
import { useAuthEventContext } from '@/context/AuthEventContext';
import { useGetLinkForPasswordReset } from '@/hooks/useAuth';

const getLinkSchema = yup
  .object()
  .shape({
    email: yup.string().required('Please enter email'),
  })
  .required();

export default function GetLinkStepOneForm() {
  const {
    setActivateGetOtpHandler,
    setEmailHandler,
    setResetPasswordLevelHandler,
  } = useAuthEventContext();
  const { isPending: isFetching, mutateAsync: getLinkForPasswordReset } =
    useGetLinkForPasswordReset();
  const [errorMessage, setErrorMessage] = useState<string>('');

  const next = () => {
    setResetPasswordLevelHandler(2);
    setActivateGetOtpHandler(false);
  };

  const onError = () => {
    setActivateGetOtpHandler(false);
  };

  // Clear error message after 3 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  const {
    register,
    handleSubmit,
    formState: { isDirty, isValid },
  } = useForm<{ email: string }>({
    resolver: yupResolver(getLinkSchema),
  });

  const onSubmit: SubmitHandler<{ email: string }> = async data => {
    setActivateGetOtpHandler(true);
    setEmailHandler(data);
    setErrorMessage(''); // Clear any existing errors

    try {
      await getLinkForPasswordReset(data);
      next(); // Move to next step on success
    } catch (error) {
      onError();
      // Extract error message from axios error response
      const axiosError = error as AxiosError<{ message?: string }>;
      const message =
        axiosError.response?.data?.message ||
        'An error occurred while sending the recovery link';
      setErrorMessage(message);
    }
  };
  return (
    <motion.div
      exit={{ opacity: 0 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      className="flex-1 px-4 sm:px-0"
    >
      <div className="mx-auto mt-8 h-full w-full max-w-[457px] px-5 py-8 sm:py-20">
        <h2 className="mb-3 pt-12 text-center text-[24px] font-[500] leading-[47px] sm:pt-24 sm:text-[32px]">
          Reset Password
        </h2>
        <p className="pb-12 text-center text-[16px] leading-[24px]">
          We're here to help you regain access to your account. Simply follow
          these straightforward steps to reset your password:
        </p>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="mx-auto w-full max-w-[400px]"
        >
          <div className="mt-5">
            <div>
              <label
                htmlFor="email"
                className="mb-1 block text-sm font-medium text-gray-700"
              >
                Email Address
              </label>
              <Input
                className="h-10 w-full"
                placeholder="Email"
                autoComplete="email"
                id="email"
                {...register('email')}
              />
            </div>
            {errorMessage && (
              <p className="mt-2 text-center text-sm font-medium text-red-600">
                {errorMessage}
              </p>
            )}
            <Button
              type="submit"
              disabled={!isDirty || !isValid || isFetching}
              className="mx-auto mt-[32px] h-10 w-full 
              border border-primary   
              bg-primary text-white disabled:cursor-not-allowed disabled:bg-transparent disabled:text-primary"
            >
              {isFetching ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <p className={`text-sm font-medium sm:text-base`}>
                  Send Recovery Link
                </p>
              )}
            </Button>
          </div>
        </form>
      </div>
    </motion.div>
  );
}

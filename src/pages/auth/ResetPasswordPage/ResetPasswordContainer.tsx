import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { Link } from 'react-router-dom';

import { AgentousLogo } from '@/assets/images';
import bgImage from '@/assets/images/onboardingBgImage.png';
import { Button } from '@/components/ui';
import { useAuthEventContext } from '@/context/AuthEventContext';

type Props = {
  children: ReactNode;
  level: number;
};

export default function ResetPasswordContainer({ children, level }: Props) {
  const { resetPasswordLevel, setResetPasswordLevelHandler } =
    useAuthEventContext();
  return resetPasswordLevel === level ? (
    <motion.div
      exit={{ opacity: 0, transition: { duration: 0.2 } }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2, delay: 0.5, ease: 'easeIn' }}
      style={{ backgroundImage: `url(${bgImage})` }}
      className="relative mx-auto flex h-screen max-w-full justify-center overflow-hidden bg-cover"
    >
      <Link className="absolute left-[7%] top-[5%]" to="/">
        <div className="min-w-[80px] max-w-[117px]">
          <img loading="lazy" src={AgentousLogo} className={`h-full w-full`} />
        </div>
      </Link>
      {children}
      {level !== 1 && (
        <div className="absolute bottom-[10%] right-[7%]">
          <Button
            onClick={() => setResetPasswordLevelHandler(level - 1)}
            className="h-[20px] w-full max-w-[100px] bg-primary text-white disabled:bg-transparent disabled:text-primary"
          >
            <p className="text-[10px]">Back</p>
          </Button>
        </div>
      )}
    </motion.div>
  ) : null;
}

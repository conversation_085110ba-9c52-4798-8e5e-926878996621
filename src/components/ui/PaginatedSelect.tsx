import React, { forwardRef, ForwardRefRenderFunction } from 'react';
import { GroupBase } from 'react-select';
import { AsyncPaginate, LoadOptions } from 'react-select-async-paginate';

import { FetchOptionsResult, LocationOption } from '@/types/locations';
import { getDefaultSelectStyles } from '@/utils/reactSelectStyles';

interface Additional {
  page: number;
}

type SingleValue = LocationOption | null;
export type MultiValue = readonly LocationOption[];

interface PaginatedSelectProps<IsMulti extends boolean = false> {
  placeholder?: string;
  value?: IsMulti extends true ? MultiValue : SingleValue;
  onChange: (value: IsMulti extends true ? MultiValue : SingleValue) => void;
  onBlur?: () => void;
  name?: string;
  fetchOptions: (search: string, page: number) => Promise<FetchOptionsResult>;
  isMulti?: IsMulti;
  error?: string | undefined;
  label?: string;
  isDisabled?: boolean;
  className?: string;
}

const PaginatedSelect: ForwardRefRenderFunction<
  any,
  PaginatedSelectProps<boolean>
> = (
  {
    placeholder = 'Search for an option...',
    value,
    onChange,
    onBlur,
    name,
    fetchOptions,
    isMulti = false,
    error,
    isDisabled,
    label,
    className = '',
  },
  _ref
) => {
  const loadOptions: LoadOptions<
    LocationOption,
    GroupBase<LocationOption>,
    Additional
  > = async (search, loadedOptions, additional) => {
    const page = additional?.page ?? 1;
    try {
      const result = await fetchOptions(search, page);
      return {
        options: result.options,
        hasMore: result.hasMore,
        additional: {
          page: page + 1,
        },
      };
    } catch (error) {
      // console.error('Error loading options:', error);
      return {
        options: [],
        hasMore: false,
        additional: {
          page: page,
        },
      };
    }
  };

  return (
    <div className={className}>
      {label && (
        <label
          htmlFor={name}
          className="mb-2 block text-xs font-normal text-grayTen"
        >
          {label}
        </label>
      )}
      <AsyncPaginate<
        LocationOption,
        GroupBase<LocationOption>,
        Additional,
        boolean
      >
        value={value}
        loadOptions={loadOptions}
        onChange={onChange}
        onBlur={onBlur}
        additional={{
          page: 1,
        }}
        debounceTimeout={300}
        placeholder={placeholder}
        isMulti={isMulti}
        name={name}
        id={name}
        isDisabled={isDisabled}
        styles={getDefaultSelectStyles()}
        components={{
          // Option: PaginatedCustomOption,
          IndicatorSeparator: () => null,
        }}
        className={`h-12 w-full ${!error ? 'border-gray-300 focus:border-primary focus:ring-primary' : 'border-red-400 text-red-500 focus:border-red-500 focus:ring-red-600'}`}
        classNamePrefix="react-select"
        isSearchable
        isClearable
      />
      {error && typeof error === 'string' && (
        <p className="mt-1 text-xs text-red-500">{error}</p>
      )}
    </div>
  );
};

const ForwardedPaginatedSelect = forwardRef(PaginatedSelect);

ForwardedPaginatedSelect.displayName = 'PaginatedSelect';

export default ForwardedPaginatedSelect as <IsMulti extends boolean = false>(
  props: PaginatedSelectProps<IsMulti> & React.RefAttributes<any>
) => React.ReactElement;

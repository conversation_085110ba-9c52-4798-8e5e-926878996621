import clsx from 'clsx';
import { ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';

export interface Column<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: T[keyof T], row: T) => React.ReactNode;
  className?: string;
  wrap?: boolean;
}

export interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  className?: string;
  onRowClick?: (row: T) => void;
  sortable?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  rowColoring?: boolean;
  rowColoringType?: 'even' | 'odd';
  rowColoringColorClass?: string;
  // Selection props
  selectable?: boolean;
  selectedRows?: Set<string>;
  onRowSelect?: (rowId: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  getRowId?: (row: T) => string;
  showCheckAll?: boolean;
}

type SortDirection = 'asc' | 'desc' | null;

function DataTable<T extends Record<string, unknown>>({
  data,
  columns,
  className = '',
  onRowClick,
  sortable = false,
  loading = false,
  emptyMessage = 'No data available',
  rowColoring = true,
  rowColoringType = 'odd',
  rowColoringColorClass = 'bg-[#FFF1EB]',
  // Selection props
  selectable = false,
  selectedRows = new Set(),
  onRowSelect,
  onSelectAll,
  getRowId,
  showCheckAll = false,
}: DataTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const resolveRowId = (row: T): string => {
    if (getRowId) {
      return getRowId(row);
    }

    const potentialId = (row as { id?: unknown }).id;
    if (typeof potentialId === 'string' || typeof potentialId === 'number') {
      return String(potentialId);
    }

    const potentialKey = (row as { key?: unknown }).key;
    if (typeof potentialKey === 'string' || typeof potentialKey === 'number') {
      return String(potentialKey);
    }

    return Math.random().toString(36).slice(2);
  };

  const handleSort = (columnKey: keyof T) => {
    if (!sortable) return;

    const column = columns.find(col => col.key === columnKey);
    if (!column?.sortable) return;

    if (sortColumn === columnKey) {
      // Cycle through: asc -> desc -> null
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortDirection(null);
        setSortColumn(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  const getSortedData = () => {
    if (!sortColumn || !sortDirection) return data;

    return [...data].sort((a, b) => {
      const aVal = a[sortColumn];
      const bVal = b[sortColumn];

      if (aVal === null || aVal === undefined) return 1;
      if (bVal === null || bVal === undefined) return -1;

      let comparison = 0;
      if (aVal < bVal) comparison = -1;
      if (aVal > bVal) comparison = 1;

      return sortDirection === 'desc' ? -comparison : comparison;
    });
  };

  const renderSortIcon = (columnKey: keyof T) => {
    const column = columns.find(col => col.key === columnKey);
    if (!sortable || !column?.sortable) return null;

    if (sortColumn === columnKey) {
      return sortDirection === 'asc' ? (
        <ChevronUp className="h-4 w-4" />
      ) : (
        <ChevronDown className="h-4 w-4" />
      );
    }

    return <ChevronDown className="h-4 w-4 text-gray-300" />;
  };

  const sortedData = getSortedData();

  // Selection logic
  const isAllSelected =
    selectable &&
    sortedData.length > 0 &&
    sortedData.every(row => selectedRows.has(resolveRowId(row)));

  const isSomeSelected =
    selectable && sortedData.some(row => selectedRows.has(resolveRowId(row)));

  const handleSelectAll = () => {
    if (onSelectAll) {
      onSelectAll(!isAllSelected);
    }
  };

  const handleRowSelect = (
    row: T,
    event?: React.MouseEvent | React.ChangeEvent<HTMLInputElement>
  ) => {
    if (event) {
      event.stopPropagation();
    }
    if (onRowSelect) {
      const rowId = resolveRowId(row);
      onRowSelect(rowId, !selectedRows.has(rowId));
    }
  };

  if (loading) {
    return (
      <div
        className={clsx('overflow-hidden sm:rounded-xl sm:bg-white', className)}
      >
        <div className="overflow-x-auto">
          <table className="min-w-full">
            {/* Header */}
            <thead>
              <tr>
                {selectable && (
                  <th className="w-12 px-4 py-5">
                    {/* Loading state - no checkbox */}
                  </th>
                )}
                {columns.map(column => (
                  <th
                    key={String(column.key)}
                    className={clsx(
                      'px-4 py-5 text-left text-xs font-medium capitalize tracking-wider text-grayTen sm:text-sm sm:font-semibold',
                      column.className
                    )}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.label}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            {/* Body with loading animation */}
            <tbody className="bg-white">
              {Array.from({ length: 10 }).map((_, index) => (
                <tr key={index}>
                  {selectable && (
                    <td className="px-4 py-5">
                      <div className="h-4 w-4 rounded-full bg-gray-100"></div>
                    </td>
                  )}
                  {[...Array(columns.length)].map((_, colIndex) => (
                    <td key={colIndex} className="px-4 py-5">
                      <div className="h-4 rounded-full bg-gray-100"></div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('overflow-hidden rounded-xl bg-white', className)}>
      <div className="overflow-x-auto pb-5">
        <table className="min-w-full">
          {/* Header */}
          <thead>
            <tr>
              {selectable && showCheckAll ? (
                <th className="w-12 px-4 py-5">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    ref={input => {
                      if (input)
                        input.indeterminate = isSomeSelected && !isAllSelected;
                    }}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </th>
              ) : (
                selectable && !showCheckAll && <th className="w-12 px-4 py-5" />
              )}
              {columns.map((column, index) => (
                <th
                  key={String(column.key) + index}
                  onClick={() => handleSort(column.key)}
                  className={clsx(
                    'no-break whitespace-nowrap px-4 py-5  text-left text-xs font-medium capitalize tracking-wider text-grayTen sm:text-sm',
                    sortable &&
                      column.sortable &&
                      'cursor-pointer hover:bg-gray-100',
                    column.className
                  )}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {renderSortIcon(column.key)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>

          {/* Body */}
          <tbody className="bg-white">
            {sortedData.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (selectable ? 1 : 0)}
                  className="px-6 py-8 text-center text-subText"
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              sortedData.map((row, index) => {
                const rowId = resolveRowId(row);
                const isSelected = selectedRows.has(rowId);

                return (
                  <tr
                    key={rowId}
                    onClick={() => onRowClick?.(row)}
                    className={clsx(
                      'border-white transition-all duration-200 ease-in-out hover:border hover:border-primary/70',
                      onRowClick && 'cursor-pointer',
                      isSelected && 'border-blue-200 bg-blue-50',
                      rowColoring &&
                        !isSelected &&
                        (rowColoringType === 'even'
                          ? index % 2 !== 0
                          : index % 2 === 0) &&
                        rowColoringColorClass
                    )}
                  >
                    {selectable && (
                      <td className="px-2 py-2 sm:px-4 sm:py-4">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={event => handleRowSelect(row, event)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </td>
                    )}
                    {columns.map(column => (
                      <td
                        key={String(column.key)}
                        className={clsx(
                          column.wrap
                            ? 'whitespace-normal'
                            : 'whitespace-nowrap',
                          'p-4 text-sm text-subText',
                          column.className
                        )}
                      >
                        {column.render
                          ? column.render(row[column.key], row)
                          : String(row[column.key] || '')}
                      </td>
                    ))}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default DataTable;

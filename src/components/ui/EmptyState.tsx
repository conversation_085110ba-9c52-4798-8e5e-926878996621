import React from 'react';

import { insightsEmptyStateBg } from '@/assets/images';

interface EmptyStateProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  className = '',
}) => {
  return (
    <div
      className={`relative flex h-[250px] w-full items-center justify-center overflow-hidden rounded-xl p-4 sm:h-[295px] sm:p-12 ${className}`}
      style={{
        backgroundImage: `url(${insightsEmptyStateBg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      {/* Dark overlay */}
      <div
        className="absolute inset-0"
        style={{
          backgroundColor: '#000000B2',
        }}
      />

      {/* Content */}
      <div className="relative z-10 flex flex-col items-start gap-6 sm:items-center">
        {/* Icon with background */}
        <div className="flex h-12 w-12 items-center justify-center rounded-2xl bg-warning sm:h-16 sm:w-16">
          {icon}
        </div>

        {/* Text content */}
        <div className="flex max-w-[456px] flex-col items-start gap-3 sm:items-center">
          <h3 className="text-left text-base font-semibold leading-[100%] text-white sm:text-center">
            {title}
          </h3>
          <p className="text-left text-base font-normal leading-[24px] text-white sm:text-center">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;

import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import React, { useCallback } from 'react';

import { cn } from '@/lib/twMerge/cn';
import { NotificationToastProps } from '@/types/notifications';

/**
 * Individual notification toast component
 */
const NotificationToast = React.forwardRef<
  HTMLDivElement,
  NotificationToastProps
>(({ notification, onClose, className, size = 'large' }, ref) => {
  // Use the agent information that was captured when the notification was created
  // This ensures the agent info remains consistent even if activeAgent changes
  const agentToUse = notification.activeAgent || {
    agentName: 'Agent',
    avatar: '', // This should not happen since we now always capture agent info
  };

  // Handle close button click
  const handleClose = useCallback(() => {
    onClose(notification.id);
  }, [notification.id, onClose]);

  // Handle keyboard navigation for close button
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        handleClose();
      }
    },
    [handleClose]
  );

  // Get styles based on notification type
  const getNotificationStyles = () => {
    const type = notification.type || 'info';

    switch (type) {
      case 'success':
        return {
          container: 'bg-[#EDF7ED] border-[#3E8E58]',
          inner: 'bg-[#FBFFFB]',
        };
      case 'error':
        return {
          container: 'bg-peach-5 border-primary',
          inner: 'bg-white',
        };
      case 'info':
      default:
        return {
          container: 'bg-peach-5 border-primary',
          inner: 'bg-white',
        };
    }
  };

  const styles = getNotificationStyles();

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: -20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{
        duration: 0.3,
        ease: [0.4, 0.0, 0.2, 1],
      }}
      className={cn(
        // Base container styles
        'flex items-center gap-3 rounded-lg border p-2 shadow-sm sm:p-4',
        styles.container,
        // Hover and focus states
        'transition-shadow duration-200 hover:shadow-md',
        // Responsive spacing
        'min-h-[60px] w-full',
        size === 'small' && 'p-2',
        className
      )}
      role="alert"
      aria-live="polite"
      aria-label={`Notification from ${agentToUse.agentName}: ${typeof notification.message === 'string' ? notification.message : 'Notification message'}`}
    >
      <div
        className={cn(
          'flex w-full justify-between rounded py-2',
          styles.inner,
          size === 'large' && 'items-center px-2'
        )}
      >
        <div
          className={`flex w-[95%] items-center gap-3 ${size === 'small' ? 'flex-col' : 'flex-row'}`}
        >
          <div className="flex items-center gap-2">
            {/* Agent Avatar */}
            <div className="h-8 w-8 flex-shrink-0 overflow-hidden rounded-full bg-peachTwo sm:h-10 sm:w-10">
              <img
                src={agentToUse.avatar}
                alt={`${agentToUse.agentName} avatar`}
                className="h-full w-full object-cover"
                onError={e => {
                  console.warn(
                    `[NotificationToast] Failed to load avatar for ${agentToUse.agentName}`
                  );
                  // Fallback to a default background color if image fails
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>

            {/* Agent Name */}
            <span className="flex-shrink-0 text-sm font-medium text-blackOne">
              {agentToUse.agentName}
            </span>
          </div>

          {/* Message */}
          <div
            className={`${notification.additionalImage ? 'w-[70%]' : 'w-full'} truncate break-words text-xs text-gray-700 sm:text-sm`}
          >
            {notification.message}
          </div>

          {/* Optional Additional Image */}
          {notification.additionalImage && (
            <div className="flex-shrink-0">
              <img
                src={notification.additionalImage}
                alt="Additional notification image"
                className={cn(
                  // Default styling for additional image
                  'h-8 w-8 object-contain',
                  // Apply custom classes if provided
                  notification.additionalImageClassname
                )}
                onError={e => {
                  console.warn(
                    `[NotificationToast] Failed to load additional image:`,
                    notification.additionalImage
                  );
                  // Hide the image if it fails to load
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          )}
        </div>

        {/* Close Button */}
        <div className="flex-shrink-0">
          <button
            onClick={handleClose}
            onKeyDown={handleKeyDown}
            className={cn(
              // Base button styles
              'flex h-6 w-6 items-center justify-center rounded-full',
              // Interactive states
              'hover:bg-gray-100 focus:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1',
              // Transition
              'transition-colors duration-150'
            )}
            aria-label={`Close notification from ${agentToUse.agentName}`}
            title="Close notification"
            type="button"
          >
            <X
              className="h-4 w-4 text-gray-500 hover:text-gray-700"
              strokeWidth={2}
            />
          </button>
        </div>
      </div>
    </motion.div>
  );
});

// Add display name for better debugging
NotificationToast.displayName = 'NotificationToast';

export default NotificationToast;

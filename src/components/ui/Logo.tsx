import { Link } from 'react-router-dom';

import { AgentousIcon, AgentousLogo } from '../../assets/images';
import { ROUTES } from '../../constants/routes';

const Logo = ({ iconOnly = false }) => {
  return (
    <Link to={ROUTES.HOME}>
      <div className="flex items-center justify-start sm:space-x-1">
        {iconOnly ? (
          <img
            className="h-auto max-w-[120px] sm:max-w-[150px]"
            loading="lazy"
            src={AgentousIcon}
            alt="Agentous Logo"
          />
        ) : (
          <>
            <img
              className="h-[20.141286849975586px] w-[119.68974304199219px] max-w-[120px] sm:h-auto sm:w-[150px]"
              loading="lazy"
              src={AgentousLogo}
              alt="Agentous Logo"
            />

            <div className="-mt-[0.75px] flex h-[13px] w-[23px] items-center justify-center rounded border border-grayNine p-px pt-px font-spartan text-[7.32px] font-medium text-grayNine sm:h-[21px] sm:w-[38px] sm:text-xs">
              Beta
            </div>
          </>
        )}
      </div>
    </Link>
  );
};

export default Logo;

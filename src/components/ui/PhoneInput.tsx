import CountryList from 'country-list-with-dial-code-and-flag';
import { ChevronDown, Search } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { useOnClickOutside } from '@/hooks/useOnClickOutside';

interface Country {
  name: string;
  code: string;
  dial_code: string;
  flag: string;
}

interface PhoneInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  countryCode?: string;
  onChange?: (value: string, countryCode: string, dialCode: string) => void;
  error?: string;
  className?: string;
  disabled?: boolean;
}

/**
 * PhoneInput Component
 * A reusable phone input with country code selector
 * Uses country-list-with-dial-code-and-flag for country data
 * Features SVG flags instead of emojis
 */
export const PhoneInput: React.FC<PhoneInputProps> = ({
  label,
  placeholder = 'Enter phone number',
  value = '',
  countryCode = 'US',
  onChange,
  error,
  className = '',
  disabled = false,
}) => {
  // Get all countries from the library and deduplicate by country code
  const allCountries: Country[] = React.useMemo(() => {
    const countries = CountryList.getAll();
    const uniqueCountries = new Map<string, Country>();

    countries.forEach(country => {
      // Only keep the first occurrence of each country code
      if (!uniqueCountries.has(country.code)) {
        uniqueCountries.set(country.code, country);
      }
    });

    return Array.from(uniqueCountries.values()).sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  }, []);

  // Find initial country
  const findCountryByCode = useCallback(
    (code: string): Country => {
      return (
        allCountries.find(c => c.code.toLowerCase() === code.toLowerCase()) ||
        allCountries[0]
      );
    },
    [allCountries]
  );

  const [selectedCountry, setSelectedCountry] = useState<Country>(
    findCountryByCode(countryCode)
  );
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    placement: 'bottom' as 'top' | 'bottom',
  });

  useOnClickOutside(dropdownRef, () => setIsOpen(false));

  // Calculate dropdown position dynamically based on available space
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const dropdownHeight = 400; // Max height of dropdown (search + list)
      const dropdownWidth = 320;
      const gap = 4; // Gap between button and dropdown

      // Calculate available space
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;

      // Determine placement based on available space
      const shouldRenderAbove =
        spaceBelow < dropdownHeight && spaceAbove > spaceBelow;

      // Calculate position
      let top: number;
      if (shouldRenderAbove) {
        // Render above the button
        top = rect.top + window.scrollY - dropdownHeight - gap;
        // Ensure it doesn't go above viewport
        if (top < window.scrollY) {
          top = window.scrollY + gap;
        }
      } else {
        // Render below the button
        top = rect.bottom + window.scrollY + gap;
        // Ensure it doesn't go below viewport
        const maxTop =
          window.innerHeight + window.scrollY - dropdownHeight - gap;
        if (top > maxTop) {
          top = maxTop;
        }
      }

      // Calculate left position
      let left = rect.left + window.scrollX;
      // Ensure dropdown doesn't overflow right edge
      if (left + dropdownWidth > window.innerWidth) {
        left = window.innerWidth - dropdownWidth - gap;
      }
      // Ensure dropdown doesn't overflow left edge
      if (left < 0) {
        left = gap;
      }

      setDropdownPosition({
        top,
        left,
        width: dropdownWidth,
        placement: shouldRenderAbove ? 'top' : 'bottom',
      });
    }
  }, [isOpen]);

  // Filter countries based on search query
  const filteredCountries = React.useMemo(() => {
    if (!searchQuery.trim()) return allCountries;

    const query = searchQuery.toLowerCase().trim();
    return allCountries.filter(country => {
      const nameMatch = country.name.toLowerCase().includes(query);
      const codeMatch = country.code.toLowerCase().includes(query);
      const dialCodeMatch =
        country.dial_code.includes(query) ||
        country.dial_code.replace('+', '').includes(query);
      return nameMatch || codeMatch || dialCodeMatch;
    });
  }, [allCountries, searchQuery]);

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setIsOpen(false);
    setSearchQuery('');
    onChange?.(value, country.code, country.dial_code);
    inputRef.current?.focus();
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const phoneValue = e.target.value.replace(/[^\d]/g, ''); // Only allow digits
    onChange?.(phoneValue, selectedCountry.code, selectedCountry.dial_code);
  };

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Update selected country when countryCode prop changes
  useEffect(() => {
    if (countryCode) {
      const country = findCountryByCode(countryCode);
      if (country.code !== selectedCountry.code) {
        setSelectedCountry(country);
      }
    }
  }, [countryCode, findCountryByCode, selectedCountry.code]);

  return (
    <div className={`w-full ${className}`}>
      {label && (
        <label className="mb-2 block text-xs font-normal text-grayTen">
          {label}
        </label>
      )}
      <div
        className={`flex items-center overflow-hidden rounded-lg border transition-colors ${
          error
            ? 'border-red-500 focus-within:ring-1 focus-within:ring-red-500'
            : 'border-gray-300 focus-within:border-primary focus-within:ring-2 focus-within:ring-primary'
        } ${disabled ? 'cursor-not-allowed bg-gray-50' : 'bg-white'}`}
      >
        {/* Country Selector */}
        <div className="relative">
          <button
            ref={buttonRef}
            type="button"
            onClick={() => !disabled && setIsOpen(!isOpen)}
            disabled={disabled}
            className={`flex h-[42px] items-center gap-2 px-3 transition-colors hover:bg-gray-100 ${
              disabled ? 'cursor-not-allowed opacity-50' : ''
            }`}
          >
            <span className="text-xl">{selectedCountry.flag}</span>
            <span className="hidden text-xs font-medium text-[#1A1A1A]">
              {selectedCountry.dial_code}
            </span>
            <ChevronDown
              className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${
                isOpen ? 'rotate-180' : ''
              }`}
            />
          </button>

          {/* Dropdown Menu - Rendered via Portal */}
          {isOpen &&
            createPortal(
              <div
                ref={dropdownRef}
                className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-xl"
                style={{
                  position: 'fixed',
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  width: `${dropdownPosition.width}px`,
                  zIndex: 9999,
                  maxHeight: '400px',
                }}
              >
                {/* Search Input */}
                <div className="sticky top-0 border-b border-gray-200 bg-white p-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Search countries..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      className="w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    />
                  </div>
                </div>

                {/* Countries List */}
                <div className="max-h-[300px] overflow-y-auto">
                  {filteredCountries.length > 0 ? (
                    filteredCountries.map(country => (
                      <button
                        key={country.code}
                        type="button"
                        onClick={() => handleCountrySelect(country)}
                        className={`flex w-full items-center gap-3 border-b border-gray-100 px-4 py-3 text-left text-sm transition-colors last:border-b-0 hover:bg-gray-50 ${
                          selectedCountry.code === country.code
                            ? 'bg-primary/10 font-medium text-primary'
                            : 'text-[#1A1A1A]'
                        }`}
                      >
                        <span className="text-2xl">{country.flag}</span>
                        <div className="flex-1">
                          <div className="font-medium">{country.name}</div>
                          <div className="text-xs text-gray-500">
                            {country.dial_code}
                          </div>
                        </div>
                        <span className="font-mono text-xs text-gray-400">
                          {country.code}
                        </span>
                      </button>
                    ))
                  ) : (
                    <div className="px-4 py-8 text-center text-sm text-gray-500">
                      No countries found
                    </div>
                  )}
                </div>
              </div>,
              document.body
            )}
        </div>

        {/* Phone Number Input */}
        <input
          ref={inputRef}
          type="tel"
          placeholder={placeholder}
          value={value}
          onChange={handlePhoneChange}
          disabled={disabled}
          className={`flex-1 border-0 py-3 pl-0 pr-4 text-sm text-[#1A1A1A] placeholder:text-gray-400 focus:outline-none ${
            disabled ? 'cursor-not-allowed bg-gray-50' : 'bg-white'
          }`}
        />
      </div>

      {/* Error Message */}
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};

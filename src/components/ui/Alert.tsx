import { ChevronLeft, X } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { cn } from '@/lib/twMerge/cn';

interface AgentInfo {
  name: string;
  avatar?: string;
}

type PositionType = 'relative' | 'absolute' | 'fixed';
type ClearMode = 'manual' | 'auto';

interface AlertProps {
  message: string;
  type?: 'error' | 'success' | 'warning' | 'info';
  onClose: () => void;
  onBack?: () => void;
  agent?: AgentInfo;
  showIcon?: boolean;
  className?: string;
  position?: PositionType;
  clearMode?: ClearMode;
  autoCloseSeconds?: number;
  // Optional key to force new alert even if message text is identical
  triggerKey?: number | string;
}

type AlertItemData = {
  id: string;
  message: string;
  type?: 'error' | 'success' | 'warning' | 'info';
};

const Alert: React.FC<AlertProps> = ({
  message,
  onClose,
  onBack,
  agent,
  className,
  position = 'relative',
  clearMode = 'auto',
  autoCloseSeconds = 4,
  type = 'info',
  triggerKey,
}) => {
  const [alerts, setAlerts] = useState<AlertItemData[]>([]);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const lastPropMessageRef = useRef<string | null>(null);
  const lastTriggerKeyRef = useRef<number | string | undefined>(undefined);
  const prevLengthRef = useRef<number>(0);
  const latestTypeRef = useRef<typeof type>(type);

  // Keep a ref of latest type to avoid double-push when type updates separately
  useEffect(() => {
    latestTypeRef.current = type;
  }, [type]);

  // Push new message into the local stack (max 2, drop oldest)
  useEffect(() => {
    if (!message) return;
    // Push when message text changes or a new triggerKey is provided
    const hasTextChanged = message !== lastPropMessageRef.current;
    const hasTriggerChanged =
      triggerKey !== undefined && triggerKey !== lastTriggerKeyRef.current;
    if (hasTextChanged || hasTriggerChanged) {
      // Deduplicate across React StrictMode double-mount by using a global key
      const dedupKey = `${message}|${type}|${String(triggerKey ?? '')}`;
      const w = typeof window !== 'undefined' ? (window as any) : undefined;
      const now = Date.now();
      if (
        w?.__AG_ALERT_DEDUP?.key === dedupKey &&
        now - w.__AG_ALERT_DEDUP.ts < 1000
      ) {
        return; // skip duplicate immediate push
      }
      if (w) {
        w.__AG_ALERT_DEDUP = { key: dedupKey, ts: now };
      }
      lastPropMessageRef.current = message;
      if (triggerKey !== undefined) {
        lastTriggerKeyRef.current = triggerKey;
      }
      const newItem: AlertItemData = {
        id: `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`,
        message,
        type,
      };
      setAlerts(prev => {
        // If this is not an explicit re-trigger, avoid accidental duplicates
        if (
          !hasTriggerChanged &&
          prev.some(a => a.message === message && a.type === type)
        ) {
          return prev;
        }
        const next = [newItem, ...prev];
        return next.slice(0, 2);
      });
    }
  }, [message, triggerKey, type]);

  // When all alerts are dismissed, ask parent to clear its state
  useEffect(() => {
    if (prevLengthRef.current > 0 && alerts.length === 0) {
      onClose();
    }
    prevLengthRef.current = alerts.length;
  }, [alerts.length, onClose]);

  // Scroll to top if newest success/error alert is out of view
  useEffect(() => {
    if (alerts.length === 0) return;
    if (position === 'fixed') return;
    const newest = alerts[0];
    if (newest.type === 'success' || newest.type === 'error') {
      const rect = containerRef.current?.getBoundingClientRect();
      if (!rect) return;
      const isInView = rect.top >= 0 && rect.top <= window.innerHeight;
      if (!isInView) {
        // Try to scroll the alert into view within the nearest scroll container
        try {
          containerRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        } catch (e) {
          // Fallback
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
    }
  }, [alerts, position]);

  const getPositionStyles = useCallback(() => {
    switch (position) {
      case 'absolute':
        return 'absolute top-0 left-0 right-0 z-50';
      case 'fixed':
        return 'fixed top-4 left-4 right-4 z-50';
      case 'relative':
      default:
        return 'relative';
    }
  }, [position]);

  return (
    <div ref={containerRef} className={cn(getPositionStyles(), className)}>
      <div className="flex max-w-[732px] flex-col">
        {alerts.map(item => (
          <div key={item.id} className="mb-4">
            <AlertItem
              data={item}
              onDismiss={() =>
                setAlerts(prev => prev.filter(a => a.id !== item.id))
              }
              onBack={onBack}
              agent={agent}
              clearMode={clearMode}
              autoCloseSeconds={autoCloseSeconds}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

interface AlertItemProps {
  data: AlertItemData;
  onDismiss: () => void;
  onBack?: () => void;
  agent?: AgentInfo;
  clearMode: ClearMode;
  autoCloseSeconds: number;
}

const AlertItem: React.FC<AlertItemProps> = ({
  data,
  onDismiss,
  onBack,
  agent,
  clearMode,
  autoCloseSeconds,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto close per-item
  useEffect(() => {
    if (clearMode === 'auto') {
      timeoutRef.current = setTimeout(() => {
        onDismiss();
      }, autoCloseSeconds * 1000);
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [clearMode, autoCloseSeconds, onDismiss]);

  // Pause auto close on hover
  useEffect(() => {
    if (clearMode === 'auto') {
      if (isHovered && timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      } else if (!isHovered && !timeoutRef.current) {
        timeoutRef.current = setTimeout(() => {
          onDismiss();
        }, autoCloseSeconds * 1000);
      }
    }
  }, [isHovered, clearMode, autoCloseSeconds, onDismiss]);

  const truncatedMessage =
    data.message.length > 100
      ? `${data.message.slice(0, 100)}...`
      : data.message;

  return (
    <div
      className={cn(
        'flex h-[80px] animate-slide-down items-center gap-4 rounded border-[0.5px] border-primary bg-[#FFECE3] p-4'
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex min-h-[48px] flex-1 items-center gap-2 rounded-md bg-[#FFFAF7] px-2 py-2 sm:gap-3 sm:px-4">
        {onBack && (
          <button
            onClick={onBack}
            className="flex-shrink-0 rounded p-1 text-[#FF3E00] transition-colors hover:bg-black/5"
            aria-label="Go back"
          >
            <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4" />
          </button>
        )}

        {agent && (
          <div className="flex flex-shrink-0 items-center gap-2">
            {agent.avatar ? (
              <img
                src={agent.avatar}
                alt={agent.name}
                className="h-8 w-8 rounded-full bg-[#F5E7DECC] object-cover"
              />
            ) : (
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-300">
                <span className="text-sm font-medium text-[#1E293B]">
                  {agent.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            <span className="text-sm font-bold text-[#1E293B] sm:text-base">
              {agent.name}
            </span>
          </div>
        )}

        <span className="flex-1 truncate text-xs font-normal text-[#1E293B] sm:text-sm">
          {truncatedMessage}
        </span>

        <button
          onClick={onDismiss}
          className="flex-shrink-0 rounded p-1 transition-colors hover:bg-black/5"
          aria-label="Close alert"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default Alert;

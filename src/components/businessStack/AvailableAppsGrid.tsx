import { Search } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';
import { Spinner } from '@/components/common/Loader';
import { But<PERSON>, NotificationContainer } from '@/components/ui';
import { useAppCategoriesQuery } from '@/hooks/useBusinessStackQueries';
import { useDebounce } from '@/hooks/useDebounce';
import { useNotifications } from '@/hooks/useNotifications';

import { AvailableAppsGridProps } from '../../types/businessStack';
import { AppCard } from './AppCard';

export const AvailableAppsGrid: React.FC<AvailableAppsGridProps> = ({
  apps,
  isLoading,
  searchQuery,
  selectedCategory,
  onSearchChange,
  onCategoryChange,
  onConnectApp,
  connectionFlow,
  onOpenTwilioModal,
  onOpenSalesforceModal,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  const { notifications, dismiss } = useNotifications();

  // Use React Query to fetch categories with caching
  const { data: categoriesData, isLoading: isLoadingCategories } =
    useAppCategoriesQuery();

  const categories = categoriesData?.data || [];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showCategoryDropdown && !target.closest('.category-dropdown')) {
        setShowCategoryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showCategoryDropdown]);

  const debouncedSearchQuery = useDebounce(localSearchQuery, 1000);

  // Sync local state when parent searchQuery changes (e.g., when parent resets search)
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // Trigger API call when debounced value changes
  useEffect(() => {
    // Only call onSearchChange if the debounced value is different from current searchQuery
    if (debouncedSearchQuery !== searchQuery) {
      onSearchChange(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, onSearchChange, searchQuery]);

  // Handle input change - update local state immediately for responsive UI
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };

  // Handle category selection
  const handleCategorySelect = (categoryName: string) => {
    onCategoryChange(categoryName);
    setShowCategoryDropdown(false);
  };

  // Get selected category display name
  const getSelectedCategoryDisplay = () => {
    if (!selectedCategory) return 'All Categories';
    const category = categories.find(
      cat => cat.categoryName === selectedCategory
    );
    return category ? category.categoryAlias : 'All Categories';
  };

  // Sort apps alphabetically and filter based on search query
  const filteredApps = apps
    .sort((a, b) => a.name.localeCompare(b.name))
    .filter(app => app.name.toLowerCase().includes(searchQuery.toLowerCase()));

  return (
    <div className="flex h-full flex-col">
      <div className="mb-6 flex justify-between gap-4">
        {/* Search Bar */}
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
          <input
            type="text"
            placeholder="Search"
            value={localSearchQuery}
            onChange={handleSearchInputChange}
            className="w-full rounded-lg border border-gray-300 py-3 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
        </div>

        {/* Category Filter */}
        <div className="category-dropdown relative">
          <Button
            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            className="flex h-[46px] min-w-[120px] items-center gap-2 rounded-[10px] border border-primary bg-[#FDF7F6] p-4 text-base text-primary transition-colors hover:bg-primary hover:text-white"
          >
            {/* <span className="truncate">{getSelectedCategoryDisplay()}</span> */}
            {/* <ChevronDown
              className={`h-4 w-4 transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`}
            /> */}
            Filter <Icons.DownloadCloud className="h-4 w-4" />
          </Button>

          {showCategoryDropdown && (
            <div className="absolute right-0 top-full z-10 mt-1 w-[185px] rounded-lg border border-gray-200 bg-white shadow-lg">
              <div className="max-h-60 divide-y divide-gray-200 overflow-y-auto">
                <button
                  onClick={() => handleCategorySelect('')}
                  className={`w-full rounded-t-lg p-3 text-left text-sm hover:bg-gray-50 ${
                    !selectedCategory
                      ? 'bg-primary/10 text-primary'
                      : 'text-gray-700'
                  }`}
                >
                  All Categories
                </button>
                {categories.map(category => (
                  <button
                    key={category.categoryName}
                    onClick={() => handleCategorySelect(category.categoryName)}
                    className={`w-full p-3 text-left font-spartan text-sm hover:bg-gray-50 ${
                      selectedCategory === category.categoryName
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-700'
                    }`}
                  >
                    {category.categoryAlias}
                  </button>
                ))}
                {isLoadingCategories && (
                  <div className="p-3 text-sm text-gray-500">
                    Loading categories...
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Notifications Container */}
      <NotificationContainer
        notifications={notifications}
        onClose={dismiss}
        className="mb-4 w-full"
        maxNotifications={2}
      />

      {/* Apps Grid */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex h-full items-center justify-center py-24">
            <div className="flex flex-col items-center gap-3">
              <Spinner />
              <p className="text-sm text-gray-500">Loading available apps...</p>
            </div>
          </div>
        ) : filteredApps.length === 0 ? (
          <div className="flex h-full items-center justify-center py-20">
            <div className="text-center">
              <div className="mb-4 text-4xl">🔍</div>
              <h3 className="mb-2 text-lg font-medium text-blackOne">
                {searchQuery ? 'No apps found' : 'No apps available'}
              </h3>
              <p className="text-sm text-gray-500">
                {searchQuery
                  ? `No apps match "${searchQuery}". Try a different search term.`
                  : 'There are no available apps to display at the moment.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-x-4 gap-y-10 sm:grid-cols-2 lg:grid-cols-4">
            {filteredApps.map(app => (
              <AppCard
                key={app.key}
                app={app}
                onConnect={onConnectApp}
                connectionFlow={connectionFlow}
                onOpenTwilioModal={onOpenTwilioModal}
                onOpenSalesforceModal={onOpenSalesforceModal}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

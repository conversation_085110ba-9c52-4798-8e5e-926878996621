import { AnimatePresence, motion } from 'framer-motion';
import { Eye, EyeOff, X } from 'lucide-react';
import React, { useState } from 'react';

import { Salesforce_Agentous } from '@/assets/images';
import { Button, Input } from '@/components/ui';
import { FormField } from '@/types/businessStack';

import { Spinner } from '../common/Loader';

interface SalesforcePreAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: Record<string, string>) => void;
  preAuth: {
    title: string;
    submitButtonText: string;
    fields: FormField[];
  };
  isLoading: boolean;
  error?: string;
}

interface FormData {
  [key: string]: string;
}

interface ValidationErrors {
  [key: string]: string;
}

export const SalesforcePreAuthModal: React.FC<SalesforcePreAuthModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  preAuth,
  isLoading,
  error,
}) => {
  const [formData, setFormData] = useState<FormData>({});
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {}
  );
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});

  // Initialize form data and sort fields by order
  const sortedFields = preAuth.fields.sort(
    (a, b) => (a.order || 0) - (b.order || 0)
  );

  const validateForm = (): boolean => {
    const errors: ValidationErrors = {};
    let isValid = true;

    sortedFields.forEach(field => {
      const value = formData[field.key] || '';

      if (field.required && !value.trim()) {
        errors[field.key] = `${field.label} is required`;
        isValid = false;
      } else if (field.minLength && value.length < field.minLength) {
        errors[field.key] =
          `${field.label} must be at least ${field.minLength} characters`;
        isValid = false;
      } else if (field.maxLength && value.length > field.maxLength) {
        errors[field.key] =
          `${field.label} cannot exceed ${field.maxLength} characters`;
        isValid = false;
      }
    });

    setValidationErrors(errors);
    return isValid;
  };

  const handleInputChange = (fieldKey: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldKey]: value,
    }));

    // Clear validation error when user starts typing
    if (validationErrors[fieldKey]) {
      setValidationErrors(prev => ({
        ...prev,
        [fieldKey]: '',
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    onSubmit(formData);
  };

  const togglePasswordVisibility = (fieldKey: string) => {
    setShowPassword(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey],
    }));
  };

  const renderField = (field: FormField) => {
    const isPasswordField = field.type === 'PASSWORD';
    const fieldValue = formData[field.key] || '';
    const hasError = !!validationErrors[field.key];

    return (
      <div key={field.key} className="space-y-2 px-12">
        <Input
          label={`${field.label || field.key} *`}
          type={
            isPasswordField && !showPassword[field.key] ? 'password' : 'text'
          }
          placeholder={field.placeholder || `Enter ${field.label || field.key}`}
          value={fieldValue}
          onChange={e => handleInputChange(field.key, e.target.value)}
          error={validationErrors[field.key]}
          fullWidth
          endIcon={
            isPasswordField ? (
              <button
                type="button"
                onClick={() => togglePasswordVisibility(field.key)}
                className="text-gray-400 hover:text-gray-600"
              >
                {showPassword[field.key] ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            ) : undefined
          }
        />
        {field.description && !hasError && (
          <p className="text-sm text-gray-500">{field.description}</p>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-[9998] bg-black/50"
        onClick={onClose}
      />

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="w-full max-w-lg rounded-2xl bg-white py-8 shadow-xl"
          onClick={e => e.stopPropagation()}
        >
          {/* Header */}
          <div className="border-b border-gray-200 px-12 pb-4">
            <div className="flex items-center justify-end">
              <button
                onClick={onClose}
                className="-mr-4 rounded-lg p-1 text-gray-400 hover:bg-lightOrangeTwo hover:text-blackOne"
                aria-label="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="flex flex-col items-center gap-4">
              <img src={Salesforce_Agentous} alt="" />

              <h2 className="text-lg font-semibold text-blackOne">
                Connect Agentous to Salesforce
              </h2>
              <p className="font-medium">
                Create Agentous Internal Connected App to Salesforce
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="py-6">
            <form onSubmit={handleSubmit}>
              <div className="space-y-4 pb-8">
                {sortedFields.map(renderField)}

                {error && (
                  <div className="rounded-lg bg-red-50 p-3 text-sm text-red-600">
                    {error}
                  </div>
                )}
              </div>

              <div className="flex justify-center gap-3 border-t border-gray-200 pt-8">
                <Button
                  type="button"
                  variant="outline"
                  className="min-w-[120px] hover:border-grayNine"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="min-w-[120px] text-white"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Spinner />
                      <span>Connecting...</span>
                    </div>
                  ) : (
                    // preAuth.submitButtonText
                    'Connect to Salesforce'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

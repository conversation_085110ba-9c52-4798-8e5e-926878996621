import { yupResolver } from '@hookform/resolvers/yup';
import { AxiosError } from 'axios';
import {
  ArrowRight,
  EyeIcon,
  EyeOffIcon,
  HelpCircle,
  Lock,
  MoveHorizontal,
  X,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import * as yup from 'yup';

import { AgentousIcon } from '@/assets/images';
import salesforceLogo from '@/assets/images/salesforce.png';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { useNotifications } from '@/hooks/useNotifications';

import { useUnifiedConnectionApi } from '../../services/businessStackService';
import AnimatedModal from '../common/AnimatedModal';
import { Button } from '../ui';

// Yup schema for Salesforce form validation
const salesforceSchema = yup.object({
  instanceUrl: yup
    .string()
    .trim()
    .required('Instance URL is required')
    .matches(
      /^https?:\/\/[^\s]+$/,
      'Please enter a valid URL starting with http:// or https://'
    )
    .test(
      'is-salesforce-url',
      'Must be a valid Salesforce URL (must contain salesforce.com)',
      (url: string | undefined) => {
        if (!url) return false;
        const trimmedUrl = url.trim();
        return (
          trimmedUrl.includes('salesforce.com') && trimmedUrl.includes('.')
        );
      }
    ),
  consumerKey: yup
    .string()
    .trim()
    .required('Consumer Key is required')
    .matches(
      /^[A-Za-z0-9._-]+$/,
      'Consumer Key can only contain letters, numbers, dots, hyphens, and underscores'
    )
    .min(10, 'Consumer Key must be at least 10 characters'),
  clientSecret: yup
    .string()
    .trim()
    .required('Client Secret is required')
    .matches(
      /^[A-Za-z0-9]+$/,
      'Client Secret can only contain letters and numbers'
    )
    .min(10, 'Client Secret must be at least 10 characters'),
});

type SalesforceFormData = yup.InferType<typeof salesforceSchema>;

interface SalesforceConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnectionSuccess: () => void;
}

type SalesforceFlowStep =
  | 'credentials-input'
  | 'connecting'
  | 'success'
  | 'error';

interface SalesforceFlowState {
  step: SalesforceFlowStep;
  isLoading: boolean;
  error?: string;
}

export const SalesforceConnectionModal: React.FC<
  SalesforceConnectionModalProps
> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { setActiveAgent } = useTenant();
  const { notifyWithImage } = useNotifications();
  const [flowState, setFlowState] = useState<SalesforceFlowState>({
    step: 'credentials-input',
    isLoading: false,
  });

  const [showClientSecret, setShowClientSecret] = useState(false);

  const { getConnectionUrl } = useUnifiedConnectionApi();

  // Initialize React Hook Form with Yup validation
  const {
    register,
    handleSubmit,
    formState: { errors, isValid, isSubmitted, isDirty },
    reset,
  } = useForm<SalesforceFormData>({
    resolver: yupResolver(salesforceSchema),
    mode: 'onChange', // Changed from 'onSubmit' to 'onChange' for better UX
    defaultValues: {
      instanceUrl: '',
      consumerKey: '',
      clientSecret: '',
    },
  });

  // Reset form and state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setFlowState({
        step: 'credentials-input',
        isLoading: false,
      });
      setShowClientSecret(false);
    }
  }, [isOpen, reset]);

  // Navigate to help page with state to return with modal open
  const handleHelpClick = () => {
    navigate(ROUTES.DASHBOARD_BUSINESS_STACK_SALESFORCE_HELP, {
      state: { returnWithModal: 'salesforce' },
    });
  };

  // Activate Regis chat
  const handleAskRegisClick = () => {
    setActiveAgent('regis');
    onClose();
  };

  // Handle form submission - triggers OAuth flow
  const onSubmit = async (data: SalesforceFormData) => {
    try {
      setFlowState(prev => ({
        ...prev,
        step: 'connecting',
        isLoading: true,
        error: undefined,
      }));

      // Step 1: Get OAuth connection URL with the credentials
      const urlResponse = await getConnectionUrl({
        appKey: 'salesforce',
        params: {
          instanceUrl: data.instanceUrl.trim(),
          consumerKey: data.consumerKey.trim(),
          consumerSecret: data.clientSecret.trim(),
        },
      });

      if (!urlResponse.status) {
        throw new Error(urlResponse.message || 'Failed to get connection URL');
      }

      // Step 2: Validate the connection URL
      let connectionUrl = urlResponse.data?.data;
      if (!connectionUrl || !connectionUrl.startsWith('http')) {
        throw new Error('Invalid connection URL received from server');
      }

      // Step 3: Modify redirect_uri to include current page URL
      let currentUrl = window.location.href.split('?')[0];
      currentUrl = currentUrl.split('/business-stack')[0];
      const redirectUri = `${currentUrl}/business-stack/salesforce`;

      const urlObj = new URL(connectionUrl);
      urlObj.searchParams.set('redirect_uri', redirectUri);
      connectionUrl = urlObj.toString();

      // Step 4: Open authentication in new tab
      const authWindow = window.open(connectionUrl, '_blank');

      if (!authWindow) {
        throw new Error(
          'Unable to open authentication window. Please check your browser settings.'
        );
      }

      // Show success message and close modal
      notifyWithImage(
        <p>Opening Salesforce authentication page...</p>,
        salesforceLogo,
        'h-8 w-8 rounded object-contain',
        'info'
      );

      // Close modal - OAuth flow will continue in new window
      handleClose();
    } catch (error) {
      let errorMessage = 'An unexpected error occurred.';

      if (error instanceof AxiosError && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setFlowState(prev => ({
        ...prev,
        step: 'error',
        isLoading: false,
        error: errorMessage,
      }));
    }
  };

  // Reset modal state when closed
  const handleClose = () => {
    setFlowState({
      step: 'credentials-input',
      isLoading: false,
    });
    reset();
    setShowClientSecret(false);
    onClose();
  };

  // Determine if button should be disabled
  const isButtonDisabled =
    flowState.isLoading || !isDirty || (isSubmitted && !isValid);

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={onClose}
      showCloseButton={false}
      maxWidth="xl"
    >
      <div>
        <button
          onClick={handleClose}
          className="absolute right-4 top-4 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
        >
          <X className="h-5 w-5" />
        </button>

        <div className="flex flex-col">
          <div className="mt-10 flex flex-col gap-4 py-6">
            {/* Header with Logos */}
            <div className="flex items-center justify-center gap-4">
              <div className="flex h-[52px] w-[52px] items-center justify-center rounded-lg bg-primary p-2">
                <img
                  src={AgentousIcon}
                  alt="Agentous"
                  className="h-full w-full object-contain"
                />
              </div>
              <MoveHorizontal
                strokeWidth={1.5}
                className="h-5 w-5 text-[#2F2F2F]"
              />
              <div className="flex h-[52px] w-[67px] items-center justify-center rounded-lg bg-yellowOne">
                <img
                  src={salesforceLogo}
                  alt="Salesforce"
                  className="h-10 w-10 object-contain"
                />
              </div>
            </div>

            <h3 className="text-center text-xl font-semibold text-blackOne">
              Connect Agentous to Salesforce
            </h3>
            <div className="mx-auto flex w-full max-w-[448px] flex-col gap-1">
              {/* Help Link */}
              <div className="flex items-center justify-start gap-1">
                <span className="text-sm text-blackOne sm:text-base">
                  Create Agentous Internal Connected App to Salesforce
                </span>
                <button
                  onClick={handleHelpClick}
                  className="flex items-center sm:text-base"
                  type="button"
                >
                  <HelpCircle className="h-5 w-5 fill-primary text-white" />
                </button>
              </div>
              <button
                onClick={handleAskRegisClick}
                type="button"
                className="flex items-center text-start text-base font-normal capitalize text-primary"
              >
                <span className="underline">ask regis how </span>{' '}
                <ArrowRight className="h-4 w-4" />
              </button>
              <p className="text-start text-base font-semibold text-blackOne">
                Provide App Credentials Below
              </p>
            </div>
          </div>
          <div className="mb-4 border-b border-gray-200"></div>

          {/* Form */}
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="w-full space-y-4"
            autoComplete="off"
            noValidate
          >
            <div className="mx-auto flex w-full max-w-[448px] flex-col gap-4 py-5">
              {/* Instance URL */}
              <div>
                <label className="mb-2 block text-sm font-medium text-blackOne">
                  Your Salesforce Instance URL{' '}
                  <span className="text-red-500">(Required)</span>
                </label>
                <input
                  type="text"
                  {...register('instanceUrl')}
                  placeholder="Ieg, https://orgfarm-f825dfaede.my.salesforce.com"
                  autoComplete="new-password"
                  className={`w-full rounded-lg border px-4 py-3 text-sm focus:outline-none focus:ring-2 ${
                    errors.instanceUrl && isSubmitted
                      ? 'border-red-500 focus:border-red-500 focus:ring-red-200'
                      : 'border-gray-300 focus:border-primary focus:ring-primary/20'
                  }`}
                />
                {errors.instanceUrl && isSubmitted && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.instanceUrl.message}
                  </p>
                )}
              </div>

              {/* App Consumer Key */}
              <div>
                <label className="mb-2 block text-sm font-medium text-blackOne">
                  App Consumer Key{' '}
                  <span className="text-red-500">(Required)</span>
                </label>
                <input
                  type="text"
                  {...register('consumerKey')}
                  placeholder="Enter ID (e.g 3mvg9lkcponinvb.kmqdsf)"
                  autoComplete="new-password"
                  className={`w-full rounded-lg border px-4 py-3 text-sm focus:outline-none focus:ring-2 ${
                    errors.consumerKey && isSubmitted
                      ? 'border-red-500 focus:border-red-500 focus:ring-red-200'
                      : 'border-gray-300 focus:border-primary focus:ring-primary/20'
                  }`}
                />
                {errors.consumerKey && isSubmitted && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.consumerKey.message}
                  </p>
                )}
              </div>

              {/* App Client Secret Key */}
              <div>
                <label className="mb-2 block text-sm font-medium text-blackOne">
                  App Client Secret Key{' '}
                  <span className="text-red-500">(Required)</span>
                </label>
                <p className="text-grayNone mb-2 flex items-center gap-1 text-xs">
                  <Lock className="h-3 w-3" />
                  Stored securely, never shared, encrypted at rest.
                </p>
                <div className="relative">
                  <input
                    type={showClientSecret ? 'text' : 'password'}
                    {...register('clientSecret')}
                    placeholder="7EEA4076F086F7B743958A0642A2910B5451E994.."
                    autoComplete="new-password"
                    className={`w-full rounded-lg border px-4 py-3 pr-10 text-sm focus:outline-none focus:ring-2 ${
                      errors.clientSecret && isSubmitted
                        ? 'border-red-500 focus:border-red-500 focus:ring-red-200'
                        : 'border-gray-300 focus:border-primary focus:ring-primary/20'
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowClientSecret(!showClientSecret)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showClientSecret ? (
                      <EyeIcon className="h-5 w-5" />
                    ) : (
                      <EyeOffIcon className="h-5 w-5" />
                    )}
                  </button>
                </div>
                {errors.clientSecret && isSubmitted && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.clientSecret.message}
                  </p>
                )}
              </div>

              {/* Error Display */}
              {flowState.error && (
                <div className="rounded-lg bg-red-50 p-4 text-sm text-red-600">
                  <p className="font-medium">Connection Failed</p>
                  <p className="mt-1">{flowState.error}</p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="w-full items-center border-t">
              <div className="mx-auto flex w-full max-w-[448px] items-center gap-4 py-6">
                <Button
                  type="button"
                  onClick={handleClose}
                  className="h-11 w-full"
                  variant="outline"
                  disabled={flowState.isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isButtonDisabled}
                  className="h-11 w-full"
                >
                  {flowState.isLoading
                    ? 'Connecting...'
                    : 'Connect to Salesforce'}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </AnimatedModal>
  );
};

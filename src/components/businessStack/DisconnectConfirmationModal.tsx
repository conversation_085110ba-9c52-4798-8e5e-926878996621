import { <PERSON><PERSON>lert, X } from 'lucide-react';
import React, { useState } from 'react';

import { AppCardProps } from '../../types/businessStack';
import AnimatedModal from '../common/AnimatedModal';
import { Button } from '../ui';

interface DisconnectConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void> | void;
  app: AppCardProps['app'];
}

const DisconnectConfirmationModal: React.FC<
  DisconnectConfirmationModalProps
> = ({ isOpen, onClose, onConfirm, app }) => {
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  if (!isOpen) return null;

  const handleConfirm = async () => {
    try {
      setIsDisconnecting(true);
      await onConfirm();
      // Only close if successful
      onClose();
    } catch (error) {
      // Close on error as well
      onClose();
    } finally {
      setIsDisconnecting(false);
    }
  };

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={onClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <button
        onClick={onClose}
        className="absolute right-4 top-4 flex h-6 w-6 items-center justify-center"
      >
        <X className="h-4 w-4" />
      </button>
      <div className="relative">
        {/* Content */}
        <div className="mx-auto flex w-full max-w-[386px] flex-col items-start justify-center gap-2 py-10">
          {/* Header */}
          <h2 className="text-lg font-semibold text-gray-900">
            Disconnect {app.name} From Business Stack?
          </h2>
          {/* App Identification */}
          <div className="flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellowOne">
              <img
                src={app.logo}
                alt={`${app.name} logo`}
                className="h-8 w-8 object-contain"
                onError={e => {
                  const target = e.target as HTMLImageElement;
                  target.src =
                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjQgMjRIMzJWMzJIMjRWMjRaIiBmaWxsPSIjRDFEOUUwIi8+CjxwYXRoIGQ9Ik0zMiAyNEg0MFYzMkgzMlYyNFoiIGZpbGw9IiNEMUQ5RTAiLz4KPHBhdGggZD0iTTI0IDMySDMyVjQwSDI0VjMyWiIgZmlsbD0iI0QxRDlFMCIvPgo8cGF0aCBkPSJNMzIgMzJINDBWNDBIMzJWMzJaIiBmaWxsPSIjRDFEOUUwIi8+Cjwvc3ZnPgo=';
                }}
              />
            </div>
            <span className="text-lg font-medium text-blackOne">
              {app.name}
            </span>
          </div>

          {/* Warning Description */}
          <p className="text-justify text-sm text-blackOne">
            Disconnecting this system will remove its integration with your
            business stack. You may lose access to synced data, automations, and
            shared resources linked to this app.
          </p>

          {/* Warning Box */}
          <div className="flex items-center gap-3">
            <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center">
              <TriangleAlert className="h-6 w-6 text-white" fill="#FBA320" />
            </div>
            <p className="text-yellow-800 text-justify text-sm">
              This action cannot be undone without reconnecting the system.
            </p>
          </div>

          {/* Confirmation Question */}
          <p className="text-sm font-bold text-blackOne">
            Are you sure you want to disconnect {app.name}?
          </p>
        </div>
        {/* Action Buttons */}
        <div className="flex w-full items-center justify-center gap-3 border-t p-6">
          <Button
            type="button"
            onClick={onClose}
            variant="outline"
            className="h-11 w-full"
            disabled={isDisconnecting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleConfirm}
            className="h-11 w-full"
            disabled={isDisconnecting}
            isLoading={isDisconnecting}
          >
            {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
          </Button>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default DisconnectConfirmationModal;

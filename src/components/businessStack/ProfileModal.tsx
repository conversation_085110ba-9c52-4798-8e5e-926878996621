import { X } from 'lucide-react';
import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';

import AnimatedModal from '../common/AnimatedModal';

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentName?: string;
  agentAvatar?: string;
  agentEmail?: string;
  managerName?: string;
  managerEmail?: string;
  lastConnection?: string;
}

const ProfileModal: React.FC<ProfileModalProps> = ({
  isOpen,
  onClose,
  agentName = '',
  agentAvatar = '',
  agentEmail = '',
  managerName = '',
  managerEmail = '',
  lastConnection = '',
}) => {
  if (!isOpen) return null;

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={onClose}
      maxWidth="xl"
      showCloseButton={false}
    >
      {/* Header */}
      <div className="flex items-center justify-center gap-4 border-b p-4">
        <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-yellowOne">
          <Icons.Profile className="h-6 w-6" />
        </div>
        <h2 className="font-spartan text-lg font-semibold text-blackOne">
          Profile
        </h2>
        <button onClick={onClose} className="absolute right-4 top-4">
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* Profile Fields */}
      <div className="border-b py-8">
        <div className="mx-auto w-full max-w-[450px] space-y-4">
          {/* Agent Name */}
          <div className="flex w-full items-center gap-6">
            <label className="w-40 flex-shrink-0 text-base font-medium text-blackOne sm:text-lg">
              Agent Name
            </label>
            <div className="flex w-fit items-center gap-3 rounded-lg border border-grayTwentyEight bg-white p-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-peachTwo">
                {agentAvatar ? (
                  <img
                    src={agentAvatar}
                    alt={agentName}
                    className="h-6 w-6 rounded object-contain"
                  />
                ) : (
                  <Icons.User className="h-6 w-6" />
                )}
              </div>
              <span className="text-base font-normal text-blackOne">
                {agentName || 'N/A'}
              </span>
            </div>
          </div>

          {/* Agent Email */}
          <div className="flex w-full items-center gap-6">
            <label className="w-40 flex-shrink-0 text-base font-medium text-blackOne sm:text-lg">
              Agent Email
            </label>
            <div className="w-[225px] rounded-lg border border-grayTwentyEight bg-white p-3">
              <span className="block truncate text-base font-normal text-blackOne">
                {agentEmail || 'N/A'}
              </span>
            </div>
          </div>

          {/* Manager */}
          <div className="flex w-full items-center gap-6">
            <label className="w-40 flex-shrink-0 text-base font-medium text-blackOne sm:text-lg">
              Manager
            </label>
            <div className="w-[225px] rounded-lg border border-grayTwentyEight bg-white p-3">
              <span className="block truncate text-base font-normal text-blackOne">
                {managerName || 'N/A'}
              </span>
            </div>
          </div>

          {/* Manager Email */}
          <div className="flex w-full items-center gap-6">
            <label className="w-40 flex-shrink-0 text-base font-medium text-blackOne sm:text-lg">
              Manager Email
            </label>
            <div className="w-[225px] rounded-lg border border-grayTwentyEight bg-white p-3">
              <span className="block truncate text-base font-normal text-blackOne">
                {managerEmail || 'N/A'}
              </span>
            </div>
          </div>

          {/* Last Connection */}
          <div className="flex w-full items-center gap-6">
            <label className="w-40 flex-shrink-0 text-base font-medium text-blackOne sm:text-lg">
              Last Connection
            </label>
            <div className="w-[225px] rounded-lg border border-grayTwentyEight bg-white p-3">
              <span className="block truncate text-base font-normal text-blackOne">
                {lastConnection || 'Never connected'}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="py-8"></div>
    </AnimatedModal>
  );
};

export default ProfileModal;

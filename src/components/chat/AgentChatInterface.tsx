import { ArrowDown, User } from 'lucide-react';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';

import { useTimezone } from '@/context/TimezoneContext';

import { IndividualAgent } from '../../data/constants';
import { ChatSkeleton } from './ChatSkeleton';
import { TypingIndicator } from './TypingIndicator';

interface AgentMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  senderName: string;
  timestamp: Date;
}

interface AgentChatInterfaceProps {
  agent: IndividualAgent;
  state: {
    messages: AgentMessage[];
    isLoading: boolean;
  };
  ChatInputComponent: React.ComponentType;
  groupedMessages: Record<string, AgentMessage[]>;
}

const AgentMessageComponent = ({
  message,
  agent,
}: {
  message: AgentMessage;
  agent: IndividualAgent;
}) => {
  const { formatUserTimestamp } = useTimezone();
  const isUser = message.sender === 'user';

  return (
    <div className="mb-6 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-peachTwo">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-grayTwentySix">
            <img
              src={agent.image}
              alt={agent.name}
              className="h-full w-full rounded-full object-cover"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {formatUserTimestamp(message.timestamp, 'time')}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded-lg bg-gray-5 p-3 text-grayTwentyFour">
          {message.content}
        </div>
      </div>
    </div>
  );
};

export const AgentChatInterface = ({
  agent,
  state,
  ChatInputComponent,
  groupedMessages,
}: AgentChatInterfaceProps) => {
  const { formatUserTimestamp } = useTimezone();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  // Helper function to format date headers for chat
  const formatDateHeader = (dateKey: string): string => {
    const date = new Date(dateKey);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const isToday = date.toDateString() === today.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();

    if (isToday) return 'Today';
    if (isYesterday) return 'Yesterday';

    // For other dates, use our unified format
    return formatUserTimestamp(date, 'date');
  };

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  };

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;
    const handleScroll = () => {
      // If user is not at the bottom (within 40px), show arrow
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };
    container.addEventListener('scroll', handleScroll);
    // Initial check
    handleScroll();
    return () => container.removeEventListener('scroll', handleScroll);
  }, [state.messages.length]);

  // Set initial scroll position to bottom BEFORE first paint
  useLayoutEffect(() => {
    const container = messagesContainerRef.current;
    if (container && state.messages.length > 0) {
      // Immediately set scroll to bottom without animation
      container.scrollTop = container.scrollHeight;
    }
  }, [state.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    // Only auto-scroll if user is at the bottom AND this is not the initial load
    if (isUserAtBottom && state.messages.length > 0) {
      scrollToBottom();
    }
  }, [state.messages, isUserAtBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!state.isLoading && isUserAtBottom && state.messages.length > 0) {
      // Small delay to ensure DOM is updated after loading completes
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [state.isLoading, isUserAtBottom]);

  return (
    <div className="relative flex h-full flex-col">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-4"
        style={{ minHeight: 0 }} // Ensures flex-1 works properly
      >
        {/* Show skeleton loader when no messages and loading */}
        {state.messages.length === 0 && state.isLoading ? (
          <ChatSkeleton messageCount={3} />
        ) : (
          Object.entries(groupedMessages).map(([date, messages]) => (
            <div key={date}>
              <div className="mb-2 text-center text-xs font-semibold text-gray-400">
                {formatDateHeader(date)}
              </div>
              {messages.map(message => (
                <AgentMessageComponent
                  key={message.id}
                  message={message}
                  agent={agent}
                />
              ))}
            </div>
          ))
        )}

        {/* Typing Indicator - only show when messages exist and loading */}
        {state.messages.length > 0 && state.isLoading && (
          <TypingIndicator agentImageSrc={agent.image} agentName={agent.name} />
        )}
      </div>

      {/* Floating Down Arrow */}
      {showScrollToBottom && (
        <button
          className="absolute bottom-24 right-6 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-lg transition hover:bg-gray-50"
          onClick={scrollToBottom}
          aria-label="Scroll to latest message"
        >
          <ArrowDown className="h-6 w-6 text-primary" />
        </button>
      )}

      {/* Chat Input */}
      <div className="flex-shrink-0 px-4 py-4">
        <ChatInputComponent />
      </div>
    </div>
  );
};

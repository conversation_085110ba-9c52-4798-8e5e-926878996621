import { ArrowDown, User } from 'lucide-react';
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkBreaks from 'remark-breaks';
import remarkGfm from 'remark-gfm';

import { useTimezone } from '@/context/TimezoneContext';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import { scyra } from '../../assets/images';
import { useTenant } from '../../context/TenantContext';
import {
  ScyraChatInterfaceProps,
  ScyraMessage,
} from '../../types/businessStack';
import { UserBasicInfoPayload } from '../../types/user';
import { getAgentAvatar } from '../../utils/agentUtils';
import { TypingIndicator } from './TypingIndicator';

const ScyraMessageComponent = ({ message }: { message: ScyraMessage }) => {
  const isUser = message.sender === 'user';
  const { activeAgent } = useTenant();
  const { data: userData } = useGetUserProfile<UserBasicInfoPayload>();
  const { formatUserTimestamp } = useTimezone();

  const agentAvatar = getAgentAvatar(activeAgent, userData);

  const processedContent = React.useMemo(() => {
    const rawField = message as unknown as Record<string, unknown>;
    const raw = (
      typeof rawField.content === 'string'
        ? rawField.content
        : typeof rawField.message === 'string'
          ? rawField.message
          : ''
    ) as string;

    if (!raw) return '';

    let content = raw.replace(/\r\n/g, '\n').replace(/\\n/g, '\n');

    // Ensure lists are recognized by markdown parsers
    content = content.replace(/\n(\s*)(\d+\.\s)/g, '\n\n$2');
    content = content.replace(/\n(\s*)([-*+]\s)/g, '\n\n$2');

    // Auto-link URLs and emails
    content = content.replace(
      /(?<!\]\()(?<!\()(https?:\/\/[^\s)]+)(?!\))/g,
      '[$1]($1)'
    );
    content = content.replace(
      /(?<!\]\()\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b(?!\))/g,
      '[$1](mailto:$1)'
    );

    return content;
  }, [message]);

  return (
    <div className="mb-6 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-peachTwo">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-grayTwentySix">
            <img
              src={agentAvatar}
              alt={activeAgent ? `${activeAgent} Agent` : 'Agent'}
              className="h-full w-full rounded-full object-cover"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {formatUserTimestamp(message.timestamp, 'time')}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded-lg bg-gray-5 p-3 text-grayTwentyFour">
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkBreaks]}
            components={{
              // Customize markdown components to match existing styling
              p: ({ children, ...props }) => (
                <p
                  className="mb-2 whitespace-pre-wrap leading-relaxed last:mb-0"
                  {...props}
                >
                  {children}
                </p>
              ),
              h1: ({ children }) => (
                <h1 className="mb-2 text-lg font-bold">{children}</h1>
              ),
              h2: ({ children }) => (
                <h2 className="mb-2 text-base font-bold">{children}</h2>
              ),
              h3: ({ children }) => (
                <h3 className="mb-2 text-sm font-bold">{children}</h3>
              ),
              ul: ({ children }) => (
                <ul className="mb-2 ml-4 list-disc">{children}</ul>
              ),
              ol: ({ children }) => (
                <ol className="mb-2 ml-4 list-decimal">{children}</ol>
              ),
              li: ({ children }) => <li className="mb-2">{children}</li>,
              code: ({ children }) => (
                <code className="rounded bg-gray-200 px-1 py-0.5 font-mono text-sm">
                  {children}
                </code>
              ),
              pre: ({ children }) => (
                <pre className="mb-2 overflow-x-auto rounded bg-gray-200 p-2 font-mono text-sm">
                  {children}
                </pre>
              ),
              a: ({ children, href }) => (
                <a
                  href={href}
                  className="text-primary hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {children}
                </a>
              ),
              strong: ({ children }) => (
                <strong className="font-bold">{children}</strong>
              ),
              em: ({ children }) => <em className="italic">{children}</em>,
              // Custom text renderer to handle whitespace
              text: ({ children }) => {
                if (typeof children === 'string') {
                  // Remove excessive whitespace but preserve single spaces
                  return children.replace(/\s+/g, ' ');
                }
                return children;
              },
            }}
          >
            {processedContent}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

export const ScyraChatInterface = ({
  state,
  ChatInputComponent,
  groupedMessages,
}: ScyraChatInterfaceProps) => {
  const { formatUserTimestamp } = useTimezone();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  // Helper function to format date headers for chat
  const formatDateHeader = (dateKey: string): string => {
    const date = new Date(dateKey);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const isToday = date.toDateString() === today.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();

    if (isToday) return 'Today';
    if (isYesterday) return 'Yesterday';

    // For other dates, use our unified format
    return formatUserTimestamp(date, 'date');
  };

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;
    const handleScroll = () => {
      // If user is not at the bottom (within 40px), show arrow
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };
    container.addEventListener('scroll', handleScroll);
    // Initial check
    handleScroll();
    return () => container.removeEventListener('scroll', handleScroll);
  }, [state.messages.length]);

  // Set initial scroll position to bottom BEFORE first paint
  useLayoutEffect(() => {
    const container = messagesContainerRef.current;
    if (container && state.messages.length > 0) {
      // Immediately set scroll to bottom without animation
      container.scrollTop = container.scrollHeight;
    }
  }, [state.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  useEffect(() => {
    // Only auto-scroll if user is at the bottom AND this is not the initial load
    if (isUserAtBottom && state.messages.length > 0) {
      scrollToBottom();
    }
  }, [state.messages, isUserAtBottom]);

  // Handle loading state changes - only scroll if user is at bottom
  useEffect(() => {
    if (!state.isLoading && isUserAtBottom && state.messages.length > 0) {
      // Small delay to ensure DOM is updated after loading completes
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [state.isLoading, isUserAtBottom, state.messages.length]);

  return (
    <div className="relative flex h-full flex-col">
      {/* Messages Container */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-4"
        style={{ minHeight: 0 }} // Ensures flex-1 works properly
      >
        {Object.entries(groupedMessages || {}).map(([date, messages]) => (
          <div key={date}>
            <div className="mb-2 text-center text-xs font-semibold text-gray-400">
              {formatDateHeader(date)}
            </div>
            {messages.map(message => (
              <ScyraMessageComponent key={message.id} message={message} />
            ))}
          </div>
        ))}

        {/* Typing Indicator */}
        {state.isLoading && (
          <TypingIndicator agentImageSrc={scyra} agentName="Scyra" />
        )}
      </div>

      {/* Floating Down Arrow */}
      {showScrollToBottom && (
        <button
          className="absolute bottom-24 right-6 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-lg transition hover:bg-gray-50"
          onClick={scrollToBottom}
          aria-label="Scroll to latest message"
        >
          <ArrowDown className="h-6 w-6 text-primary" />
        </button>
      )}

      {/* Chat Input */}
      <div className="flex-shrink-0 px-4 py-4">
        <ChatInputComponent />
      </div>
    </div>
  );
};

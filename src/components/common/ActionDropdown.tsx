import classNames from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import {
  Download,
  Edit,
  Eye,
  FileText,
  MoreVertical,
  Trash2,
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

interface ActionDropdownProps {
  actions?: Array<{
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
    variant?: 'default' | 'danger' | 'success' | 'info';
    isDisabled?: boolean;
    isLoading?: boolean;
    closeOnClick?: boolean | undefined;
  }>;
  customTriggerIcon?: React.ReactNode;
}

const ActionDropdown: React.FC<ActionDropdownProps> = ({
  actions,
  customTriggerIcon,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const defaultActions = [
    {
      label: 'View',
      icon: <Eye className="h-4 w-4" />,
      onClick: () => console.log('View'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Replace',
      icon: <Download className="h-4 w-4" />,
      onClick: () => console.log('Replace'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Edit',
      icon: <Edit className="h-4 w-4" />,
      onClick: () => console.log('Edit'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Delete',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: () => console.log('Delete'),
      variant: 'danger' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
    {
      label: 'Audit Log',
      icon: <FileText className="h-4 w-4" />,
      onClick: () => console.log('Audit Log'),
      variant: 'default' as const,
      isDisabled: false,
      isLoading: false,
      closeOnClick: true,
    },
  ];

  const menuActions = actions || defaultActions;

  // Update position when opened
  const updatePosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY + 4,
        left: rect.right + window.scrollX - 192, // 192px = w-48
      });
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      updatePosition();
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, [isOpen]);

  const dropdownPortal = isOpen ? (
    <AnimatePresence>
      <motion.div
        ref={dropdownRef}
        initial={{ opacity: 0, scale: 0.95, y: -10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: -10 }}
        transition={{ duration: 0.1 }}
        className="fixed z-[999999] w-48 rounded-xl border border-gray-200 bg-white shadow-[0px_5px_40px_0px_#0000001A]"
        style={{
          top: position.top,
          left: position.left,
        }}
      >
        <div className="divide-y divide-gray-200">
          {menuActions.map((action, index) => {
            const closeOnClick =
              action.closeOnClick === undefined ? true : action.closeOnClick;
            return (
              <button
                key={index}
                onClick={() => {
                  action.onClick();
                  closeOnClick && setIsOpen(false);
                }}
                disabled={action.isDisabled}
                className={classNames(
                  'rounded-0 flex w-full items-center p-3 text-sm transition-colors duration-150 first:rounded-t-xl last:rounded-b-xl',
                  {
                    'text-red-600 hover:bg-red-50': action.variant === 'danger',
                    'bg-greenFade text-greenOne': action.variant === 'success',
                    'text-blue-600 hover:bg-blue-50': action.variant === 'info',
                    'text-subText hover:bg-[#FFECE3]':
                      action.variant === 'default',
                    'cursor-not-allowed opacity-50': action.isDisabled,
                  }
                )}
              >
                {action.icon}
                <span className="ml-2">{action.label}</span>
              </button>
            );
          })}
        </div>
      </motion.div>
    </AnimatePresence>
  ) : null;

  return (
    <>
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="rounded-xl transition-colors duration-150"
      >
        {customTriggerIcon || (
          <MoreVertical className="h-5 w-5 text-grayTen" strokeWidth={3} />
        )}
      </button>

      {typeof document !== 'undefined' &&
        createPortal(dropdownPortal, document.body)}
    </>
  );
};

export default ActionDropdown;

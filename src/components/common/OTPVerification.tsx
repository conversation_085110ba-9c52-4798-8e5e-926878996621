/* eslint-disable unicorn/filename-case */
import { Loader } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Button } from '../ui';

interface OTPVerificationProps {
  title: string;
  description?: string;
  isLoading: boolean;
  error: string | null;
  success?: string | null;
  onSubmit: (code: string) => void;
  onResend: () => void;
  onClose?: () => void;
  setError: (error: string | null) => void;
  isResendLoading?: boolean;
}

export const OTPVerification: React.FC<OTPVerificationProps> = ({
  title,
  description,
  isLoading,
  error,
  success,
  onSubmit,
  onResend,
  setError,
  isResendLoading = false,
}) => {
  const [verificationCode, setVerificationCode] = useState([
    '',
    '',
    '',
    '',
    '',
    '',
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resendState, setResendState] = useState<'idle' | 'loading' | 'sent'>(
    'idle'
  );

  // Clear error when user starts typing
  useEffect(() => {
    if (error && verificationCode.some(code => code !== '')) {
      setError(null);
    }
  }, [verificationCode, error, setError]);

  // Reset submitting state when loading changes
  useEffect(() => {
    if (!isLoading) {
      setIsSubmitting(false);
    }
  }, [isLoading]);

  // Handle resend loading state
  useEffect(() => {
    if (isResendLoading && resendState === 'idle') {
      setResendState('loading');
    } else if (!isResendLoading && resendState === 'loading') {
      setResendState('sent');
      // Reset to idle after 3 seconds
      const timeout = setTimeout(() => {
        setResendState('idle');
      }, 3000);
      return () => clearTimeout(timeout);
    }
  }, [isResendLoading, resendState]);

  const handleVerificationCodeChange = (index: number, value: string) => {
    // Handle pasting full OTP (6 digits)
    if (value.length === 6 && /^\d+$/.test(value)) {
      const digits = value.split('');
      const newCodes = [...verificationCode];

      // Fill all 6 inputs with the pasted digits
      for (let i = 0; i < 6; i++) {
        newCodes[i] = digits[i] || '';
      }

      setVerificationCode(newCodes);

      // Auto-submit if all 6 digits are filled
      if (!isSubmitting && !isLoading) {
        setIsSubmitting(true);
        setTimeout(() => {
          onSubmit(newCodes.join(''));
        }, 100);
      }

      return;
    }

    // Handle pasting partial OTP (less than 6 digits)
    if (value.length > 1 && /^\d+$/.test(value) && value.length < 6) {
      const digits = value.split('');
      const newCodes = [...verificationCode];

      // Fill codes starting from current index
      for (let i = 0; i < digits.length && index + i < 6; i++) {
        newCodes[index + i] = digits[i];
      }

      setVerificationCode(newCodes);

      // Auto-focus the next empty input after pasting
      const nextEmptyIndex = newCodes.findIndex(
        (code, idx) => idx > index && code === ''
      );
      if (nextEmptyIndex !== -1) {
        setTimeout(() => {
          const nextInput = document.getElementById(`code-${nextEmptyIndex}`);
          nextInput?.focus();
        }, 50);
      }

      return;
    }

    // Handle single digit input
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCodes = [...verificationCode];
      newCodes[index] = value;
      setVerificationCode(newCodes);

      // Auto-focus next input if current input has a value
      if (value && index < 5) {
        setTimeout(() => {
          const nextInput = document.getElementById(`code-${index + 1}`);
          nextInput?.focus();
        }, 50);
      }

      // Auto-submit if all 6 digits are filled
      if (
        newCodes.every(code => code !== '') &&
        newCodes.join('').length === 6 &&
        !isSubmitting &&
        !isLoading
      ) {
        setIsSubmitting(true);
        setTimeout(() => {
          onSubmit(newCodes.join(''));
        }, 100);
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');

    // Only handle if pasted data is numeric and 6 digits
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split('');
      const newCodes = [...verificationCode];

      // Fill all 6 inputs with the pasted digits
      for (let i = 0; i < 6; i++) {
        newCodes[i] = digits[i];
      }

      setVerificationCode(newCodes);

      // Auto-submit after pasting
      if (!isSubmitting && !isLoading) {
        setIsSubmitting(true);
        setTimeout(() => {
          onSubmit(newCodes.join(''));
        }, 100);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    // Handle backspace
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const prevInput = document.getElementById(`code-${index - 1}`);
      prevInput?.focus();
    }

    // Handle arrow keys
    if (e.key === 'ArrowLeft' && index > 0) {
      const prevInput = document.getElementById(`code-${index - 1}`);
      prevInput?.focus();
    }

    if (e.key === 'ArrowRight' && index < 5) {
      const nextInput = document.getElementById(`code-${index + 1}`);
      nextInput?.focus();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const code = verificationCode.join('');
    if (code.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }
    if (!isSubmitting && !isLoading) {
      setIsSubmitting(true);
      onSubmit(code);
    }
  };

  const handleResendClick = async () => {
    if (resendState !== 'idle') return;

    setResendState('loading');
    try {
      await onResend();
    } catch (error) {
      setResendState('idle');
      // Error handling is done in parent components
    }
  };

  const getResendButtonContent = () => {
    switch (resendState) {
      case 'loading':
        return (
          <span className="flex items-center gap-1">
            <span className="flex">
              <span className="animate-pulse">.</span>
              <span
                className="animate-pulse"
                style={{ animationDelay: '0.2s' }}
              >
                .
              </span>
              <span
                className="animate-pulse"
                style={{ animationDelay: '0.4s' }}
              >
                .
              </span>
            </span>
            <span>Resending</span>
          </span>
        );
      case 'sent':
        return 'Sent';
      case 'idle':
      default:
        return 'Resend Code';
    }
  };

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="w-full max-w-md space-y-6 rounded-xl bg-white p-4 sm:p-8">
        <div className="text-center">
          <h1 className="text-lg font-medium text-blackOne sm:text-2xl sm:font-semibold">
            {title}
          </h1>
        </div>

        <div className="space-y-4">
          {description && (
            <p className="text-center text-sm text-subText">{description}</p>
          )}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="mb-4 block text-start text-sm font-normal text-subText sm:text-base">
                Enter a 6-digit code to sent to your email to verify your
                identity
              </label>
              <div className="flex justify-start gap-4">
                {verificationCode.map((code, index) => (
                  <input
                    key={index}
                    id={`code-${index}`}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={6}
                    value={code}
                    onChange={e =>
                      handleVerificationCodeChange(index, e.target.value)
                    }
                    onPaste={handlePaste}
                    onKeyDown={e => handleKeyDown(e, index)}
                    className="h-10 w-10 rounded-md border border-[#DFEAF2] text-center text-lg font-medium focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary disabled:cursor-not-allowed disabled:opacity-50 sm:h-12 sm:w-12"
                    disabled={isLoading || isSubmitting || success !== null}
                    autoComplete="one-time-code"
                  />
                ))}
              </div>
            </div>

            <div className="text-start text-sm text-subText sm:text-base">
              Didn't get a message?{' '}
              <button
                type="button"
                onClick={handleResendClick}
                disabled={isLoading || resendState !== 'idle'}
                className={`font-normal transition-colors disabled:opacity-50 ${
                  resendState === 'sent'
                    ? 'text-green-600'
                    : resendState === 'loading'
                      ? 'text-orange-600'
                      : 'text-primary hover:underline'
                }`}
              >
                {getResendButtonContent()}
              </button>
            </div>

            <Button
              type="submit"
              disabled={
                isLoading ||
                isSubmitting ||
                verificationCode.some(code => !code) ||
                success !== null
              }
              className="h-12 w-full rounded-lg border border-primary bg-transparent px-4 py-2 font-medium text-primary hover:bg-primary hover:text-white disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isLoading || isSubmitting ? (
                <div className="flex items-center justify-center gap-4 hover:text-white">
                  <Loader className="h-4 w-4 animate-spin" />{' '}
                  <span>Processing...</span>
                </div>
              ) : (
                'Proceed'
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

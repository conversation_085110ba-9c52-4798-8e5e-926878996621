import { X } from 'lucide-react';
import React, { useState } from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';

import { Button } from '../ui';
import AnimatedModal from './AnimatedModal';

interface DeclineReasonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  requesterName?: string;
}

const DeclineReasonModal: React.FC<DeclineReasonModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  requesterName,
}) => {
  const [reason, setReason] = useState('');

  const handleConfirm = () => {
    if (reason.trim()) {
      onConfirm(reason.trim());
    }
  };

  const handleClose = () => {
    setReason(''); // Reset on close
    onClose();
  };

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={handleClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col items-center gap-6">
        <Button
          variant="ghost"
          className="absolute right-4 top-4"
          onClick={handleClose}
        >
          <X className="h-5 w-5" />
        </Button>

        <div className="mt-16 flex w-full flex-col items-center justify-center gap-8 sm:max-w-[420px]">
          {/* Icon */}
          <Icons.CardRejected className="h-8 w-8 text-danger" />

          {/* Input */}
          <div className="flex w-full flex-col gap-4">
            <label className="text-start text-sm font-medium text-blackOne sm:text-base">
              {requesterName
                ? `Provide reason for declining ${requesterName}'s request`
                : 'Provide reason for declining this request'}
            </label>
            <textarea
              placeholder="Enter your reason for declining this request..."
              value={reason}
              onChange={e => setReason(e.target.value)}
              rows={4}
              className="w-full resize-none rounded-xl border border-gray-300 px-4 py-3 text-base text-blackOne placeholder:text-gray-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
              autoFocus
            />
          </div>
        </div>

        {/* Buttons */}
        <div className="flex w-full items-center justify-center space-x-4 border-t p-4">
          <Button
            onClick={handleClose}
            variant="outline"
            className="h-12 w-full rounded-xl"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!reason.trim()}
            className="h-12 w-full rounded-xl"
          >
            Decline Request
          </Button>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default DeclineReasonModal;

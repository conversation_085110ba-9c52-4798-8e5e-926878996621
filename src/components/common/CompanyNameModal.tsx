import { X } from 'lucide-react';
import React, { useState } from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';

import { Button } from '../ui';
import AnimatedModal from './AnimatedModal';

interface CompanyNameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceed: (companyName: string) => void;
  suiteName?: string;
}

const CompanyNameModal: React.FC<CompanyNameModalProps> = ({
  isOpen,
  onClose,
  onProceed,
}) => {
  const [companyName, setCompanyName] = useState('');

  const handleProceed = () => {
    if (companyName.trim()) {
      onProceed(companyName.trim());
      setCompanyName(''); // Reset after proceeding
    }
  };

  const handleClose = () => {
    setCompanyName(''); // Reset on close
    onClose();
  };

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={handleClose}
      maxWidth="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col items-center gap-6 p-0">
        <Button
          variant="ghost"
          className="absolute right-4 top-4"
          onClick={handleClose}
        >
          <X className="h-5 w-5" />
        </Button>

        <div className="mt-16 flex w-full max-w-[370px] flex-col items-center justify-center gap-8">
          {/* Icon */}
          <div className="flex h-[48px] w-[48px] items-center justify-center rounded-lg bg-yellowOne">
            <Icons.Building className="h-8 w-8 text-warning" />
          </div>

          {/* Input */}
          <div className="flex w-full flex-col gap-5">
            <label className="text-start text-lg font-medium text-blackOne">
              Provide Company Name
            </label>
            <input
              type="text"
              placeholder="e.g., ABC, Inc."
              value={companyName}
              onChange={e => setCompanyName(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter' && companyName.trim()) {
                  handleProceed();
                }
              }}
              className="w-full rounded-lg border border-gray-300 px-4 py-3 text-base text-blackOne placeholder:text-gray-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
              autoFocus
            />
          </div>
        </div>

        {/* Buttons */}
        <div className="flex w-full items-center justify-center space-x-4 border-t p-6">
          <Button
            onClick={handleClose}
            variant="outline"
            className="h-12 w-full"
          >
            Cancel
          </Button>
          <Button
            onClick={handleProceed}
            disabled={!companyName.trim()}
            className="h-12 w-full"
          >
            Proceed
          </Button>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default CompanyNameModal;

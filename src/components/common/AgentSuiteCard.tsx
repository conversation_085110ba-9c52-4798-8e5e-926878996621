import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import { AIAgentSuite } from '@/types/agents';

export interface AgentSuiteCardProps {
  suite: AIAgentSuite;
  link: string;
  onAgentSuiteClick?: () => void;
  isSuiteClaimed: boolean;
  showClaimButton?: boolean;
}

export const AgentSuiteCard: React.FC<AgentSuiteCardProps> = ({
  suite,
  link,
  onAgentSuiteClick,
  isSuiteClaimed,
  showClaimButton = true,
}) => (
  <Link
    to={onAgentSuiteClick ? '#' : link}
    state={suite}
    className="group block"
    onClick={onAgentSuiteClick}
  >
    <div className="h-full max-w-[290px] overflow-hidden rounded-xl border border-gray-200 bg-white transition-shadow hover:border-primary sm:rounded-2xl">
      {/* Card Image */}
      <img
        src={suite.avatar}
        className="h-[120px] w-full object-cover md:h-[200px]"
        alt={suite.agentSuiteName}
        onError={e => {
          // Fallback to mock logo if agent avatar fails to load
          (e.target as HTMLImageElement).src = mockAgentsSuite.filter(
            agent =>
              agent.id.toLowerCase() === suite.agentSuiteKey.toLowerCase()
          )[0].image;
        }}
      />

      {/* Card Content */}
      <div className="flex flex-col gap-3 p-4 font-inter text-blackOne">
        <div className="flex w-fit items-center justify-center rounded border border-grayNine bg-grayNineTeen px-2 py-[2px]">
          <span className="text-sm font-semibold">{suite?.agentSuiteName}</span>
        </div>
        <p className="whitespace-pre-line text-[13px] font-semibold leading-[18px] sm:text-base sm:leading-[22px]">
          {suite?.description}
        </p>
        <p className="whitespace-pre-line text-[13px] leading-[18px] text-subText sm:text-sm sm:leading-[21px]">
          {suite?.roleDescription}
        </p>
        {showClaimButton &&
          (isSuiteClaimed ? null : (
            <Button
              className="h-10 w-fit font-spartan font-normal capitalize text-white"
              onClick={onAgentSuiteClick}
            >
              claim suite
            </Button>
          ))}
      </div>
    </div>
  </Link>
);

import clsx from 'clsx';
import React, { useRef } from 'react';

import { AgentSuiteCard } from '@/components/common/AgentSuiteCard';
import DashboardWithChatLayout from '@/components/layout/DashboardWithChatLayout';
import { useTenant } from '@/context/TenantContext';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { AIAgentSuite } from '@/types/agents';

import { Icons } from '../../assets/icons/DashboardIcons';
import { useAuth } from '../../context/AuthContext';
import { useGetAIAgentSuites } from '../../hooks/useAgents';
import AgentSuiteSkeletonLoader from '../ui/AgentSuiteSkeleton';

interface AgentSelectionLayoutProps {
  title: string;
  description: string;
  bgImage: string;
  pageType: 'knowledge-base' | 'business-stack' | 'dashboard';
  onAgentSuiteClick: (suite: AIAgentSuite) => void;
  className?: string;
}

const AgentSelectionLayout: React.FC<AgentSelectionLayoutProps> = ({
  title,
  description,
  bgImage,
  pageType,
  onAgentSuiteClick,
  className,
}) => {
  const { claimedSuites, isUserLoading: isLoadingUserProfile } = useAuth();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const { data: agentSuites = [], isLoading, error } = useGetAIAgentSuites();
  const { activeAgent, setActiveAgent } = useTenant();
  const isMobile = useMediaQuery('(max-width: 768px)');
  // Handle agents suite card click
  const handleAgentSuiteClick = (suite: AIAgentSuite) => {
    if (activeAgent === 'regis') {
      setActiveAgent(suite.availableAgents[0]?.agentKey);
    }
    onAgentSuiteClick(suite);
  };

  return (
    <DashboardWithChatLayout reloadChatHistoryRef={reloadChatHistoryRef}>
      <div
        className={clsx(
          'flex flex-1 flex-col gap-6 overflow-y-auto p-4 sm:p-8'
        )}
      >
        <div className="flex flex-col gap-4 text-start">
          <div className="mb-2 flex items-center gap-2">
            {pageType === 'knowledge-base' && (
              <Icons.Knowledge className="h-5 w-5 text-primary sm:h-6 sm:w-6" />
            )}
            {pageType === 'business-stack' && (
              <Icons.Stack className="h-5 w-5 text-primary sm:h-6 sm:w-6" />
            )}
            {pageType === 'dashboard' && (
              <Icons.Dashboard className="h-5 w-5 text-primary sm:h-6 sm:w-6" />
            )}
            <h1 className="text-lg font-semibold text-blackOne sm:text-2xl">
              {pageType === 'knowledge-base'
                ? 'Knowledge Base'
                : pageType === 'business-stack'
                  ? 'Business Stack'
                  : 'Dashboard'}
            </h1>
          </div>

          {/* Hero Section */}
          <div
            className={clsx(
              'flex h-fit max-w-4xl items-center overflow-hidden rounded-xl bg-cover bg-center text-white sm:h-[140px] sm:rounded-2xl',
              className
            )}
            style={{
              backgroundImage: `url(${pageType === 'dashboard' ? '' : bgImage})`,
            }}
          >
            <div className="flex w-full items-center justify-between">
              <div className="p-4 text-left sm:p-6">
                <h2 className="mb-2 text-sm font-bold text-white xs:text-base sm:text-lg">
                  {title}
                </h2>
                <p className="text-sm text-white">{description}</p>
              </div>
            </div>
            <div className="relative h-full w-[178px] p-2 sm:mr-8 sm:p-0">
              <img
                src={bgImage}
                alt="bg"
                className="h-full w-full"
                style={isMobile ? { objectFit: 'contain' } : {}}
              />
            </div>
          </div>
        </div>

        {/* Activate an agent to begin section */}
        <h3 className="text-base font-medium text-blackOne sm:text-lg">
          {pageType === 'knowledge-base'
            ? 'Select Suite -> Upload Knowledge Base'
            : pageType === 'business-stack'
              ? 'Select Suite -> Connect Business Systems'
              : 'Select Suite -> View Dashboard'}
        </h3>
        {/* Error State */}
        {error && (
          <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 text-red-700">
            <p>Error loading agent suites: {error.message}</p>
          </div>
        )}

        {/* Content */}
        <div className="w-fit">
          {isLoading ? (
            <AgentSuiteSkeletonLoader count={4} />
          ) : (
            <div className="grid grid-cols-2 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-2">
              {agentSuites.map(suite => {
                const isMatchingSuite = claimedSuites?.some(
                  claimedSuite =>
                    claimedSuite.suite.agentSuiteKey === suite.agentSuiteKey
                );
                return (
                  <AgentSuiteCard
                    key={suite.agentSuiteKey}
                    suite={suite}
                    link={'#'}
                    onAgentSuiteClick={() => handleAgentSuiteClick(suite)}
                    isSuiteClaimed={isMatchingSuite || false}
                    showClaimButton={!isLoadingUserProfile}
                  />
                );
              })}
            </div>
          )}
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};

export default AgentSelectionLayout;

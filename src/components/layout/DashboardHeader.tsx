'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { Menu, Search, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { chatIcon2 } from '@/assets/icons';
import { Button } from '@/components/ui';
import { useTenant } from '@/context/TenantContext';

import { ROUTES } from '../../constants/routes';
import Logo from '../ui/Logo';
import { UserDropdown } from '../ui/UserDropdown';

interface DashboardHeaderProps {
  onToggleMobileSidebar: () => void;
}

export function DashboardHeader({
  onToggleMobileSidebar,
}: DashboardHeaderProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { setActiveAgent } = useTenant();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const searchInputRef = useRef<HTMLInputElement>(null);

  const isSettingsPage = location.pathname.includes('/settings');

  useEffect(() => {
    if (isSearchOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearchOpen]);

  const handleCloseSettings = () => {
    navigate(ROUTES.DASHBOARD_AI_AGENTS);
  };

  const toggleSearch = () => {
    if (isSearchOpen && searchQuery.trim()) {
      // TODO: Implement search functionality
      console.log('Searching for:', searchQuery);
    }
    setIsSearchOpen(!isSearchOpen);
    if (isSearchOpen) {
      setSearchQuery('');
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // TODO: Implement search functionality
      console.log('Searching for:', searchQuery);
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleChatWithRegis = () => {
    setActiveAgent('regis');
    navigate(ROUTES.DASHBOARD_AI_AGENTS);
  };

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="border-[#E6E6E6] bg-[#FFFDF9]/80 px-4 py-3 backdrop-blur-sm sm:border-b"
    >
      <div className="flex w-full items-center justify-between gap-4">
        {/* Left Section */}
        <div className="flex flex-shrink-0 items-center gap-2 md:w-[300px]">
          <button
            type="button"
            onClick={onToggleMobileSidebar}
            className="flex items-center justify-center border-0 text-black transition-colors hover:bg-primary/10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary lg:hidden"
            aria-label="Open navigation menu"
          >
            <Menu className="h-5 w-5" strokeWidth={2.5} />
          </button>
          <Logo />
        </div>

        {/* Right Section */}
        <div className="flex w-full items-center justify-between gap-4">
          {isSettingsPage ? (
            <h1 className="hidden text-2xl font-medium text-subText lg:block">
              Settings
            </h1>
          ) : (
            <span />
          )}
          <div className="flex w-full items-center justify-end gap-4">
            {isSettingsPage ? (
              // Settings mode - show close button
              <button
                onClick={handleCloseSettings}
                className="flex h-8 w-8 items-center justify-center rounded-full bg-primary transition-colors hover:bg-red-600 md:h-[48px] md:w-[48px]"
                title="Close settings"
              >
                <X
                  className="h-4 w-4 text-white sm:h-6 sm:w-6"
                  strokeWidth={3}
                />
              </button>
            ) : (
              // Normal mode - show regular buttons and user dropdown
              <>
                {/* Search Section */}
                <div className="relative hidden items-center">
                  {/* <div className="flex relative items-center"> */}
                  <AnimatePresence>
                    {isSearchOpen && (
                      <motion.div
                        initial={{ width: 0, opacity: 0 }}
                        animate={{ width: 300, opacity: 1 }}
                        exit={{ width: 0, opacity: 0 }}
                        transition={{
                          duration: 0.3,
                          ease: [0.4, 0, 0.2, 1],
                        }}
                        className="absolute right-12 top-0 overflow-hidden"
                      >
                        <form
                          onSubmit={handleSearchSubmit}
                          className="relative"
                        >
                          <motion.input
                            ref={searchInputRef}
                            type="text"
                            value={searchQuery}
                            onChange={e => setSearchQuery(e.target.value)}
                            onKeyDown={handleSearchKeyDown}
                            placeholder="Search..."
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.1 }}
                            className="h-[44px] w-full rounded-lg border border-gray-300 bg-white px-4 pr-10 text-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
                          />
                          <button
                            type="submit"
                            className="absolute right-2 top-1/2 flex h-6 w-6 -translate-y-1/2 items-center justify-center rounded-lg text-gray-400 hover:text-primary"
                          >
                            <Search className="h-4 w-4" />
                          </button>
                        </form>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <motion.button
                    onClick={toggleSearch}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="relative z-10 flex h-[44px] items-center justify-center rounded-xl p-3 transition-colors hover:bg-gray-100"
                  >
                    <motion.div
                      animate={isSearchOpen ? { rotate: 180 } : { rotate: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      {isSearchOpen ? (
                        <X className="h-5 w-5" />
                      ) : (
                        <Search className="h-5 w-5" />
                      )}
                    </motion.div>
                  </motion.button>
                </div>

                <Button
                  className="flex h-9 w-10 items-center justify-center gap-2 rounded-lg bg-primary p-0 text-white transition-colors hover:bg-orange-15 sm:h-[44px] sm:w-fit sm:px-4 sm:py-2.5"
                  onClick={handleChatWithRegis}
                >
                  <img src={chatIcon2} alt="" className="h-6 w-6" />
                  <span className="mt-0.5 hidden font-spartan md:block">
                    Chat with Regis
                  </span>
                </Button>

                <Button className="hidden h-[44px] rounded-lg border border-primary bg-white p-4 text-primary">
                  Upgrade
                </Button>
                <UserDropdown />
              </>
            )}
          </div>
        </div>
      </div>
    </motion.header>
  );
}

import { AnimatePresence, motion } from 'framer-motion';
import React, { useEffect, useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';

import { useMediaQuery } from '@/hooks/useMediaQuery';

import { DismissibleBanner } from '../common/DismissibleBanner';
import DashboardSidebar from '../sidebar/DashboardSidebar';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { DashboardHeader } from './DashboardHeader';

const DashboardShell: React.FC = () => {
  const location = useLocation();
  const isSettingsPage = location.pathname.includes('/settings');
  const isDesktop = useMediaQuery('(min-width: 1024px)');

  // Initialize sidebar state from session storage or default based on page type
  const [isCollapsed, setIsCollapsed] = useState<boolean>(() => {
    const savedState = sessionStorage.getItem('pivotl-sidebar-collapsed');
    if (savedState !== null) {
      return JSON.parse(savedState);
    }
    return !isSettingsPage;
  });
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Save sidebar state to session storage whenever it changes
  useEffect(() => {
    sessionStorage.setItem(
      'pivotl-sidebar-collapsed',
      JSON.stringify(isCollapsed)
    );
  }, [isCollapsed]);

  // Update sidebar state when navigating between settings and non-settings pages
  useEffect(() => {
    const savedState = sessionStorage.getItem('pivotl-sidebar-collapsed');
    if (savedState === null) {
      setIsCollapsed(!isSettingsPage);
    }
    setIsMobileSidebarOpen(false);
  }, [isSettingsPage]);

  useEffect(() => {
    if (isDesktop) {
      setIsMobileSidebarOpen(false);
    }
  }, [isDesktop]);

  useEffect(() => {
    setIsMobileSidebarOpen(false);
  }, [location.pathname]);

  return (
    <ErrorBoundary>
      <div className="flex h-screen flex-col font-inter">
        <DismissibleBanner
          id="professional-trial"
          title="Welcome to your Professional trial!"
          message={
            <span className=" text-[#1FC16B">
              You have 13 days to try PivotL's{' '}
              <a
                href="#"
                className="font-normal text-primary underline hover:no-underline"
              >
                paid features
              </a>
              . Upgrade anytime for as low as $19.99 USD/month.
            </span>
          }
          variant="success"
          hide={true}
        />

        {/* Header */}
        <DashboardHeader
          onToggleMobileSidebar={() => {
            if (!isDesktop) {
              setIsMobileSidebarOpen(true);
            }
          }}
        />

        {/* Main Content Area */}
        <div className="flex flex-1 flex-row overflow-hidden">
          <div className="hidden h-full lg:flex">
            <DashboardSidebar
              isCollapsed={isCollapsed}
              toggleSidebar={() => setIsCollapsed(!isCollapsed)}
            />
          </div>
          {/* Page Content */}
          <main className={`flex-1 overflow-y-auto bg-[#FAFAFA]`}>
            <Outlet />
          </main>
        </div>

        <AnimatePresence>
          {isMobileSidebarOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 lg:hidden"
            >
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.4 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-black"
                onClick={() => setIsMobileSidebarOpen(false)}
              />
              <motion.div
                initial={{ x: '-100%' }}
                animate={{ x: 0 }}
                exit={{ x: '-100%' }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className="absolute left-0 top-0 h-full w-[80vw] max-w-sm border-r border-[#E6E6E6] bg-white shadow-2xl"
              >
                <DashboardSidebar
                  isCollapsed={false}
                  toggleSidebar={() => setIsMobileSidebarOpen(false)}
                  variant="mobile"
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </ErrorBoundary>
  );
};

export default DashboardShell;

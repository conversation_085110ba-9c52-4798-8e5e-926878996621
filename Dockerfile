# Stage 1: Build the React app
FROM node:22-alpine

# Create app directory
WORKDIR /app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
COPY package*.json ./

RUN npm install --production

# Set the NODE_ENV environment variable
ARG NODE_ENV=production
ENV VITE_NODE_ENV=${NODE_ENV}
ENV VITE_REST_API_URL={{rest_api_url}}
ENV VITE_KEYCLOAK_URL={{keycloak_url}}
ENV VITE_CLIENT_BASE_URL={{client_base_url}}
ENV VITE_GOOGLE_SITE_KEY={{google_site_key}}

# Bundle app source
COPY . .

RUN npm run build

EXPOSE 3000

# Install serve globally (if not already installed)
RUN npm install -g serve

# Start the server
CMD ["npm", "run", "serve"]